import { ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import { debounce } from 'lodash';
import { useEffect, useState, type PropsWithChildren } from 'react';
import themeConfig from './themeConfig';

const Config = ({ children }: PropsWithChildren) => {
  const [config, setConfig] = useState(themeConfig());

  const resetConfig = () => {
    setConfig(themeConfig());
  };

  useEffect(() => {
    window.addEventListener('resize', debounce(resetConfig, 200));

    return () => {
      window.removeEventListener('resize', resetConfig);
    };
  }, []);
  return (
    <ConfigProvider locale={zhCN} theme={config}>
      {children}
    </ConfigProvider>
  );
};
export default Config;
