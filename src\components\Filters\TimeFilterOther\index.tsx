/**
 * 此组件区别于标准时间控件，可以选择更早的年份
 */
import { CalendarOutlined } from '@ant-design/icons';
import { Button, Cascader, Select } from 'antd';
import type { ReactNode } from 'react';
import { useState } from 'react';
import styles from './index.module.less';
import { allYears, getDays, hours, months, source } from './utils';
export interface Value {
  rangeStartYear?: number;
  rangeEndYear?: number;
  rangeStartMonth?: number;
  rangeEndMonth?: number;
  rangeStartDay?: number;
  rangeEndDay?: number;
  startTime?: any[];
  endTime?: any[];
  year?: number;
  month?: number;
  day?: number;
  hour?: number;
}
export interface Props {
  value: Value;
  sections?: [boolean, boolean, boolean, boolean];
  footer?: ReactNode;
  onChange: (value: Value) => void;
  onSubmit?: (type: 'reset' | 'ok') => void;
}

const Component = ({
  value,
  sections = [true, true, true, true],
  footer,
  onChange,
  onSubmit,
}: Props) => {
  const [rangeMonths, setRangeMonths] = useState<any[]>([]);
  const [rangeStartDays, setRangeStartDays] = useState<any[]>([]);
  const [rangeEndDays, setRangeEndDays] = useState<any[]>([]);
  const [singleMonths, setSingleMonths] = useState<any[]>([]);
  const [singleDays, setSingleDays] = useState<any[]>([]);
  const [singleHours, setSingleHours] = useState<any[]>([]);

  return (
    <div className={styles.timeFilter}>
      {sections[0] && (
        <div className={styles.item}>
          <div className={styles.line}>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择年份"
              options={allYears}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeStartYear}
              onChange={(rangeStartYear) => {
                const next: Value = {
                  rangeStartYear,
                  rangeEndYear: value.rangeEndYear,
                };
                if (!next.rangeEndYear || rangeStartYear > next.rangeEndYear) {
                  next.rangeEndYear = rangeStartYear;
                }
                onChange(next);
              }}
            />
            <span>到</span>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择年份"
              options={allYears}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeEndYear}
              onChange={(rangeEndYear) => {
                const next: Value = {
                  rangeStartYear: value.rangeStartYear,
                  rangeEndYear,
                };
                if (!next.rangeStartYear || rangeEndYear < next.rangeStartYear) {
                  next.rangeStartYear = rangeEndYear;
                }
                onChange(next);
              }}
            />
          </div>
          <div className={styles.line}>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择月份"
              options={rangeMonths}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeStartMonth}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setRangeMonths(value.rangeStartYear ? months : []);
                }
              }}
              onChange={(rangeStartMonth) => {
                const next: Value = {
                  rangeStartYear: value.rangeStartYear,
                  rangeEndYear: value.rangeEndYear,
                  rangeStartMonth,
                  rangeEndMonth: value.rangeEndMonth,
                };
                if (next.rangeStartYear === next.rangeEndYear) {
                  if (!next.rangeEndMonth || rangeStartMonth > next.rangeEndMonth) {
                    next.rangeEndMonth = rangeStartMonth;
                  }
                } else {
                  if (!next.rangeEndMonth) {
                    next.rangeEndMonth = 1;
                  }
                }
                onChange(next);
              }}
            />
            <span>到</span>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择月份"
              options={rangeMonths}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeEndMonth}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setRangeMonths(value.rangeEndYear ? months : []);
                }
              }}
              onChange={(rangeEndMonth) => {
                const next: Value = {
                  rangeStartYear: value.rangeStartYear,
                  rangeEndYear: value.rangeEndYear,
                  rangeStartMonth: value.rangeStartMonth,
                  rangeEndMonth,
                };
                if (next.rangeStartYear === next.rangeEndYear) {
                  if (!next.rangeStartMonth || rangeEndMonth < next.rangeStartMonth) {
                    next.rangeStartMonth = rangeEndMonth;
                  }
                } else {
                  if (!next.rangeStartMonth) {
                    next.rangeStartMonth = 1;
                  }
                }
                onChange(next);
              }}
            />
          </div>
          <div className={styles.line}>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择日期"
              options={rangeStartDays}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeStartDay}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setRangeStartDays(
                    value.rangeStartYear && value.rangeStartMonth
                      ? getDays(value.rangeStartYear, value.rangeStartMonth)
                      : [],
                  );
                }
              }}
              onChange={(rangeStartDay) => {
                const next: Value = {
                  rangeStartYear: value.rangeStartYear,
                  rangeEndYear: value.rangeEndYear,
                  rangeStartMonth: value.rangeStartMonth,
                  rangeEndMonth: value.rangeEndMonth,
                  rangeStartDay,
                  rangeEndDay: value.rangeEndDay,
                };
                if (
                  next.rangeStartYear === next.rangeEndYear &&
                  next.rangeStartMonth === next.rangeEndMonth
                ) {
                  if (!next.rangeEndDay || rangeStartDay > next.rangeEndDay) {
                    next.rangeEndDay = rangeStartDay;
                  }
                } else {
                  if (!next.rangeEndDay) {
                    next.rangeEndDay = 1;
                  }
                }
                onChange(next);
              }}
            />
            <span>到</span>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择日期"
              options={rangeEndDays}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeEndDay}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setRangeEndDays(
                    value.rangeEndYear && value.rangeEndMonth
                      ? getDays(value.rangeEndYear, value.rangeEndMonth)
                      : [],
                  );
                }
              }}
              onChange={(rangeEndDay) => {
                const next: Value = {
                  rangeStartYear: value.rangeStartYear,
                  rangeEndYear: value.rangeEndYear,
                  rangeStartMonth: value.rangeStartMonth,
                  rangeEndMonth: value.rangeEndMonth,
                  rangeStartDay: value.rangeStartDay,
                  rangeEndDay,
                };
                if (
                  next.rangeStartYear === next.rangeEndYear &&
                  next.rangeStartMonth === next.rangeEndMonth
                ) {
                  if (!next.rangeStartDay || rangeEndDay < next.rangeStartDay) {
                    next.rangeStartDay = rangeEndDay;
                  }
                } else {
                  if (!next.rangeStartDay) {
                    next.rangeStartDay = 1;
                  }
                }
                onChange(next);
              }}
            />
          </div>
          <div className={styles.describe}>
            <p>查询范围：</p>
            <p>
              从哪年哪月之间添加的文件，或从哪年哪月到哪年哪月之间添加的文件，或从哪年哪月哪日到哪年哪月哪日添加的文件。
            </p>
          </div>
        </div>
      )}
      {sections[1] && (
        <div className={styles.item}>
          <div className={styles.line}>
            <Cascader
              popupClassName={styles.cascaderPopup}
              placeholder="选择时间"
              options={source}
              suffixIcon={<CalendarOutlined />}
              value={[value.endTime as any]}
              onChange={(endTime) => {
                const next: Value = {
                  endTime,
                };
                onChange(next);
              }}
            />
            <span>之前添加</span>
          </div>
          <div className={styles.describe}>
            <p>查询范围：</p>
            <p>从哪年之前添加的文件，或从哪年哪月添加的文件，或从哪年哪月哪日前添加的文件。</p>
          </div>
        </div>
      )}
      {sections[2] && (
        <div className={styles.item}>
          <div className={styles.line}>
            <Cascader
              popupClassName={styles.cascaderPopup}
              placeholder="选择时间"
              options={source}
              suffixIcon={<CalendarOutlined />}
              value={[value.startTime as any]}
              onChange={(startTime) => {
                const next: Value = {
                  startTime,
                };
                onChange(next);
              }}
            />
            <span>之后添加</span>
          </div>
          <div className={styles.describe}>
            <p>查询范围：</p>
            <p>从哪年之后添加的文件，或从哪年哪月添加的文件，或从哪年哪月哪日后添加的文件。</p>
          </div>
        </div>
      )}
      {sections[3] && (
        <div className={styles.item}>
          <div className={styles.line}>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="按年查"
              options={allYears}
              suffixIcon={<CalendarOutlined />}
              value={value.year}
              onChange={(year) => {
                const next: Value = {
                  year,
                };
                onChange(next);
              }}
            />
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="按月查"
              options={singleMonths}
              suffixIcon={<CalendarOutlined />}
              value={value.month}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setSingleMonths(value.year ? months : []);
                }
              }}
              onChange={(month) => {
                const next: Value = {
                  year: value.year,
                  month,
                };
                onChange(next);
              }}
            />
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="按日查"
              options={singleDays}
              suffixIcon={<CalendarOutlined />}
              value={value.day}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setSingleDays(value.year && value.month ? getDays(value.year, value.month) : []);
                }
              }}
              onChange={(day) => {
                const next: Value = {
                  year: value.year,
                  month: value.month,
                  day,
                };
                onChange(next);
              }}
            />
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="按时查"
              options={singleHours}
              suffixIcon={<CalendarOutlined />}
              value={value.hour}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setSingleHours(value.year && value.month && value.day ? hours : []);
                }
              }}
              onChange={(hour) => {
                const next: Value = {
                  year: value.year,
                  month: value.month,
                  day: value.day,
                  hour,
                };
                onChange(next);
              }}
            />
          </div>
        </div>
      )}
      <div className={styles.footer}>
        {!!footer && footer}
        {!footer && (
          <>
            <Button
              type="link"
              onClick={() => {
                if (onSubmit) {
                  onSubmit('reset');
                }
              }}
            >
              重置
            </Button>
            <Button
              type="primary"
              onClick={() => {
                if (onSubmit) {
                  onSubmit('ok');
                }
              }}
            >
              确定
            </Button>
          </>
        )}
      </div>
    </div>
  );
};

export default Component;
