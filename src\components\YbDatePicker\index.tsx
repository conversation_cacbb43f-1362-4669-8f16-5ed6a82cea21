/**
 * @description 时间选择组件
 * <AUTHOR>
 * @date 2024-08-12
 */
import { Input, Popover } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';
import YbDatePanel from './YbDatePanel';
import styles from './index.module.less';
const YbDatePicker = forwardRef((props: any, ref) => {
  const { width, dateValue, setDateValue } = props;
  const [showPanel, setShowPanel] = useState(false);

  const onSelect = (date: string) => {
    setDateValue(date);
    setShowPanel(false);
  };
  const onClose = () => {
    setDateValue('');
    setShowPanel(false);
  };
  useImperativeHandle(ref, () => ({
    onClose,
  }));
  return (
    <div style={{ width, display: 'inline-block' }}>
      <Popover
        trigger="click"
        overlayClassName={styles.YbDatePickerPop}
        content={<YbDatePanel onSelect={onSelect} onClose={onClose} />}
        placement="bottomLeft"
        arrow={false}
        open={showPanel}
        onOpenChange={(newOpen: boolean) => {
          setShowPanel(newOpen);
        }}
      >
        <Input value={dateValue} readOnly placeholder="请选择时间" />
      </Popover>
    </div>
  );
});
export default YbDatePicker;
