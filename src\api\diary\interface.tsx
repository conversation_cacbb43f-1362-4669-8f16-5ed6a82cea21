export interface queryPageParamsType {
  pageNo: number;
  pageSize: number;
  userNames?: any[];
  realNames?: any[];
  titles?: any[];
  contents?: string;
  startTimes?: any[];
  endTimes?: any[];
  deletedEnum?: any;
}

export interface addDiaryParamsType {
  diaryDate: string;
  city: string;
  weather: string;
  temperature: string;
  title?: string;
  content: string;
}

export interface updateDiaryParamsType {
  id: string;
  title?: string;
  content: string;
}

export interface shareDiaryParamsType {
  sharedUserIds: string[];
  sharedGroupIds: string[];
  shareDiaryIds: string[];
}

export interface delDiaryParamsType {
  deletedSource: number;
  deletedType: number;
  deletedAll: boolean;
  ids?: any[];
  startTime?: string;
  endTime?: string;
}
