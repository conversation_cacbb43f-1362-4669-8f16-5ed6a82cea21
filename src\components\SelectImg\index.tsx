import { getAlbumPage } from '@/api/magicAlbum';
import { AlbumListItem } from '@/components/AlbumList/interface';
import ImageCrop from '@/components/ImageCrop/ImageCrop';
import { imgInfoType } from '@/pages/MagicAlbum/album.interface';
import useUserStore from '@/store/useUserStore';
import { getImg, getWidth } from '@/utils/common';
import { Modal } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import styles from './index.module.less';
import SelectImgDetail from './SelectImgDetail';
import SelectImgList from './SelectImgList';

interface ModalProps {
  multiple: boolean; // 是否多选
  onAdd: (list: Array<imgInfoType>, isShare?: boolean) => void; //数据回传方法
  selectTag: number; // 0-不限制选什么 1-横图 2-竖图 3-正方形
  title?: string; // 弹窗title
  openSource?: string; // 从哪里打开，不用关闭弹窗
  isShowInsertBtn?: boolean; //是否显示【从已删照片插入】按钮
  deleteCache?: any;
  ImageCropAspect?: {
    width: number;
    height: number;
    ratio: number;
  }; // 图片裁剪宽高比
}

const SelectImg = forwardRef((props: ModalProps, ref) => {
  const {
    multiple,
    selectTag,
    onAdd,
    title,
    openSource,
    isShowInsertBtn,
    deleteCache,
    ImageCropAspect,
  } = props;
  const [show, setShow] = useState(false);
  const [showSelect, setShowSelect] = useState(false);
  const [renderAlbumList, setRenderAlbumList] = useState(Array<AlbumListItem>);
  const [pager, setPager] = useState({
    total: 0,
    pageSize: 6,
    pageNo: 1,
  });
  const [albumsId, setAlbumsId] = useState('');
  const [isShare, setIsShare] = useState(false);
  const [albumsTemplateId, setAlbumsTemplateId] = useState(Number);
  const [albumsCategorySingle, setAlbumsCategorySingle] = useState(1);
  setAlbumsCategorySingle;
  const [imgSrc, setImgSrc] = useState(''); // 截图组件数据源
  const cropRef = useRef<any>(null);
  const onShow = () => {
    setShow(true);
  };

  const onClose = () => {
    setImgSrc('');
    setShowSelect(false);
    setShow(false);
    setPager({ total: 0, pageSize: 6, pageNo: 1 });
  };

  const showSelectImg = (
    albumsId: any,
    albumsTemplateId: any,
    senderUserId: any,
    albumsCategory: any,
  ) => {
    if (albumsTemplateId >= 10000) {
      return;
    }
    setShowSelect(true);
    setAlbumsId(albumsId);
    setIsShare(Boolean(senderUserId));
    setAlbumsTemplateId(albumsTemplateId);
    setAlbumsCategorySingle(albumsCategory);
  };
  const closeSelectImg = () => {
    setShowSelect(false);
  };
  useImperativeHandle(ref, () => ({
    onShow,
    onClose,
  }));
  const getData = () => {
    const { userInfo } = useUserStore.getState();
    getAlbumPage({ ...pager, userId: userInfo?.id }).then((res: any) => {
      setRenderAlbumList(res.data.list);
      setPager({
        pageNo: pager.pageNo,
        pageSize: pager.pageSize,
        total: res.data.total,
      });
    });
  };
  const prevPage = () => {
    setPager(Object.assign({ ...pager }, { pageNo: pager.pageNo - 1 }));
  };
  const nextPage = () => {
    setPager(Object.assign({ ...pager }, { pageNo: pager.pageNo + 1 }));
  };
  // 选择截取后的头像
  const hadSelectImg = (list: any, isShare: boolean) => {
    if (openSource === 'group') {
      if (list?.length) {
        const path = isShare ? list[0].hashPath : list[0].visitPath;
        setImgSrc(getImg(path));
      }
    } else {
      onAdd(list);
    }
  };
  useEffect(() => {
    getData();
  }, [pager.pageNo]);

  useEffect(() => {
    return () => {
      closeSelectImg();
    };
  }, []);

  return (
    <Modal
      title={title ?? '选择照片'}
      open={show}
      footer={null}
      width={getWidth(1120)}
      centered
      maskClosable={false}
      zIndex={1200}
      className="selectImgModal"
      wrapClassName={styles.SelectImgModal}
      onCancel={onClose}
      closeIcon={<span>关闭</span>}
    >
      {imgSrc ? (
        <ImageCrop
          ref={cropRef}
          aspect={ImageCropAspect}
          src={imgSrc}
          onAdd={onAdd}
          closeModal={onClose}
        />
      ) : (
        <>
          {showSelect ? (
            <SelectImgDetail
              closeSelectImg={closeSelectImg}
              closeModal={onClose}
              albumsId={albumsId}
              albumsTemplateId={albumsTemplateId}
              albumsCategorySingle={albumsCategorySingle}
              isShare={isShare}
              multiple={multiple}
              selectTag={selectTag}
              onAdd={hadSelectImg}
              openSource={openSource}
              isShowInsertBtn={isShowInsertBtn}
              deleteCache={deleteCache}
            />
          ) : (
            <SelectImgList
              renderList={renderAlbumList}
              showSelectImg={showSelectImg}
              pager={pager}
              prevPage={prevPage}
              nextPage={nextPage}
            />
          )}
        </>
      )}
    </Modal>
  );
});
export default SelectImg;
