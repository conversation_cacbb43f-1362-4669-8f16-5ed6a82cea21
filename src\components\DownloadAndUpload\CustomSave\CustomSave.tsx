import { useEffect, useState } from 'react';
import FileChoose from '../FileChoose/FileChoose';
import ReclassifySave from '../ReclassifySave/ReclassifySave';
import styles from './CustomSave.module.less';
interface ModalProps {
  onClose?: () => void; // 关闭弹框回调方法
}
const CustomDownload = (ModalProps: ModalProps) => {
  const [isShowFileChoose, setIsShowFileChoose] = useState<boolean>(false);
  const [Data, setData] = useState<any>([]);
  useEffect(() => {}, []);
  return (
    <>
      <div className={styles.CustomDownload}>
        <ReclassifySave
          data={Data}
          onCloseModal={ModalProps.onClose}
          showFileChoose={() => {
            setIsShowFileChoose(true);
          }}
        />
        {isShowFileChoose ? (
          <FileChoose
            setData={setData}
            isOpen={isShowFileChoose}
            onClose={() => {
              setIsShowFileChoose(false);
            }}
          />
        ) : null}
      </div>
    </>
  );
};

export default CustomDownload;
