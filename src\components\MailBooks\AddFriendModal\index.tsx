import MultiModal from '@/components/MultiModal';
import AddFriend from '@/pages/addressBook/components/addFriend';
import { getWidth } from '@/utils/common';

interface AddFriendProps {
  open: boolean;
  onCancel: () => void;
  success?: () => void;
}
/**
 * 添加通讯录好友模态框
 */
const AddFriendModal = ({ open, onCancel, success }: AddFriendProps) => {
  const curType = 'addFriend';

  return (
    <MultiModal
      title=""
      open={open}
      footer={null}
      closable={false}
      layoutClassName="modalRight"
      width={getWidth(380)}
      mask={true}
      top={180}
      destroyOnClose={true}
    >
      <AddFriend cb={onCancel} curType={curType} success={success} isListOpen showType="small" />
    </MultiModal>
  );
};
export default AddFriendModal;
