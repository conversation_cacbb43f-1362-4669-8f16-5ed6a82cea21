import { getWidth } from '@/utils/common';
import { Button, Flex, Space, Table } from 'antd';
import { useContext, useMemo, useRef } from 'react';
import FilePreview, { FilePreviewAPI } from '../FliePreview';
import { getColumns } from './columns';
import Context from './Context';
import styles from './index.module.less';

const Component = () => {
  const { useCtxStore, columnFields } = useContext(Context);
  const [selectedList, setSelectedList] = useCtxStore!((state) => [
    state.selectedList,
    state.setSelectedList,
  ]);
  const columns = useMemo(() => {
    return getColumns(false, columnFields).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.render = (value: any, row: any) => {
          return (
            <Space>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  preview(row);
                }}
              >
                浏览
              </Button>
              <Button
                ghost
                type="primary"
                size="small"
                onClick={() => {
                  setSelectedList((value: any) => value.filter((item: any) => item.id !== row.id));
                }}
              >
                取消
              </Button>
            </Space>
          );
        };
      }
      return item;
    });
  }, [selectedList]);
  const filePreviewRef = useRef<FilePreviewAPI>();
  const preview = (row: any) => {
    filePreviewRef.current?.open({ ...row });
  };
  return (
    <Flex vertical className={styles.selectedFilesList}>
      <div className={styles.title}>已选文件({selectedList.length})</div>
      <Table
        size="small"
        className="select-table"
        columns={columns}
        dataSource={selectedList}
        rowKey={'id'}
        {...(selectedList.length > 4 ? { scroll: { y: getWidth(160) } } : {})}
        pagination={false}
      />
      <FilePreview ref={filePreviewRef} />
    </Flex>
  );
};

export default Component;
