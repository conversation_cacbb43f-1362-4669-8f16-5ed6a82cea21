import { MailChatAttachVOList } from '@/api/mail/chat/mailModels';
import FliePreview, { FilePreviewAPI } from '@/components/FliePreview';
import useUserStore from '@/store/useUserStore';
import { message, Tooltip } from 'antd';
import { FC, useEffect, useRef, useState } from 'react';
import styles from './Collapse.module.less';
interface CollapseProps {
  items: MailChatAttachVOList[];
  expand?: boolean;
  safeLevel?: number;
  type?: string;
  sendType?: string;
  revokeFlag?: number;
}

const CollapseFiles: FC<CollapseProps> = ({
  items = [],
  safeLevel = 0,
  expand = false,
  type = 'other',
  sendType = 'send',
  revokeFlag = 0,
}) => {
  const filePreviewRef = useRef<FilePreviewAPI>();
  const [expanded, setExpanded] = useState<boolean>(false);
  const userInfo = useUserStore((state) => state.userInfo);

  useEffect(() => {
    setExpanded(expand);
  }, [expand]);

  const preview = (index: number, e: any) => {
    e.stopPropagation();
    if (sendType === 'from' && revokeFlag === 1) {
      return message.info('该邮件已撤回，无法查看附件');
    }
    filePreviewRef.current?.open({
      id: items[index].attachmentId || '302470223',
      title: items[index].attachmentName,
      fileSize: items[index].attachmentSize,
      visitPath: type === 'edit' ? (items[index].visitPath as string) : items[index].attachmentUrl,
      fileType: items[index].fileType || items[index].attachmentName.split('.')[1],
      fileFormatType: items[index].fileFormatType,
      safeLevel: sendType == 'send' ? 0 : safeLevel,
      filePath: items[index].filePath || items[index].attachmentUrl,
      userId: userInfo?.id || '',
      source: 0,
    });
  };

  const rederItems = () => {
    if (items.length <= 1) {
      return items.map((item, index) => (
        <div key={index} className={styles.conDiv}>
          <Tooltip title={item.attachmentName}>
            <span
              onClick={(e: any) => preview(index, e)}
              className={`${styles.hoverSpan} ${styles.voSpan}`}
            >
              附件{index + 1}：{item.attachmentName}
            </span>
          </Tooltip>
          {index < items.length - 1 ? <br /> : null}
        </div>
      ));
    }
    if (expanded) {
      return items.map((item, index) => (
        <div key={index} className={styles.conDiv}>
          <Tooltip title={item.attachmentName}>
            <span
              onClick={(e: any) => preview(index, e)}
              className={`${styles.hoverSpan} ${styles.voSpan}`}
            >
              附件{index + 1}：{item.attachmentName}
            </span>
          </Tooltip>
          {index < items.length - 1 ? <br /> : renderButtons()}
        </div>
      ));
    }
    return (
      <div key={0} className={styles.conDiv}>
        <Tooltip title={items[0].attachmentName}>
          <span
            onClick={(e: any) => preview(0, e)}
            className={`${styles.hoverSpan} ${styles.voSpan}`}
          >
            附件1：{items[0].attachmentName}
          </span>
        </Tooltip>
        {renderButtons()}
      </div>
    );
  };

  const toggleExpanded = (e: any) => {
    setExpanded(!expanded);
    e.stopPropagation();
  };

  const renderButtons = () => {
    return (
      <>
        {expanded ? (
          <span className={styles.cosSpan} onClick={toggleExpanded}>
            折叠
          </span>
        ) : (
          <span className={styles.cosSpan} onClick={toggleExpanded}>
            展开{items.length - 1}
          </span>
        )}
      </>
    );
  };

  return (
    <>
      {rederItems()}
      <FliePreview ref={filePreviewRef} zIndex={1000} />
    </>
  );
};

export default CollapseFiles;
