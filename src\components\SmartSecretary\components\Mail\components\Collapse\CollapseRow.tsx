import { FC, useState } from 'react';
import styles from './Collapse.module.less';
interface CollapseProps {
  items: string[];
}

const CollapseRow: FC<CollapseProps> = ({ items = [] }) => {
  const [expanded, setExpanded] = useState<boolean>(false);

  const rederItems = () => {
    if (items.length <= 5) {
      return items.map((item, index) => (
        <span key={index}>
          {/* {index > 0 && '、'} */}
          <span style={index === 0 ? { color: 'var(--yb-primary-color)' } : {}}>{item}</span>
          {index < items.length - 1 ? '、' : null}
        </span>
      ));
    }
    if (expanded) {
      return items.map((item, index) => (
        <span key={index}>
          <span style={index === 0 ? { color: 'var(--yb-primary-color)' } : {}}>{item}</span>
          {index < items.length - 1 ? '、' : renderButtons()}
        </span>
      ));
    }
    return items.slice(0, 5).map((item, index) => (
      <span key={index}>
        <span style={index === 0 ? { color: 'var(--yb-primary-color)' } : {}}>{item}</span>
        {index < 4 ? '、' : renderButtons()}
      </span>
    ));
  };

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  const renderButtons = () => {
    return (
      <>
        {expanded ? (
          <span className={styles.cosSpan} onClick={toggleExpanded}>
            折叠
          </span>
        ) : (
          <span className={styles.cosSpan} onClick={toggleExpanded}>
            展开{items.length - 5}
          </span>
        )}
      </>
    );
  };

  return <>{rederItems()}</>;
};

export default CollapseRow;
