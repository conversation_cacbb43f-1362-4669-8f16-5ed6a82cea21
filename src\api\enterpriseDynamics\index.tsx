import api, { Extra } from '../index';

export const examPaperCreate = () => {
  return api.post({
    url: '/web-api/exam/paper/create',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
};

export const examPaperAnswerSubmit = (data: any) => {
  return api.post({
    url: '/web-api/exam/paper/answer/submit',
    data,
  });
};

export const getExamQuestionsByBank = () => {
  return api.get({
    url: '/web-api/exam/question/bank/getExamQuestionsByBank',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
};

export const examMockList = (data: any) => {
  return api.post({
    url: '/web-api/exam/paper/mockPager/page',
    data,
  });
};

export const examPaperViewScore = (params: any) => {
  return api.get({
    url: '/web-api/exam/paper/viewScore',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    params,
  });
};

export const examFormalList = (data: any) => { 
  return api.post({
    url: '/web-api/exam/test/formalTest/page',
    data,
  });  
};

export const examFormalPagerList = (data: any) => { 
  return api.post({
    url: '/web-api/exam/paper/formalPager/page',
    data,
  });  
};

export const createQuestion = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/exam/question/create',
      data,
    },
    extra,
  );
};

export const questionBankStats = () => {
  return api.get({
    url: '/web-api/exam/question/bank/stats',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });
};

export const getQuestionById = (id: any) => {
  return api.get({
    url: `/web-api/exam/question/get?id=${id}`,
  });
};

export const updateQuestion = (data: any, extra?: Extra) => {
  return api.put(
    {
      url: '/web-api/exam/question/update',
      data,
    },
    extra,
  );
};

export const deleteQuestion = (data: any, extra?: Extra) => {
  return api.delete(
    {
      url: '/web-api/exam/question/delete',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      data,
    },
    extra,
  );
};

export const updateQuestionBank = (data: any, extra?: Extra) => {
  return api.put(
    {
      url: '/web-api/exam/question/bank/update',
      data,
    },
    extra,
  );
};

export const getQuestionBankDescription = (id: any) => {
  return api.get({
    url: '/web-api/exam/question/bank/get',
  });
};

export const paperConfirmScore = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/exam/paper/confirmScore',
      data,
    },
    extra,
  );
};

export const publicExam = (data: any) => {
  return api.post({
    url: '/web-api/exam/test/create',
    data,
  });
};

export const selectTestPage = (data: any) => {
  return api.post({
    url: '/web-api/exam/test/formalTest/page',
    data,
  });
};