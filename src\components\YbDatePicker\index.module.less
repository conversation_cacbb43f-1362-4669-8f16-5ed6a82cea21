.uploadTimeFilter {
  // position: absolute;
  margin-top: 2px;
  z-index: 999;
  background-color: #fff;
  width: 400px;
  box-sizing: border-box;
  font-size: 12px;
  padding: 14px 0;
  border-radius: 8px;
  border: 1px solid #a6b7ff;
  .datePickerBox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 0 10px;
    box-sizing: border-box;
  }
  .searchTips {
    padding: 0 10px;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.55);
    font-size: 12px;
    margin-bottom: 10px;
  }
  .borderBottom1MB10 {
    width: 100%;
    height: 0;
    border-bottom: 1px solid #c0d6ec;
    margin-bottom: 10px;
  }
  .datePickerDateBox {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 0 10px;
    box-sizing: border-box;
    span {
      padding-left: 12px;
    }
  }
  .datePickerBox2 {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 0 10px;
    box-sizing: border-box;
    :global {
      .ant-picker {
        border: 1px solid #c0d6ec !important;
        color: #000;
        font-size: 12px;
        input::placeholder {
          color: #91acc7;
          font-size: 12px;
        }
        .ant-picker-suffix {
          .anticon-calendar {
            // display: none !important;
          }
        }
      }
    }
  }
  .submitBtn {
    width: 110px;
    height: 32px;
    font-size: 14px;
    margin-bottom: 6px;
    background: #3d5afe;
  }

  :global {
    .ant-picker {
      border: 1px solid #c0d6ec !important;
      color: #000;
      font-size: 12px;
      input::placeholder {
        color: #000;
        font-size: 12px;
      }
      .ant-picker-suffix {
        .anticon-calendar {
          // display: none !important;
        }
      }
    }
  }
}

.YbDatePickerPop {
  :global {
    .ant-popover-inner {
      padding: 0;
    }
  }
}
