/**
 * 考勤模式模版
 */
export interface AttendanceMode {
  templateId: string;
  templateType: string;
  modeName: string;
}

interface WorkTimeFrame {
  type: string; // 类型
  desc: string; // 描叙;
  workTime: string;
}
export interface AttendanceTemplate {
  id: string; // 主键ID
  isDefault: string; // 是否默认true:是，false:否
  templateName: string; // 模版名称
  templateType: string; // 模版类型(1：正常工作制度，2：特殊工作制，3：两班倒工作制，4：三班倒工作制)
  workDay: string; // 工作时间
  lateOrEarly: {
    start: number;
    end: number;
  }; // 迟到或早退
  absenteWork: {
    day: number; // 旷工天数
    minute: number; // 旷工时长
  }; // 视为旷工
  seriousAbsenteWork: {
    day: number; // 旷工天数
    minute: number; // 旷工时长
  };
  monthRestDay: {
    day: string[];
    desc: string;
    typeCode: 'week' | 'month';
  }; // 月休息
  publicHolidays: {
    desc: string;
    enable: '1' | '0';
  }; // 法定节假日
  desc: string; // 描述
  enable: string; // 是否法定节假日1，是0，否
  overTimeFrame: {
    startTime: string; // 开始时间
    endTime: string; // 结束时间
    startTimeDesc: string; // 开始时间描述
    endTimeDesc: string; // 结束时间描述
  }; // 加班时段
  startTime: string; // 开始时间
  endTime: string; // 结束时间
  startTimeDesc: string; // 描述
  endTimeDesc: string; // 描述
  workTimeTable: {
    startMonday: string; // 描述
    endMonday: string; // 描述
  }; // 工作日
  startMonday: string; // 描述
  endMonday: string; // 描述
  workTimeFrameList: WorkTimeFrame[]; // 工作时间段
  createTime: string; // 创建时间;
}
