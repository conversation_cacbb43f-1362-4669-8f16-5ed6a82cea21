/**
 * @description 相册列表组件
 * <AUTHOR>
 * @date 2024-08-16
 */
import { updateAlbumNameBatch } from '@/api/magicAlbum';
import { setBrowseLog } from '@/pages/LoginReview/setRecord';
import { ListItem } from '@/pages/MagicAlbum/album.interface';
import { getImg } from '@/utils/common';
import { formatDate } from '@/utils/date';
import { Button, Input, message, Spin } from 'antd';
import { FC } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './index.module.less';
import { AlbumListItem } from './interface';

const AlbumList: FC<any> = (AlbumListProps) => {
  const [messageApi, contextHolder] = message.useMessage();
  const {
    renderList,
    albumListLoading,
    pager,
    hidePager,
    fromModule,
    pageState,
    isEditName = false,
    changeEditName,
    handleEditName,
    handleEditNameRefreshList,
    isShare = false,
    openShare,
  } = AlbumListProps;
  const navigate = useNavigate();
  // 记录浏览记录
  const setScanLog = (obj: any) => {
    obj.fileName = obj.albumsName || obj.albumsTemplateName;
    obj.fileType = 'image';
    setBrowseLog(obj, 2);
  };
  const saveEditName = async () => {
    //调用保存接口
    const albumNameUpdateReqVOList: Array<any> = [];
    renderList.map((item: ListItem) => {
      item.albumsId &&
        albumNameUpdateReqVOList.push({
          albumsId: item.albumsId,
          albumsName: item.albumsName,
        });
    });
    await updateAlbumNameBatch({ albumNameUpdateReqVOList });
    messageApi.success('相册重命名成功');
    changeEditName(false);
    // todo 更新列表数据
    handleEditNameRefreshList();
  };
  const showImg = (item: any) => {
    if (item.senderUserId || item.shareGroupId) {
      return getImg(item.coverPhotoHashUrl);
    } else {
      return getImg(item.coverPhotoUrl);
    }
  };
  return (
    <div className={styles.albumContainOut}>
      {contextHolder}

      <div className={styles.albumContainInner}>
        {renderList && renderList.length ? (
          <div className={`${styles.albumContain} ${hidePager ? styles.netAlbumContain : ''}`}>
            {renderList.map((item: AlbumListItem, index: number) => (
              <div
                key={item.albumsId ? `${item.albumsId}-${index}` : item.albumsTemplateId}
                className={styles.albumItem}
                onClick={() => {
                  setScanLog(item);
                  navigate('/magicAlbum/albumView', {
                    state: {
                      isTopping: item.templateTag && pager.pageNo === 1 && index === 0, // 用于判断是否为置顶模版
                      albumsTemplateId: item.albumsTemplateId,
                      senderUserId: item.senderUserId, // 判断是否来自分享
                      albumsId: item.albumsId,
                      albumsName: item.albumsName, // 为了带去修改相册回填相册名称
                      renderList: renderList,
                      page: pager.pageNo, //当前第几页
                      pageSize: pager.pageSize, //一页几条
                      total: pager.total, //总数
                      pageTotal: Math.ceil(pager.total / pager.pageSize),
                      index: index, //当前页的第几个
                      albumsCategorySingle: item.albumsCategorySingle,
                    },
                  });
                }}
              >
                {isEditName && item.albumsId && !item.senderUserId ? (
                  <div className="mt-5 flex justify-center">
                    <Input
                      value={item.albumsName}
                      className={styles.albumNameInput}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                      onChange={(e) => {
                        handleEditName(e.target.value, index);
                      }}
                    />
                  </div>
                ) : (
                  <div
                    className={styles.albumName}
                    title={item.albumsName || item.albumsTemplateName || ''}
                  >
                    {item.albumsName || item.albumsTemplateName || ''}
                  </div>
                )}
                {item.senderUserId ? (
                  <div className={styles.timeRow}>{formatDate(item.shareTime, 'YYYY-MM-DD')}</div>
                ) : (
                  item.createTime &&
                  item.albumsId && (
                    <div className={styles.timeRow}>
                      {formatDate(item.createTime, 'YYYY-MM-DD')}
                    </div>
                  )
                )}
                <div className={styles.imageWrap}>
                  <img
                    className={styles.image}
                    src={(item.albumsId && showImg(item)) || item.albumsTemplateCoverUrl || ''}
                  />
                </div>
                <div className={styles.imageRight} />

                {pageState
                  ? item.albumsId && AlbumListProps.renderItemBtn?.(item, index)
                  : AlbumListProps.renderItemBtn?.(item, index)}
                {item.senderUserId ? <div className={styles.shareTag}>分享</div> : null}
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.noData}>
            {albumListLoading ? <Spin size="large" /> : '暂无数据'}
          </div>
        )}
      </div>
      {AlbumListProps.RightBtn}
      {!hidePager && pager.total > 0 && (
        <div className={styles.pager}>
          {isEditName && (
            <>
              <Button type="primary" className="mr-3 w-20" onClick={saveEditName}>
                保存
              </Button>
              <Button
                className="mr-3 w-20"
                onClick={() => {
                  changeEditName(false);
                }}
              >
                取消
              </Button>
            </>
          )}
          {isShare && (
            <>
              <Button type="primary" className="w-30 mr-3" onClick={openShare}>
                点击选择分享对象
              </Button>
            </>
          )}
          <span className={styles.blueFont}>{pager.total}</span> 个相册 共{' '}
          <span className={styles.blueFont}>{Math.ceil(pager.total / pager.pageSize)}</span> 页
          当前第 <span className={styles.blueFont}>{pager.pageNo}</span> 页
        </div>
      )}
    </div>
  );
};
export default AlbumList;
