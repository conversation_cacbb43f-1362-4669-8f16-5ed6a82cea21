import { getArticleListPost } from '@/api/officialSite';
import { ArticleType, DetailByIdRes } from '@/api/officialSite/types';
import useAppStore from '@/store/useAppStore';
import useIntelligentSecretaryStore from '@/store/useIntelligentSecretaryStore.ts';
import { getWidth } from '@/utils/common';
import { Divider, List } from 'antd';
import { FC, useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import styles from './index.module.less';
import NewsItem from './NewsItem';

const NotificationFile: FC<{
  changeActiveTab: (
    key: string,
    item?: any,
    articleId?: string,
    pageType?: string,
    tabItem?: any,
  ) => void;
}> = ({ changeActiveTab }) => {
  const { channel } = useAppStore((state: any) => state);
  const [list, setList] = useState<DetailByIdRes[]>([]);
  const [pageNo, setPageNo] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [text, queryData, queryType] = useIntelligentSecretaryStore((state) => [
    state.text,
    state.queryData,
    state.queryType,
  ]);

  const fetchList = async (newPageNo: number) => {
    try {
      const params: any = {
        typeCode: ArticleType.通知文件,
        pageNo: newPageNo,
        pageSize: 20,
      };

      if (queryType.current === 'current') {
        params.keyWords = [text];
      } else if (queryType.current === 'results') {
        params.keyWords = queryData;
      }

      const { data } = (await getArticleListPost(params)) as { data: any };

      const nextItems = data.list;
      if (nextItems.length < params.pageSize) {
        setHasMore(false);
      }

      const newList: DetailByIdRes[] = [...list, ...nextItems];
      setList(newPageNo === 1 ? nextItems : newList);
      setPageNo(newPageNo);
    } catch (error) {
      //
    }
  };

  useEffect(() => {
    fetchList(1).then(() => {});
  }, [queryType]);

  return (
    <div className={styles.todayNews}>
      <div
        id="scrollableDivId"
        className={styles.scrollableDivBox}
        style={{ height: window.innerHeight - getWidth(channel === 'web' ? 404 : 360) }}
      >
        <InfiniteScroll
          dataLength={list.length}
          next={() => fetchList(pageNo + 1)}
          hasMore={hasMore}
          loader={false}
          endMessage={<Divider plain>没有更多数据了</Divider>}
          scrollableTarget="scrollableDivId"
        >
          <List
            className={styles.todoList}
            itemLayout="horizontal"
            dataSource={list}
            renderItem={(item) => <NewsItem articleItem={item} changeActiveTab={changeActiveTab} />}
          />
        </InfiniteScroll>
      </div>
    </div>
  );
};

export default NotificationFile;
