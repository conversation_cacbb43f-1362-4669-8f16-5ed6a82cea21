.data-wrapper {
  position: absolute;
  top: 34px;
  &.single {
    width: 350px;
    .footer {
      justify-content: flex-end;
    }
  }
  width: 650px;
  height: 510px;
  border-radius: 8px;
  opacity: 1;
  background: #ffffff;
  box-sizing: border-box;
  border: 1px solid #8c9eff;
  box-shadow: 0px 20px 40px 0px rgba(0, 0, 0, 0.1);
  z-index: 999 !important;
  .header {
    height: 62px;
    display: flex;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    .col {
      display: flex;
      justify-content: space-between;
      position: relative;
      flex: 1;
      padding: 16px;
      h3 {
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        color: #4d70fe;
      }
      + .col {
        &::before {
          position: absolute;
          content: ' ';
          left: 0;
          top: 0;
          width: 1px;
          height: 100%;
          background: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }
  .treeBox {
    display: flex;
  }
  .treeContent {
    width: 325px;
    flex: 1;

    .allSel {
      background: #e8eaf6;
      padding: 5px 10px;
      display: flex;
      align-items: center;
      div {
        flex: 1;
      }
    }
    :global {
      .ant-tree-switcher_close::after,
      .ant-tree-switcher_open::after {
        background-image: none !important;
      }
      .ant-tabs-nav {
        padding: 0 10px;
        margin: 0 !important;
      }
    }
    .btn {
      padding: 0 5px;
      background: rgba(0, 0, 0, 0.1);
      border: 1px rgba(0, 0, 0, 0.1) solid;
    }
    .actBtn {
      padding: 0 5px;
      border: 1px #3d5afe solid !important;
      background: #fff !important;
      color: #3d5afe !important;
    }
    .col {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      .filter {
        height: 65px;
        padding: 16px 0 16px 16px;
        :global {
          .ant-form-item {
            margin-inline-end: 8px;
            button {
              width: 72px;
            }
          }
        }
      }
      .treelist {
        height: 380px;
        overflow-y: auto;

        li {
          // line-height: 35px;
          padding: 8px;
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          // cursor: pointer;
          div {
            flex: 1;
            margin: 0 4px;
          }
          .btn {
            padding: 0 5px;
            background: rgba(0, 0, 0, 0.1);
            border: 1px rgba(0, 0, 0, 0.1) solid;
          }
          .actBtn {
            padding: 0 5px;
            border: 1px #3d5afe solid !important;
            background: #fff !important;
            color: #3d5afe !important;
          }
        }
        .actLi {
          background: #e8eaf6 !important;
          color: #3d5afe !important;
          border-radius: 8px;
        }

        .ant-tree-treenode {
          padding: 8px 16px !important;
        }
        :global {
          .ant-tree-treenode {
            padding: 8px 16px !important;
            .item {
              // padding: 8px 16px;
              &:hover {
                background: #e8eaf6;
              }
              &.active {
                button {
                  background: #92a2ff;
                  color: #ffffff;
                }
                color: var(--yb-primary-color);
              }
            }
          }
        }
      }
      + .col {
        &::before {
          position: absolute;
          content: ' ';
          left: 0;
          top: 0;
          width: 1px;
          height: 100%;
          background: rgba(0, 0, 0, 0.05);
          transform: scaleX(0.5);
        }
      }
    }
  }
  .footer {
    height: 64px;
    display: flex;
    padding: 0 16px;
    align-items: center;
    justify-content: center;
    position: relative;
    &::before {
      position: absolute;
      content: ' ';
      left: 0;
      top: 0;
      right: 0;
      height: 1px;
      transform: scaleY(0.5);
      background: rgba(0, 0, 0, 0.05);
    }
  }
}
.treeBox {
  :global {
    .ant-tree-title {
      display: flex;
      align-items: center;
      // display: 'flex', alignItems: 'center', padding: '8px'
      padding: 8px;
      // span {
      //   flex: 1;
      // }
      > div {
        display: flex;
        align-items: center;
        width: 100%;
      }
    }
    .ant-tree-switcher {
      display: none !important;
    }
    .ant-tree-treenode {
      // padding: 8px;
      display: flex;
      align-items: center;
    }
  }
}

.outShowBox {
  display: flex;
  align-items: center;
  border: 1px #d9d9d9 solid;
  border-radius: 5px;
  padding: 5px 10px;
  cursor: pointer;
  line-height: 20px;
  div {
    flex: 1;
  }
  &.active {
    border: 1px #3d5afe solid;
  }
}
.placeholder {
  color: var(--ant-color-text-placeholder);
}

.treeTitle {
  .active,
  .tree-search-value {
    color: #3d5afe;
  }
}
