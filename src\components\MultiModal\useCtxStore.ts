import { create } from '@/store';

interface state {
  modalLeft: boolean;
  modalRight: boolean;
}

const groupMap = new Map();

const createUseCtxStore = (group: string = 'default') => {
  if (groupMap.has(group)) {
    return groupMap.get(group);
  }
  const useCtxStore = create<state>(
    (): state => ({
      modalLeft: false,
      modalRight: false,
    }),
  );
  groupMap.set(group, useCtxStore);
  return useCtxStore;
};

export default createUseCtxStore;
