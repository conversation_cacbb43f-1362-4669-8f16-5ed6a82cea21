export interface CreateQuestionModel {
  id?: string;
  questionBankId?: number; // 题库ID
  questionContent: string; // 题目内容
  questionType: number; // 题目类型 1:填空题 2:单选题 3:多选题 4:简答题 5:论述题
  answer?: string; //	答案（填空题、简答题、论述题）
  questionOptions?: QuestionOption[]; // 题目选项（单选题、多选题）
}

interface QuestionOption {
  id?: number;
  questionId?: number;
  optionContent: string; // 选项内容
  isCorrect: number; // 是否正确答案（0:错误 1:正确）
}
