import { useContext, useEffect, useMemo } from 'react';
import Context from '../Context';
import MultiModal from '@/components/MultiModal';
import { Button } from 'antd';
import Filter from './Filter';
import List from './List';
import styles from './index.module.less';

const Component = () => {
  const { useUsersCtxStore, useMainPanelCtxStore, config } = useContext(Context);
  const [usersOpen, setUsersOpen, list, selectedList] = useUsersCtxStore!((state) => [
    state.usersOpen,
    state.setUsersOpen,
    state.list,
    state.selectedList,
    state.setSelectedList,
  ]);
  const [setUserList, userMap, setUserMap] = useMainPanelCtxStore!((state) => [
    state.setUserList,
    state.userMap,
    state.setUserMap,
  ]);
  const okEnabled = useMemo(() => {
    return selectedList.some((item: any) => !userMap[item.id]);
  }, [selectedList, userMap]);
  const cancel = () => {
    setUsersOpen(false);
  };
  const ok = () => {
    const map = { ...userMap };
    selectedList.forEach((item: any) => {
      if (!map[item.id]) {
        map[item.id] = item;
      }
    });
    setUserMap(map);
    setUserList(Object.values(map));
  };
  useEffect(() => {
    if (usersOpen === false) {
      useUsersCtxStore!.reset();
    }
  }, [usersOpen]);
  const title = (
    <div className={styles.title}>
      <span>通讯录</span>
      <span>
        共计 <b>{list.length}</b> 条，已选择 <b>{selectedList.length}</b> 条
      </span>
    </div>
  );

  return (
    <MultiModal
      layoutGroup={`share_${config?.module}`}
      layoutClassName="modalLeft"
      destroyOnClose={true}
      title={title}
      open={usersOpen}
      onCancel={cancel}
      footer={[
        <Button key="submit" type="primary" disabled={!okEnabled} onClick={ok}>
          确认选择
        </Button>,
      ]}
    >
      <Filter />
      <List />
    </MultiModal>
  );
};

export default Component;
