/**
 * 选择图片组件
 */

import { searchConditionAll } from '@/api/library';
import Drawer from '@/components/Drawer';
import { getImg } from '@/utils/common';
import { Button } from 'antd';
import classNames from 'classnames';
import { FC, useEffect, useState } from 'react';
import Header from '../../publicComponents/Header';
import styles from './index.module.less';
interface Props {
  title?: string; // 头部标题
  open: boolean; // 开关标识
  onClose: () => void; // 关闭窗口
  onSubmit: (list: any[]) => void; // 提交表单
  mode: 'single' | 'multiple'; // 单选/多选
  type: 1 | 2 | null;
  defaultValue?: string | string[] | any[]; // 默认选中的 传id。格式如： '5802666698' | ['5802666698', '5802666698'] | [{ id: '5802666698' }]
  dataSource?: any[]; // 数据源
  disabledList?: any[]; // 禁用列表[{ id: '5802666698',... }]
}
const SelectPicture: FC<Props> = ({
  title = '添加图片',
  open,
  onClose,
  onSubmit,
  mode,
  type,
  defaultValue,
  dataSource,
  disabledList,
}) => {
  const [selectListIds, setSelectListIds] = useState<any[]>([]);
  const [renderList, setRenderList] = useState(dataSource || []); // 用于控制渲染的列表
  const [libraryImgPageNo, setLibraryImgPageNo] = useState({ current: 1 });
  const [hasMore, setHasMore] = useState(true);
  const getLibraryDictData = () => {
    searchConditionAll({
      fileFormatType: ['library_file_type_pic'],
      pageNo: libraryImgPageNo.current,
      pageSize: 50,
    }).then(({ data }: any) => {
      libraryImgPageNo.current === 1
        ? setRenderList(data.list)
        : setRenderList([...renderList, ...data.list]);
      if (renderList.length + data.list.length >= data.total) {
        setHasMore(false);
      }
    });
  };
  useEffect(() => {
    if (open) getLibraryDictData();
  }, [open]);
  useEffect(() => {
    let list: any[] = [];
    if (!defaultValue) return;
    if (typeof defaultValue === 'string') {
      list = [defaultValue];
    }
    if (typeof defaultValue === 'object') {
      if (typeof defaultValue[0] === 'string') {
        list = [...defaultValue];
      } else {
        list = defaultValue.map((item) => item.id);
      }
    }
    setSelectListIds(list);
  }, [defaultValue]);

  // 提交选中
  const handlderSubmit = () => {
    onSubmit(renderList.filter((item: any) => selectListIds.includes(item.id)));
  };

  // 渲染头部功能按钮
  const renderActionList = () => {
    const btnList = [
      <Button key="goBack" onClick={onClose}>
        返回
      </Button>,
    ];

    return btnList;
  };
  return (
    <Drawer
      title={<Header title={title} onCancel={onClose} actionList={renderActionList()} />}
      onClose={onClose}
      open={open}
      footer={
        <div className={styles.footer}>
          <div>已选择图片{selectListIds.length}</div>
          <div>
            <Button
              size="large"
              type="primary"
              disabled={selectListIds.length === 0}
              onClick={handlderSubmit}
            >
              确定
            </Button>
          </div>
        </div>
      }
    >
      <div className={styles.employeeListContainer}>
        <div className={styles.imgListContainer}>
          {renderList.map((item) => (
            <div
              key={item.id}
              className={styles.imageItem}
              onClick={() => {
                if (selectListIds.includes(item.id)) {
                  setSelectListIds(selectListIds.filter((id: any) => item.id !== id));
                } else {
                  console.log([...selectListIds, item.id]);
                  setSelectListIds([...selectListIds, item.id]);
                }
              }}
            >
              <img
                src={getImg(item.visitPath || item.path)}
                // style={{ width: `${imgHeight}px`, height: `${imgHeight}px` }}
              />
              <span
                className={classNames(
                  styles.imageItemBtn,
                  selectListIds.includes(item.id) ? styles.imageSelected : undefined,
                )}
              >
                选择
              </span>
            </div>
          ))}
        </div>
      </div>
    </Drawer>
  );
};

export default SelectPicture;
