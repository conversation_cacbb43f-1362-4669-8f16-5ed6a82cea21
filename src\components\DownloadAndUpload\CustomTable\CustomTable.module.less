@top1: 81px;
@top2: -3px;
.CustomTable {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  .CustomTableBox {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
  .VirtualScroll {
    position: absolute;
    width: 14px;
    height: 100%;
    right: -2px;
    z-index: 1;
    padding: 1px;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #fff;
    border: 1px solid #999;
    box-sizing: content-box;
  }
  .downArrow,
  .upArrow {
    width: 14px;
    height: 14px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #98b6ff;
    color: #fff;
    position: absolute;
    cursor: pointer;
    z-index: 21;
  }
  .downArrow {
    right: 0;
    top: 0;
    border-bottom: 1px solid #fff;
  }
  .upArrow {
    bottom: 0;
    right: 0;

    border-top: 1px solid #fff;
  }
  
  .VirtualScrollTop70 {
    :global {
      .ant-table-tbody-virtual-scrollbar {
        width: 14px !important;
      }
      .ant-table-tbody-virtual-scrollbar-thumb {
        min-height: 100px;
        max-height: 100px;
        position: relative;
        border-radius: 0 !important;
        display: block !important;
       
        z-index: 20;
        background-color: transparent !important;
        visibility: visible !important;
        &::after {
          width: 14px;
          height: 116px;
          content: '下拉更多';
          font-size: 10px;
          position: absolute;
          background-color: #98b6ff;
          color: #fff;
          z-index: 20;
          line-height: 1.3;
          display: flex;
          justify-content: center;
          align-items: center;
          text-align: center;
          top: @top1;
          pointer-events: none;
        }
      }
    }
  }
  .VirtualScrollTop10 {
    :global {
      .ant-table-tbody-virtual-scrollbar {
        width: 14px !important;
      }
      .ant-table-tbody-virtual-scrollbar-thumb {
        min-height: 100px;
        max-height: 100px;
        position: relative;
        border-radius: 0 !important;
        display: block !important;
        transform: translateY(-50%);
        z-index: 20;
        background-color: transparent !important;
        visibility: visible !important;
        &::after {
          height: 116px;
          content: '下拉更多';
          font-size: 10px;
          position: absolute;
          background-color: #98b6ff;
          color: #fff;
          transform: translateY(-50%);
          z-index: 20;
          line-height: 1.3;
          display: flex;
          justify-content: center;
          align-items: center;
          text-align: center;
          top: @top2;
        }
      }
    }
  }
  :global{
    .ant-table-tbody-virtual-scrollbar-horizontal{
      display: none !important;
      .ant-table-tbody-virtual-scrollbar-thumb::after{
        content: '';
        
        width: 0 !important;
      }
    }
  }

}
