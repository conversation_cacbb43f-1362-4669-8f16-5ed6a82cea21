import voiceOff from '@/assets/images/mail/voiceOff.png';
import voiceOn from '@/assets/images/mail/voiceOn.png';
import { getWidth } from '@/utils/common';
import { Timeout } from 'ahooks/lib/useRequest/src/types';
import { Button, Flex, message, Modal } from 'antd';
import { useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
interface Props {
  open: boolean;
  title: string;
  onCancel: () => void;
  onAdd: (data: any) => void;
}
const Component = ({ open, title, onCancel, onAdd }: Props) => {
  const [duration, setDuration] = useState<number>(0);
  const [isRunning, setIsRunning] = useState<boolean>(false); //是否已开启录音
  const intervalRef = useRef<Timeout>();

  const audioChunksRef = useRef<Blob[]>([]);
  const mediaStreamRef = useRef<MediaStream>();
  const mediaRecorderRef = useRef<MediaRecorder>();
  const [audioBlob, setaudioBlob] = useState<Blob>();
  useEffect(() => {
    if (audioBlob && duration < 120) {
      onAdd({ duration, audioBlob, type: 'mp3' });
      onCancel();
    }
  }, [audioBlob]);

  useEffect(() => {
    if (duration === 120) {
      stopRecording();
      message.warning('录制时长最长为120秒,已自动结束并保存');
      setTimeout(() => {
        onAdd({ duration, audioBlob, type: 'mp3' });
        onCancel();
      }, 2000);
    }
  }, [duration]);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaStreamRef.current = stream;
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' }); // 注意：这里可能需要指定正确的MIME类型，例如'audio/webm'或'audio/ogg; codecs=opus'
        setaudioBlob(audioBlob); // 个state来保存
        audioChunksRef.current = []; // 清空chunks以便下次录制
      };
      mediaRecorder.start();
      startTimer();
    } catch (error) {
      console.error('获取媒体输入权限失败:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      stopTimer();
      mediaRecorderRef.current.stop();
    }
  };

  const startTimer = () => {
    if (!isRunning) {
      setIsRunning(true);
      intervalRef.current = setInterval(() => {
        setDuration((prev) => prev + 1);
      }, 1000);
    }
  };
  const stopTimer = () => {
    if (isRunning) {
      clearInterval(intervalRef.current);
      setIsRunning(false);
    }
  };
  const formatTime = () => {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const secs = duration % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  const startRecord = () => {
    startRecording();
  };
  const stopRecord = () => {
    stopRecording();
  };

  useEffect(() => {
    return () => {
      stopTimer();
      mediaStreamRef.current?.getTracks().forEach((track) => track.stop());
    };
  }, []);

  // 初始化竖条形状态，包括基准高度和当前高度
  const [bars, setBars] = useState(
    Array.from({ length: 20 }, (_, i) => ({
      currentHeight: Math.floor(Math.random() * 51) + 50, // 随机初始化当前高度50-100
    })),
  );
  const animationFrameId = useRef<number | null>(null);
  const animate = () => {
    // 更新每个竖条形的高度
    const newBars = bars.map((bar) => {
      const changeAmount = Math.floor(Math.random() * 51) - 25; // -25 到 25 的随机变化量
      const newHeight = bar.currentHeight + changeAmount; // 限制高度范围
      return { currentHeight: newHeight };
    });
    setBars(newBars);
    // 请求下一帧动画
    // animationFrameId.current = requestAnimationFrame(animate);
  };
  useEffect(() => {
    if (isRunning) {
      const intervalId = setInterval(() => {
        if (animationFrameId.current !== null) {
          cancelAnimationFrame(animationFrameId.current);
        }
        animationFrameId.current = requestAnimationFrame(animate);
      }, 300);
      return () => {
        clearInterval(intervalId);
        if (animationFrameId.current !== null) {
          cancelAnimationFrame(animationFrameId.current);
        }
      };
    }
  }, [isRunning]);

  return (
    <Modal
      title={null}
      open={open}
      centered
      closable={false}
      footer={null}
      keyboard={false}
      maskClosable={false}
      width={getWidth(462)}
      mask={false}
      className={styles.recordContent}
    >
      <Flex justify={'space-between'} align={'center'} className={styles.header}>
        <span className={styles.headerSpan}>{title}</span>
        <Button className={styles.headerButton} onClick={onCancel}>
          关闭
        </Button>
      </Flex>
      <Flex vertical justify={'space-around'} className={styles.content}>
        <Flex justify={'center'}>
          <span className={styles.duration}>{formatTime()}</span>
        </Flex>
        <div className={styles.animate}>
          {/* {isRunning ? <span>录制中...</span> : <span>等待录制</span>} */}
          {/* 录音结束后展示音频播放器 */}
          {bars.map((bar, index) => (
            <div
              key={index}
              className={styles.bar}
              style={{
                height: `${bar.currentHeight}px`,
              }}
            />
          ))}
        </div>
        <Flex vertical justify={'flex-start'} align={'center'} gap={10}>
          {isRunning ? (
            <img src={voiceOff} onClick={stopRecord} className={styles.images} />
          ) : (
            <img src={voiceOn} onClick={startRecord} className={styles.images} />
          )}
          <span className={styles.conButton}>{isRunning ? '结束录音' : '开始录音'}</span>
        </Flex>
      </Flex>
    </Modal>
  );
};

export default Component;
