* {
  &::-webkit-scrollbar {
    width: 14px;
    cursor: pointer;
  }
  &::-webkit-scrollbar-button {
    &:vertical,
    &:horizontal {
      &:increment {
        background-image: url(./down.png);
        background-color: #98b6ff;
        background-size: 8px 5px;
        background-position: center center;
        background-repeat: no-repeat;
        border-top: 1px solid #fff;
      }
      &:decrement {
        background-size: 8px 5px;
        background-image: url('./up.png');
        background-color: #98b6ff;
        background-position: center center;
        background-repeat: no-repeat;
        border-bottom: 1px solid #fff;
      }
    }
    &:horizontal {
      &:increment {
        background-size: 5px 8px;
        background-image: url('./right.png');
        border-top: 0px solid #fff;
        border-left: 1px solid #fff;
      }
      &:decrement {
        background-size: 5px 8px;
        background-image: url(./left.png);
        border-bottom: 0px solid #fff;
        border-right: 1px solid #fff;
      }
    }
  }
  &::-webkit-scrollbar-track {
    background-color: #fff;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #98b6ff;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    &:vertical {
      min-height: 100px;
      background-image: url(./more.png);
    }
    &:horizontal {
      background-image: url(./rightMore.png);
    }
  }
}
