import SpinTb from '@/assets/images/video/spin.svg';
import SonicDiagram from '@/pages/AudioPlay/SonicDiagram';
import CanvasFilter from '@/pages/VideoPlay/CanvasFilter';
import useUserStore from '@/store/useUserStore';
import { useEffect, useRef, useState } from 'react';
import Player, { Events } from 'xgplayer';
import FlvPlugin, { EVENT } from 'xgplayer-flv';
import 'xgplayer/dist/index.min.css';
import styles from './AudioVideoLivingPreview.module.less';
interface dataType {
  pullUrl: string;
  isAduio?: boolean;
}
// 播放器实例
let player: Player;
const AudioVideoLivingPreview = (props: dataType) => {
  const videoRef = useRef<HTMLDivElement>(null);
  const [isSpining, setIsSpining] = useState<boolean>(true);
  const [VideoElement, setVideoElement] = useState<HTMLVideoElement | null>(null);
  useEffect(() => {
    playerInit();
    return () => {
      if (player) {
        player.destroy();
      }
    };
  }, []);
  // 播放初始化
  const playerInit = () => {
    const token = useUserStore.getState().accessToken;
    const clientType = useUserStore.getState().clientType;
    const deviceId = useUserStore.getState().deviceId;
    const $div: any = videoRef.current;
    player = new Player({
      el: $div,
      width: '100%',
      height: '100%',
      autoplay: true,
      fitVideoSize: 'fixHeight',
      isLive: true,
      url: `${props.pullUrl}?streamToken=${token}&deviceId=${deviceId}&deviceType=${clientType}`,
      plugins: [FlvPlugin],
      // flv: {},
      ignores: [
        'controls',
        'progress',
        'progresspreview',
        'mobile',
        'pc',
        'definition',
        'fullscreen',
        'cssfullscreen',
        'play',
        'poster',
        'enter',
        'replay',
        'start',
        'volume',
        'time',
        'playbackrate',
        'pip',
      ],
    });
    const media = player.media as HTMLVideoElement;
    setVideoElement(media);
    // 监听视频能播放
    media.addEventListener('canplay', () => {
      setIsSpining(false);
    });
    // 监听 开始拉流
    player.on(Events.LOAD_START, () => {
      //console.log('player.plugins.flv.core')
    });
    player.on('core_event', ({ eventName, ...rest }) => {
      if (eventName === EVENT.LOAD_RESPONSE_HEADERS) {
        console.log(rest);
      }
    });
    // 播放失败前
    player.usePluginHooks('error', 'showError', (_plugin, ..._args) => {
      return true;
    });

    //监听报错
    player.on(Events.ERROR, (error) => {
      console.log('error=============', error);
    });
    //播放时间改变
    player.on(Events.TIME_UPDATE, () => {});
    //播放声音改变
    player.on(Events.VOLUME_CHANGE, () => {});
    //播放路径改变
    player.on(Events.URL_CHANGE, () => {});
    //视频时长发生变化
    player.on(Events.DEFINITION_CHANGE, () => {});
    //等待加载数据中
    player.on(Events.WAITING, () => {});
    //视频缓冲足够可播放
    player.on(Events.CANPLAY, () => {});
  };
  const handleUrl = (url: string) => {
    const token = useUserStore.getState().accessToken;
    const clientType = useUserStore.getState().clientType;
    const deviceId = useUserStore.getState().deviceId;
    return `${url}?streamToken=${token}&deviceId=${deviceId}&deviceType=${clientType}`;
  };
  useEffect(() => {
    const url = handleUrl(props.pullUrl);
    if (player.config.url == url) return;
    player.switchURL(url);
  }, [props.pullUrl]);

  return (
    <div className={`${styles.AudioVideoLivingPreview}`}>
      {isSpining ? (
        <div
          className="absolute z-10 flex items-center justify-center"
          style={{ width: '100%', height: '100%', backgroundColor: 'rgba(0,0,0,1)' }}
        >
          <div className={styles.spinning}>
            <img src={SpinTb} />
            <div>加载中...</div>
          </div>
        </div>
      ) : null}
      <div className={styles.mainBox}>
        <div className={styles.filterBg}>
          {VideoElement && !props.isAduio ? <CanvasFilter video={VideoElement} /> : null}
        </div>
        <div className={styles.videoBox}>
          <div style={{ position: 'absolute', width: '100%', height: '100%' }}>
            {VideoElement ? <CanvasFilter video={VideoElement} /> : null}
          </div>
          <div className={styles.videoBoxRander} ref={videoRef} hidden={props.isAduio} />
          {VideoElement && props.isAduio ? (
            <div className={styles.aduioLivingBox}>
              <SonicDiagram video={VideoElement} />
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default AudioVideoLivingPreview;
