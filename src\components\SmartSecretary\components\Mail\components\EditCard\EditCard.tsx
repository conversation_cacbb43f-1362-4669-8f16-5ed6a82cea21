import { MailBookReq, options } from '@/api/mail/chat/mailModels';
import type { FilePreviewAPI } from '@/components/FliePreview';
import FliePreview from '@/components/FliePreview';
import useUserStore from '@/store/useUserStore';
import { Button, Flex, Input, InputRef, List, message, Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';

import { sendEmailForInside, sendEmailForOuter } from '@/api/mail/chat';
import RecordVideo from '@/components/RecordVideo';
import Recording from '@/components/Recording';
import SelectFile from '@/components/SmartSecretary/publicComponents/SelectFile';
import SelectPicture from '@/components/SmartSecretary/publicComponents/SelectPicture';
import { Popup } from '@/components/SmartSecretary/publicComponents/vantComponents';
import VideoCall from '@/components/VideoCall';
import VoiceCall from '@/components/VoiceCall';
import useAddressBookStore from '@/store/useAddressBook';
import { cwsRequest } from '@/store/useCppWebSocketStore';
import { getImg } from '@/utils/common';
import { truncateString } from '@/utils/parser';
import SelectEmployee from '../SelectEmploy';
import styles from './EditCard.module.less';
import { convert, convertAddress, convertGroup, revert } from './convert';

interface Props {
  replyData: any;
  close: () => void;
  restChat: () => void;
}
const EditCard = ({ replyData, close, restChat }: Props) => {
  const emptyData: MailBookReq = {
    id: '0',
    mailTitle: '',
    mailContent: '',
    confidentialityStatus: 0,
    senderId: '',
    shareFlag: 0,
    senderAddress: '',
    senderName: '',
    timedTime: '',
    addressList: [],
    addressGroupList: [],
    copyAddressList: [],
    copyAddressGroupList: [],
    secretAddressList: [],
    mailChatAttachVOList: [],
    jsonContent: '',
  };
  const [editData, setEditData] = useState<MailBookReq>(emptyData);
  const filePreviewRef = useRef<FilePreviewAPI>();
  const baomidengji = options.find((o) => +o.value == editData.confidentialityStatus)?.label;
  const userInfo = useUserStore((state) => state.userInfo);
  const { TextArea } = Input;

  useEffect(() => {
    if (replyData.isSet) {
      const { address, mail } = replyData;
      console.log(address);
      console.log(mail);
      setTitle(mail.mailStatus == 1 ? '' : mail.mailTitle ? `回复：${mail.mailTitle || ''}` : '');
      const res = revert(address);
      if (res.type === 1) {
        setSelectData1([res]);
      } else {
        setSelectData2([res]);
      }
    }
  }, [replyData]);
  //标题和正文处理start-------------------
  const [title, setTitle] = useState<string>(editData.mailTitle);
  const [content, setContent] = useState<string>(editData.mailContent);
  const [isVisibleTitle, setIsVisibleTitle] = useState<boolean>(false);
  const inputRefTitle = useRef<InputRef>(null);
  const inputRefContent = useRef<InputRef>(null);
  const [visible, setVisible] = useState(false); //正文类型弹窗
  const onChangeTitle = (e: any) => {
    setTitle(e.target.value);
  };
  const onChangeContent = (e: any) => {
    setContent(e.target.value);
  };
  const handleShowInput = (val: string) => {
    if (val === 'title') {
      setIsVisibleTitle(true);
    }
  };
  const handleBlurTitle = () => {
    setIsVisibleTitle(false);
  };
  useEffect(() => {
    if (isVisibleTitle && inputRefTitle.current) {
      inputRefTitle.current.focus({
        cursor: 'end',
      });
    }
  }, [isVisibleTitle]);
  const footer = (
    <div className={styles.contentFooter}>
      <div
        className={styles.contentCancal}
        onClick={() => {
          setVisible(false);
        }}
      >
        取消
      </div>
    </div>
  );
  const treeData = [
    { name: '普通正文', code: 1 },
    { name: '图片正文', code: 2 },
    { name: '语音正文', code: 3 },
    { name: '视频正文', code: 4 },
    { name: '语音通话正文', code: 5 },
    { name: '视频通话正文', code: 6 },
  ];
  const checkContenType = (item: any) => {
    setVisible(false);
    if (item.code === 2) {
      setOpenSelectPicture(true);
    }
    switch (item.code) {
      case 1:
        inputRefContent.current?.focus({
          cursor: 'end',
        });
        break;
      case 2:
        setOpenSelectPicture(true);
        break;
      case 3:
        setRecording(true);
        break;
      case 4:
        setRecordVideo(true);
        break;
      case 5:
        setVoiceCall(true);
        break;
      case 6:
        setVideoCall(true);
        break;
    }
  };
  const checkStatus = (val: number) => {
    if (val > 4) {
      if (content || editData.mailChatAttachVOList.length > 0 || selectData1.length === 0) {
        return true;
      }
      return false;
    } else {
      return false;
    }
  };
  //图片正文和附件处理start------------------
  const [imageData, setImageData] = useState<any[]>([]); //图片正文
  const [fileData, setFileData] = useState<any[]>([]); //普通附件
  const [videoData, setVideoData] = useState<any[]>([]); //视频附件
  const [audioData, setAudioData] = useState<any[]>([]); //音频附件
  const [openSelectPicture, setOpenSelectPicture] = useState(false); //选择文件组件
  const [openSelectFile, setOpenSelectFile] = useState(false); //选择附件组件
  const handleAddImage = (list: any) => {
    setImageData(list);
    setOpenSelectPicture(false);
  };
  const handleAddFile = (list: any) => {
    console.log('fileData', list);
    setOpenSelectFile(false);
    setFileData(list);
  };
  const deleteImage = (item: any) => {
    const data = imageData.filter((val) => val.id !== item.id);
    setImageData(data);
  };
  const deleteFile = (item: any) => {
    const data = fileData.filter((val) => val.id !== item.id);
    setFileData(data);
  };
  const deleteAudio = (item: any) => {
    const data = audioData.filter((val) => val.attachmentId !== item.attachmentId);
    setAudioData(data);
  };
  const deleteVideo = (item: any) => {
    const data = videoData.filter((val) => val.attachmentId !== item.attachmentId);
    setVideoData(data);
  };
  const rendImageItems = () => {
    return (
      <>
        <Flex className={styles.imageBox} wrap justify={'space-between'}>
          {imageData.map((item: any) => {
            return (
              <Flex vertical key={item.id} gap={5} className={styles.image} align="center">
                <img src={getImg(item.visitPath)}></img>
                <span className={styles.delBtn} onClick={() => deleteImage(item)}>
                  删除
                </span>
              </Flex>
            );
          })}
        </Flex>
      </>
    );
  };
  const rendFileItems = () => {
    return (
      <>
        <Flex className={styles.fileBox} wrap gap={5}>
          {fileData.map((item: any) => {
            return (
              <Flex key={item.id} className={styles.file} wrap={false} justify={'space-between'}>
                <span>{truncateString(item.title, 12)}</span>
                <span className={styles.delBtn} onClick={() => deleteFile(item)}>
                  删除
                </span>
              </Flex>
            );
          })}
        </Flex>
      </>
    );
  };
  const rendAudioItems = () => {
    return (
      <>
        <Flex className={styles.fileBox} wrap gap={5}>
          {audioData.map((item: any) => {
            return (
              <Flex
                key={item.attachmentId}
                className={styles.file}
                wrap={false}
                justify={'space-between'}
              >
                <span>{truncateString(item.attachmentName, 12)}</span>
                <span className={styles.delBtn} onClick={() => deleteAudio(item)}>
                  删除
                </span>
              </Flex>
            );
          })}
        </Flex>
      </>
    );
  };
  const rendVideoItems = () => {
    return (
      <>
        <Flex className={styles.fileBox} wrap gap={5}>
          {videoData.map((item: any) => {
            return (
              <Flex
                key={item.attachmentId}
                className={styles.file}
                wrap={false}
                justify={'space-between'}
              >
                <span>{truncateString(item.attachmentName, 12)}</span>
                <span className={styles.delBtn} onClick={() => deleteVideo(item)}>
                  删除
                </span>
              </Flex>
            );
          })}
        </Flex>
      </>
    );
  };
  //收件人、抄送人、密送人、收件群、密送群处理start------------------
  const [selectData1, setSelectData1] = useState<any[]>([]); //收件人
  const [selectData2, setSelectData2] = useState<any[]>([]); //收件群
  const [selectData3, setSelectData3] = useState<any[]>([]); //抄送人
  const [selectData4, setSelectData4] = useState<any[]>([]); //抄送群
  const [selectData5, setSelectData5] = useState<any[]>([]); //密送人
  const [sendMailType, setSendMailType] = useState<number>(0); //1内部 2外部
  const [addressBookList] = useAddressBookStore((state) => [state.addressBookList]); // 通讯录列表：包含联系人和群组
  const [dataSource, setDataSource] = useState(addressBookList); //当前可选通讯录数据源
  const [openAddressBook, setOpenAddressBook] = useState(false); //打开通讯录组件
  const [selectAddressType, setSelectAddressType] = useState(1); //1收件 2抄送 3密送

  useEffect(() => {
    if (sendMailType === 2) {
      //取外部联系人
      setDataSource(addressBookList.filter((item: any) => item.contactType === 2));
    } else if (sendMailType === 1) {
      //获取内部联系人
      setDataSource(addressBookList.filter((item: any) => item.contactType !== 2));
    } else {
      //内部+外部
      setDataSource(addressBookList);
    }
  }, [sendMailType]);
  useEffect(() => {
    if (
      [...selectData1, ...selectData2, ...selectData3, ...selectData4, ...selectData5].length === 0
    ) {
      return;
    }
    if ([...selectData2, ...selectData4].length > 0) {
      setSendMailType(1);
    } else {
      const a = [...selectData1, ...selectData3, ...selectData5].find((item: any) => {
        return item.contactType === 2;
      });
      if (a) {
        setSendMailType(2);
      } else {
        setSendMailType(1);
      }
    }
  }, [selectData1, selectData2, selectData3, selectData4, selectData5]);
  const changeData = (item1: any, item2: any) => {
    const a =
      item1?.map((i: any) =>
        i.username
          ? `${i.contactName ? i.contactName : ''}` + `${i.username}`
          : `${i.contactName ? i.contactName : i.email}`,
      ) || [];
    const b = item2?.map((i: any) => `${i.contactName}`) || [];
    return [...a, ...b];
  };
  const renderItems = (item1: any, item2: any) => {
    const result = changeData(item1, item2);
    if (result.length === 0) return;
    return (
      <Flex gap={5}>
        <span>{result[0]}</span>
        <span style={{ color: '#F45252' }}>{`(${result.length})`}</span>
      </Flex>
    );
  };
  const getEmloyeeValue = (list: any) => {
    const listPerson = list.filter((item: any) => item.type === 1);
    const listGroup = list.filter((item: any) => item.type === 2);
    if (selectAddressType === 1) {
      setSelectData1(listPerson);
      setSelectData2(listGroup);
    } else if (selectAddressType === 2) {
      setSelectData3(listPerson);
      setSelectData4(listGroup);
    } else if (selectAddressType === 3) {
      setSelectData5(list);
    }
    setOpenAddressBook(false);
  };

  //音视频录制和通话
  const [recording, setRecording] = useState<boolean>(false);
  const [recordVideo, setRecordVideo] = useState<boolean>(false);
  const [videoCall, setVideoCall] = useState<boolean>(false);
  const [voiceCall, setVoiceCall] = useState<boolean>(false);
  //添加音视频正文
  const handleAddRecord = (data: any) => {
    setTip('文件上传中，请稍后...');
    setLoading(true);
    const name = userInfo?.realName ? userInfo?.realName : userInfo?.username;
    const filename = name + `${data.type === 'mp3' ? '语音正文邮件' : '视频正文邮件'}`;
    const filesize = data.type === 'mp3' ? data.audioBlob.size : data.videoBlob.size;
    const filetype = data.type === 'mp3' ? 'audio' : 'video';
    const fileFormatType =
      data.type === 'mp3' ? 'library_file_type_audio' : 'library_file_type_video';
    const blobData = data.type === 'mp3' ? data.audioBlob : data.videoBlob;
    cwsRequest({
      module: 'mediaData',
      method: 'sendMediaData',
      data: {
        module: 'recordfile',
        startmethod: 'startrecordfile', //开始发送文件
        endmethod: 'stoprecordfile', //结束发送文件名
        data: {
          filename: filename, //文件名
          filesize: filesize, //文件流大小
          filetype: 'webm',
          business_type: 1, //文件来源 1表示邮箱
          mediatype: filetype,
          duration: data.duration,
        },
        BlobData: blobData, // 文件流数据
      },
    })
      .then((res: any) => {
        console.log('音视频res', res);
        if (res.code == 0 && res.data) {
          setLoading(false);
          const obj: any = {
            attachmentName: filename,
            attachmentSize: filesize,
            fileType: 'webm',
            fileFormatType: fileFormatType,
            mediaType: fileFormatType === 'library_file_type_audio' ? 1 : 2,
            attachmentId: res.data.id,
            attachmentUrl: res.data.filePath,
            visitPath: res.data.visitPath,
            mediaDuration: res.data.duration,
          };
          if (data.type === 'mp3') {
            setAudioData([...audioData, obj]);
          } else {
            setVideoData([...videoData, obj]);
          }
        } else {
          setLoading(false);
          message.info('音视频上传文库失败');
        }
      })
      .catch((e: any) => {
        console.log('err: ', e);
        setLoading(false);
        message.info('音视频上传文库失败');
      });
  };
  //发送和重置
  const [messageApi, contextHolder] = message.useMessage();
  const [loading, setLoading] = useState<boolean>(false);
  const [tip, setTip] = useState<string>('加载中');
  //发邮件
  const sendMail = () => {
    setTip('邮件发送中，请稍后...');
    setLoading(true);
    const path = sendMailType === 1 ? sendEmailForInside : sendEmailForOuter;
    const obj: MailBookReq = {
      ...editData,
      senderId: userInfo?.id as string,
      senderAddress: userInfo?.email as string,
      senderName: userInfo?.realName as string,
      mailContent: content,
      mailTitle: title,
      addressList: convertAddress(selectData1),
      addressGroupList: convertGroup(selectData2),
      copyAddressList: convertAddress(selectData3),
      copyAddressGroupList: convertGroup(selectData5),
      secretAddressList: convertAddress(selectData5),
      mailChatAttachVOList: convert(imageData, fileData, audioData, videoData),
    };
    path(obj).then((res: any) => {
      setLoading(false);
      if (res.data) {
        close();
        rest();
        messageApi.open({
          type: 'success',
          content: res.data,
        });
      } else {
        messageApi.open({
          type: 'error',
          content: '邮件发送失败请稍后重发',
        });
      }
    });
  };
  //初始化
  const rest = () => {
    setSelectData1([]);
    setSelectData2([]);
    setSelectData3([]);
    setSelectData4([]);
    setSelectData5([]);
    setImageData([]);
    setFileData([]);
    setVideoData([]);
    setAudioData([]);
    setSendMailType(0);
    setDataSource(addressBookList);
    setTitle('');
    setContent('');
    setEditData(emptyData);
    restChat();
  };
  useEffect(() => {
    return () => {
      rest();
    };
  }, []);

  return (
    <div className={styles.editCard} id={`div-${editData.id}`}>
      <Spin tip={tip} size="small" spinning={loading}>
        {contextHolder}
        <Flex vertical gap={5}>
          <Flex className={styles.itemPar}>
            <span className={styles.editSpan}>
              <span className={styles.textSpan}>收件人</span>:
            </span>
            <div className={styles.editBox}>{renderItems(selectData1, selectData2)}</div>
            <Button
              size="small"
              type="primary"
              ghost
              onClick={() => {
                setSelectAddressType(1);
                setOpenAddressBook(true);
              }}
            >
              选择
            </Button>
          </Flex>
          <Flex className={styles.itemPar}>
            <span className={styles.editSpan}>
              <span className={styles.textSpan}>标题</span>:
            </span>
            <div className={styles.editBox} onClick={() => handleShowInput('title')}>
              {isVisibleTitle ? (
                <Input
                  onChange={onChangeTitle}
                  value={title}
                  onBlur={handleBlurTitle}
                  ref={inputRefTitle}
                  maxLength={80}
                  placeholder="请输入标题"
                  variant="borderless"
                ></Input>
              ) : (
                <span>{title}</span>
              )}
            </div>
          </Flex>
          <Flex className={styles.itemPar}>
            <span className={styles.editSpan}>
              <span className={styles.textSpan}>正文</span>:
            </span>
            <div className={styles.editBox} onClick={() => handleShowInput('content')}>
              <TextArea
                onChange={onChangeContent}
                value={content}
                ref={inputRefContent}
                placeholder="请输入正文"
                variant="borderless"
                autoSize={{ minRows: 1, maxRows: 999999 }}
              ></TextArea>
              {imageData.length > 0 && rendImageItems()}
              {videoData.length > 0 && rendVideoItems()}
              {audioData.length > 0 && rendAudioItems()}
            </div>
            <Button
              size="small"
              type="primary"
              ghost
              onClick={() => {
                setVisible(true);
              }}
            >
              选择
            </Button>
          </Flex>
          <Flex className={styles.itemPar}>
            <span className={styles.editSpan}>
              <span className={styles.textSpan}>附件</span>:
            </span>
            <div className={styles.editBox}>{fileData.length > 0 && rendFileItems()}</div>
            <Button size="small" type="primary" ghost onClick={() => setOpenSelectFile(true)}>
              选择
            </Button>
          </Flex>
          <Flex className={styles.itemPar}>
            <span className={styles.editSpan}>
              <span className={styles.textSpan}>抄送</span>:
            </span>
            <div className={styles.editBox}>{renderItems(selectData3, selectData4)}</div>
            <Button
              size="small"
              type="primary"
              ghost
              onClick={() => {
                setSelectAddressType(2);
                setOpenAddressBook(true);
              }}
            >
              选择
            </Button>
          </Flex>
          <Flex className={styles.itemPar}>
            <span className={styles.editSpan}>
              <span className={styles.textSpan}>密送</span>:
            </span>
            <div className={styles.editBox}>{renderItems(selectData5, [])}</div>
            <Button
              size="small"
              type="primary"
              ghost
              onClick={() => {
                setSelectAddressType(3);
                setOpenAddressBook(true);
              }}
            >
              选择
            </Button>
          </Flex>
          <Flex className={styles.itemPar}>
            <span className={styles.editSpan}>
              <span className={styles.textSpan}>保密等级</span>:
            </span>
            <div className={`${styles.editBox} ${styles.fontColor}`}>
              {`${baomidengji}` + ' ' + `${editData.timedTime}`}
            </div>
            <Button size="small" type="primary" ghost disabled>
              选择
            </Button>
          </Flex>
          <Flex className={styles.itemParSend}>
            <span onClick={sendMail}>发送邮件</span>
          </Flex>
        </Flex>
      </Spin>
      <Popup visible={visible} position="bottom" round onClose={() => setVisible(false)}>
        <List
          size="large"
          header={null}
          footer={footer}
          bordered
          dataSource={treeData}
          className={styles.contentPopup}
          renderItem={(item) => (
            <List.Item>
              {
                <Button onClick={() => checkContenType(item)} disabled={checkStatus(item.code)}>
                  {item.name}
                </Button>
              }
            </List.Item>
          )}
        />
      </Popup>
      {openSelectPicture && (
        <SelectPicture
          open={openSelectPicture}
          mode="single"
          type={1}
          onClose={() => setOpenSelectPicture(false)}
          onSubmit={handleAddImage}
          defaultValue={imageData.map((item) => item.id)}
        />
      )}
      {openSelectFile && (
        <SelectFile
          open={openSelectFile}
          mode="single"
          type={1}
          onClose={() => setOpenSelectFile(false)}
          onSubmit={handleAddFile}
          defaultValue={fileData.map((item) => item.id)}
        />
      )}
      {openAddressBook && (
        <SelectEmployee
          open={openAddressBook}
          title="通讯录"
          mode="multiple"
          type={selectAddressType === 3 ? 2 : 3}
          selectData={
            selectAddressType === 1
              ? [...selectData1, ...selectData2]
              : selectAddressType === 2
                ? [...selectData3, ...selectData4]
                : selectData5
          }
          onClose={() => setOpenAddressBook(false)}
          onSubmit={(list) => {
            getEmloyeeValue(list);
          }}
          sendMailType={sendMailType}
          dataSource={dataSource}
        />
      )}
      {recording && (
        <Recording
          open={recording}
          title={'语音正文输入'}
          onCancel={() => setRecording(false)}
          onAdd={handleAddRecord}
        ></Recording>
      )}
      {recordVideo && (
        <RecordVideo
          open={recordVideo}
          title={'视频正文输入'}
          onCancel={() => setRecordVideo(false)}
          onAdd={handleAddRecord}
        ></RecordVideo>
      )}
      {voiceCall && (
        <VoiceCall
          open={voiceCall}
          title={'语音通话正文输入'}
          addressData={convertAddress(selectData1)}
          sourceType={1}
          onCancel={() => setVoiceCall(false)}
        ></VoiceCall>
      )}
      {videoCall && (
        <VideoCall
          open={videoCall}
          title={'视频通话正文输入'}
          addressData={convertAddress(selectData1)}
          onCancel={() => setVideoCall(false)}
          sourceType={1}
        ></VideoCall>
      )}
      <FliePreview ref={filePreviewRef} zIndex={1000} />
    </div>
  );
};

export default EditCard;
