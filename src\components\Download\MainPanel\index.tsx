import { MinimizeButton } from '@/components/LoadingList';
import MultiModal from '@/components/MultiModal';
import useCppWebSocketStore from '@/store/useCppWebSocketStore';
import { Button } from 'antd';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import Context from '../Context';
import CreateFolder from './CreateFolder';
import Progress from './Progress';
import SelectFiles from './SelectFiles';
import SelectLocation from './SelectLocation';
import SelectLocationFolder from '@/components/SelectLocationFolder'
import ToolBar from './ToolBar';
import styles from './index.module.less';

interface Props {
  onMinimize?: () => void;
  onCancel?: () => void;
}

const Component = ({ onMinimize, onCancel }: Props) => {
  const progressRef = useRef<{
    download: (path:string) => void;
    pause: () => void;
    resume: () => void;
    abort: () => void;
  }>();
  const [response] = useCppWebSocketStore((state) => [state.response]);
  const [cppsocketstatedie, setCppsocketstatedie] = useState(false);
  const selectLocationRef = useRef<{ getCreatedFolder: () => void }>();
  const SelectLocationFolderRef = useRef<{ open: () => void }>();
  const { useMainPanelCtxStore, useSelectFilesCtxStore, config } = useContext(Context);
    const [mainPanelOpen, selectedFileList, setSelectedFileList, loadingStatus, setLoadingStatus] =
    useMainPanelCtxStore!((state) => [
      state.mainPanelOpen,
      state.selectedFileList,
      state.setSelectedFileList,
      state.loadingStatus,
      state.setLoadingStatus,
    ]);
  const [setSelectFilesOpen] = useSelectFilesCtxStore!((state) => [state.setSelectFilesOpen]);
  const [locationOpen, setLocationOpen] = useState(false);
  const [folderOpen, setFolderOpen] = useState(false);
  useEffect(() => {
    return () => {
      cancel();
      abort();
      useMainPanelCtxStore?.reset();
    };
  }, []);
  const okEnabled = useMemo(() => {
    return selectedFileList.length > 0 && loadingStatus === 'init';
  }, [selectedFileList, loadingStatus]);
  const pauseEnabled = useMemo(() => {
    return loadingStatus === 'loading';
  }, [loadingStatus]);
  const resumeEnabled = useMemo(() => {
    return loadingStatus === 'pause';
  }, [loadingStatus]);
  const abortEnabled = useMemo(() => {
    return loadingStatus === 'loading' || loadingStatus === 'pause';
  }, [loadingStatus]);
  const minimizeEnabled = useMemo(() => {
    return loadingStatus === 'loading' || loadingStatus === 'pause';
  }, [loadingStatus]);
  const closeEnabled = useMemo(() => {
    return loadingStatus === 'init' || loadingStatus === 'complete' || cppsocketstatedie;
  }, [loadingStatus]);
  useEffect(() => {
    if (response.method === 'checkcppsocket' && response.data === 'cppsocketdie') {
      console.log(setSelectedFileList);
      setCppsocketstatedie(true);
      setLoadingStatus('complete');
      setSelectedFileList((selectedFileList: any) => {
        console.log(selectedFileList);
        return selectedFileList.map((item: any) => {
          if (item.status !== 'success') {
            item.status = 'error';
          }
          return item;
        });
      });
    }
  }, [response]);
  const toolBarClick = (type: string) => {
    if (type === 'selectFiles') {
      setSelectFilesOpen(true);
    }
  };
  const createFolderOk = () => {
    setFolderOpen(false);
    selectLocationRef.current?.getCreatedFolder();
  };
  const minimize = () => {
    if (onMinimize) {
      onMinimize();
    }
  };
  const cancel = () => {
    if (onCancel) {
      onCancel();
    }
  };
  const download = (path:string) => {
    setLocationOpen(false);
    setSelectFilesOpen(false);
    progressRef.current?.download(path);
  };
  const pause = () => {
    progressRef.current?.pause();
  };
  const resume = () => {
    progressRef.current?.resume();
  };
  const abort = () => {
    progressRef.current?.abort();
  };
  const title = (
    <div className={styles.title}>
      <div className={styles.left}>
        <span>一键下载</span>
        <span>
          已选择 <b>{selectedFileList.length}</b> 个
        </span>
      </div>
      <div className={styles.right}>
        <MinimizeButton disabled={!minimizeEnabled} onClick={minimize} />
        <Button type="primary" ghost disabled={!closeEnabled} onClick={cancel}>
          关闭
        </Button>
      </div>
    </div>
  );

  return (
    <MultiModal
      layoutGroup={`download_${config?.module}_${config?.type}`}
      layoutClassName="modalRight"
      destroyOnClose={true}
      closable={false}
      title={title}
      open={mainPanelOpen}
      footer={[
        <Button
          key="download"
          type="primary"
          loading={loadingStatus === 'waiting'}
          disabled={!okEnabled}
          onClick={() => SelectLocationFolderRef.current?.open()}
        >
          确认下载
        </Button>,
        <Button key="pause" type="primary" ghost disabled={!pauseEnabled} onClick={pause}>
          暂停下载
        </Button>,
        <Button key="resume" type="primary" ghost disabled={!resumeEnabled} onClick={resume}>
          恢复下载
        </Button>,
        <Button key="abort" type="primary" ghost danger disabled={!abortEnabled} onClick={abort}>
          终止下载
        </Button>,
      ]}
    >
      <ToolBar onClick={toolBarClick} />
      <SelectFiles />
      <Progress ref={progressRef} />
      <SelectLocationFolder ref={SelectLocationFolderRef} title='选择一键下载位置' onConfirm={download} config={config}/>
      {/* {locationOpen && (
        <SelectLocation
          ref={selectLocationRef}
          onCancel={() => setLocationOpen(false)}
          onOk={download}
          onCreateFolder={() => setFolderOpen(true)}
        />
      )}
      {folderOpen && <CreateFolder onCancel={() => setFolderOpen(false)} onOk={createFolderOk} />} */}
    </MultiModal>
  );
};

export default Component;
