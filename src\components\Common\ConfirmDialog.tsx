import { CSSProperties, ReactNode } from 'react';
import { createPortal } from 'react-dom';

interface Props {
  open: boolean;
  title?: ReactNode;
  content: ReactNode;
  contentBorder?: boolean;
  okText?: string;
  onOk: () => void;
  cancelText?: string;
  onCancel: () => void;
  onClose?: () => void;
  closeText?: string;
  showAction?: boolean;
  containerStyle?: CSSProperties;
}

/**
 * 二次确认-对话框组件
 * @param props 参数对象
 * @param props.open 显示、隐藏对话框
 * @param props.title 对话框标题（可选）
 * @param props.content 对话框内容
 * @param props.onOk 确认执行方法
 * @param props.onCancel 取消执行方法
 * @param props.onClose 关闭执行方法
 * @param props.okText 确认按钮文字，默认为‘确认’（可选）
 * @param props.cancelText 确认按钮文字，默认为‘取消’（可选）
 * @param props.closeText 关闭按钮文字，默认为‘关闭’（可选）
 */
export default function ConfirmDialog({
  open,
  title = '',
  closeText = '关闭',
  content,
  showAction = true,
  okText = '确认',
  onOk,
  onCancel,
  onClose = onCancel,
  cancelText = '取消',
  containerStyle,
  contentBorder,
}: Props) {
  return (
    <>
      {open &&
        createPortal(
          <div className={`fixed left-0 top-0 z-[1001] h-full w-full bg-[#0000004d]`}>
            <div
              style={{
                boxShadow: `var(--ant-box-shadow)`,
                ...containerStyle,
              }}
              className="fixed left-1/2 top-1/2 z-50 -translate-x-1/2 -translate-y-1/2 rounded-lg bg-white"
            >
              <div className="flex justify-between p-4">
                <span className={`text-xl font-medium text-indigo-A300`}>{title}</span>
                <button
                  onClick={onClose}
                  className={`rounded border border-indigo-A300 px-4 py-1 text-sm text-indigo-A300`}
                >
                  {closeText}
                </button>
              </div>

              <div className={`${contentBorder ? 'border-y' : ''} border-[#f2f2f2] px-14 py-7`}>
                {content}
              </div>

              {showAction && (
                <div className="flex justify-center">
                  <div className="flex pb-5 pt-2">
                    <button
                      className={`mr-[5px] rounded border bg-indigo-A300 px-5 py-2 text-sm font-medium text-white`}
                      onClick={onOk}
                    >
                      {okText}
                    </button>
                    <button
                      className={`ml-[5px] rounded border border-indigo-A300 px-5 py-2 text-sm font-medium text-indigo-A300`}
                      onClick={onCancel}
                    >
                      {cancelText}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>,
          document.body,
        )}
    </>
  );
}
