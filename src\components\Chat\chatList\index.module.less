.scrollableDiv {
  // height: 100% !important;
  height: calc(100% - 114px);
  overflow: auto;
  // padding: 0 16px;
}
.todoListContainer {
  width: 100%;
  margin: 12px 0;
}
.todoList {
  // :global {
  //   .ant-list-item-meta-description,
  //   .ant-list-item-meta-title div {
  //     overflow: hidden; /* 超出部分隐藏 */
  //     display: -webkit-box; /* 使用 WebKit 的弹性盒模型 */
  //     -webkit-line-clamp: 2; /* 限制在一个块元素显示的文本的行数 */
  //     -webkit-box-orient: vertical;
  //   }
  // }
  li {
    cursor: pointer;
  }
  margin: 8px;
  background: #fff;
  height: calc(100%-88px);
  .leftContent {
    .ant-list-item-meta-avatar {
      margin-inline-end: 8px;
    }
  }
  .titleLine {
    display: flex;
    align-items: baseline;
    .title {
      font-size: 17px;
      font-weight: 500;
      color: #292929;
      overflow: hidden; /* 超出部分隐藏 */
      display: -webkit-box; /* 使用 WebKit 的弹性盒模型 */
      -webkit-line-clamp: 2; /* 限制在一个块元素显示的文本的行数 */
      -webkit-box-orient: vertical;
      flex: 1;
    }
  }
  .contentLine {
    display: flex;
    align-items: end;
    margin-top: 5px;
  }

  .content {
    flex: 1;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }
  .unread {
    color: #fff;
    background: #e53935;
    border-radius: 50%;
    font-size: 13px;
    padding: 0 2px;
  }
  .cirleUnread {
    color: #fff;
    background: #e53935;
    border-radius: 50%;
    font-size: 13px;
    width: 20px;
    line-height: 20px;
    text-align: center;
  }
  .time {
    margin: -1px 0 0 10px;
    color: rgba(0, 0, 0, 0.25);
  }
  .typeTitle {
    padding: 0px 4px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 12px;
  }
  .tag_default {
    border: 1px solid #c5cae9;
    background: #e8eaf6;
    padding: 1px 4px;
    border-radius: 2px;
    color: #3949ab;
  }
  .tag_yellow {
    border: 1px solid #fff3e0;
    background: #fff3e0;
    padding: 1px 4px;
    border-radius: 2px;
    color: #f57c00;
  }
  .tag_red {
    background: #ffebee;
    color: #e53935;
  }
  .tag_purple1 {
    background: #ede7f6;
    color: #5e35b1;
  }
  .tag_purple2 {
    background: #f3e5f5;
    color: #8e24aa;
  }
  .tag_green {
    background: #e0f2f1;
    color: #00897b;
  }
  .tag_blue {
    background: #e3f2fd;
    color: #1e88e5;
  }
  .tag_yellow {
    background: #fff3e0;
    color: #fb8c00;
  }
}
