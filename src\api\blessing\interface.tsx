export interface queryParams {
  userRealName?: Array<string>;
  userAccount?: Array<string>;
  mobile?: Array<string>;
  title?: Array<string>;
  keyWord?: Array<string>;
  contents?: Array<string>;
  calendarTimeStar?: Array<string>;
  calendarTimeEnd?: Array<string>;
}

// 获取备忘祝福列表请求参数
export interface getMBPageParams {
  pageNo: number;
  pageSize: number;
  userId: string;
  queryType: number;
  deleted?: number;
  queryParams?: queryParams;
  querySource?: number; // 查询来源：1-备忘祝福,2-数据银行,3-回收站，4-共享天地
  userGroupId?: string; // 共享群id（当查询来源是4的时候必填）
}

// 新增或修改备忘请求参数
export interface memoParams {
  userId?: string;
  type: number;
  content: string;
  contentJson: string;
  calendarTime?: string;
  id?: string;
}

// 新增祝福请求参数
export interface addBlessParams {
  userId: string;
  type: number;
  receiveUserIds: Array<string> | undefined;
  content: string;
  contentJson: string;
  calendarTime: string;
  firstAddLibraryData?: boolean; // TODO:暂时不传，待和C++联调
  addLibraryFile?: any; // TODO:暂时不传，待和C++联调
}

// 修改祝福请求参数
export interface updateBlessParams {
  id: string;
  type: number;
  receiveUserIds: string[] | undefined;
  content: string;
  calendarTime: string;
}

export interface shareMBParams {
  shareUserId: string;
  sharedUserIds?: string[];
  sharedGroupIds?: string[];
  shareMemoIds?: string[];
  shareBlessIds?: string[];
}

// export type memoListType = {
//   calendarTime: number;
//   content: string;
//   createTime: number;
//   creator: string;
//   id: string;
//   mobile: string;
//   personId: string;
//   readStatus: number;
//   updateTime: number;
//   updater: string;
//   userAccount: string;
//   userId: string;
//   userRealName: string;
// };

export type ListPropsType = {
  isMemo: boolean;
  total: number;
  pageNo: number;
  setPageNo: (page: number) => void;
  pageSize: number;
  setPageSize: React.Dispatch<React.SetStateAction<number>>;
  listData: any;
  loading: boolean;
  onChange: (pagination: any, filters: any, sorter: any, extra: any) => void;
  handleBrowser: (record: any) => void;
};
