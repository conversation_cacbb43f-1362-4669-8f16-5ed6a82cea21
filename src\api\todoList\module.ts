export interface TodoListResProps {
  /*主键 */
  id: number;

  /*发送者用户ID */
  sendUserId: string;

  /*接收者用户ID */
  receiveUserId: number;

  /*接收者对应群ID,可以是多个群 */
  receiveGroupId: number;

  /*数据类型(0-裕邦邮箱...) */
  todoType: number;

  /*数据类型(0-裕邦邮箱...) */
  todoTypeName: string;

  /*业务消息id(前端跳转用) */
  bizMsgId: string;

  /*标题 */
  title: string;

  /*标题2 */
  title2: string;

  /*待办事项内容 */
  content: string;

  /* */
  sendTime: string;

  /* */
  todoTime: string;

  /*标记(右上角标记内容) */
  mark: string;

  markCode: string;
  updateTime: string;
  update_time: string;

  /*状态(false-未读，true-已读) */
  status: boolean;

  /*附加信息1 */
  extra1: string;

  /* */
  createTime: string;

  /*排序id，用于置顶功能 */
  sortId: boolean;

  /*群组头像 */
  groupAvatar: string;

  /*系统默认群 */
  defaultGroup: number;

  /*群名 */
  groupName: string;

  /*真实姓名 */
  realName: string;
  sendUsername: string;

  /*头像地址 */
  avatar: string;
  sendAvatar: string;
  sendRealName: string;
  ignoreMark: number | boolean;
  sendEmail: string;
}
