import { FC, useEffect, useRef, useState } from 'react';
import styles from './index.module.less';

import { getBillPage, getMobileConfigNew, getNewMemberPageNew } from '@/api/account';
import SearchHead from './components/searchHead/index';
import TableContent from './components/tableContent';
import { columns1, columns2, columns3, columns4 } from './data';
interface childProps {
  fromModule?: 'dataBank' | 'recycle' | 'workReview';
}
interface ChildSearchProps {
  setSearchVal: (data: any) => void;
}

const LoginReview: FC<childProps> = ({ fromModule }) => {
  const [tableData, setTableData] = useState([]);
  const [total, setTotal] = useState<number>(0);
  const [queryParams, setQueryParams] = useState<any>({});
  const queryData = (params: any) => {
    setQueryParams({ ...params });
  };
  const [activeIndex, setActiveIndex] = useState<string>('1');
  const searchRef: any = useRef<ChildSearchProps>(null);
  const [tableQuery, setTableQuery] = useState<any>({});

  const getActiveIndex = (index: string) => {
    setActiveIndex(index);
  };
  const getActiveColumns = () => {
    switch (activeIndex) {
      case '1':
        return columns1;
      case '2':
        return columns2;
      case '3':
        return columns3;
      case '4':
        return columns4;
    }
  };
  // 获取数据
  const getActiveData = () => {
    if (!queryParams.pageNo) return;
    const obj = { ...queryParams };
    switch (activeIndex) {
      case '1':
        return getMobileData(obj);
      case '2':
        return getMobileDataOther(obj);
      case '3':
        return getPayData(obj);
      case '4':
        return getAccountData(obj);
      default:
        return;
    }
  };
  // 工作手机
  const getMobileData = async (params: any) => {
    const obj = {
      ...params,
      source: fromModule == 'recycle' ? 2 : 1, // 1-数据银行,2-回收站
      optType: 2, // 配置类型:1-配置备用手机,2-修改工作手机,
    };
    getMobileConfigNew(obj).then((res: any) => {
      setTableData(res.data.list);
      setTotal(res.data.total);
    });
  };
  // 备用手机
  const getMobileDataOther = async (params: any) => {
    const obj = {
      ...params,
      source: fromModule == 'recycle' ? 2 : 1, // 1-数据银行,2-回收站
      optType: 1, // 配置类型:1-配置备用手机,2-修改工作手机,
    };
    getMobileConfigNew(obj).then((res: any) => {
      setTableData(res.data.list);
      setTotal(res.data.total);
    });
  };
  // 支付历史记录
  const getPayData = async (params: any) => {
    const obj = {
      pageNo: params.pageNo,
      pageSize: params.pageSize,
      payStatusType: 1, // 0-未支付，1-非未支付，2-已支付或者已退款
      payChannelCode: params.payChannelCode || '',
      deleted: fromModule == 'recycle' ? 1 : 0, // 删除标识,0-未删除，1-临时删除，2-永久删除,示例值(1)
    };
    getBillPage(obj).then((res: any) => {
      setTableData(res.data.list.filter((e: any) => e.status !== 0));
      setTotal(res.data.list.length);
    });
  };
  // 子账户配置
  const getAccountData = async (params: any) => {
    const obj = {
      ...params,
      source: fromModule == 'recycle' ? 2 : 1, // 1-数据银行,2-回收站
    };
    getNewMemberPageNew(obj).then((res: any) => {
      setTableData(res.data.list);
      setTotal(res.data.total);
    });
  };

  // 页码变了
  const handleChildPage = (page: number) => {
    setQueryParams({ ...queryParams, pageNo: page });
  };

  useEffect(() => {
    getActiveData();
  }, [queryParams]);

  useEffect(() => {
    searchRef.current?.setSearchVal({ ...tableQuery });
  }, [tableQuery]);

  return (
    <>
      <div className={styles.WenKuDaQuanContainer}>
        {/* 搜索菜单栏 */}
        <SearchHead
          ref={searchRef}
          queryData={queryData}
          getActiveIndex={getActiveIndex}
          activeIndex={activeIndex}
          fromModule={fromModule}
        />
        {/* 表格 */}
        <TableContent
          columns={getActiveColumns()}
          data={tableData}
          total={total}
          setTableQuery={setTableQuery}
          onReceivePage={handleChildPage}
        />
      </div>
    </>
  );
};

export default LoginReview;
