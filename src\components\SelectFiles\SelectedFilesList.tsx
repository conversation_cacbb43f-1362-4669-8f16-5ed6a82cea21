import { useMemo, useContext } from 'react';
import { Table, Button, Flex, Space } from 'antd';
import { getColumns } from './columns';
import Context from './Context';
import styles from './index.module.less';

const Component = () => {
  const { useCtxStore, columnFields } = useContext(Context);
  const [selectedList, setSelectedList] = useCtxStore!((state) => [
    state.selectedList,
    state.setSelectedList,
  ]);
  const columns = useMemo(() => {
    return getColumns(false, columnFields).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.render = (value: any, row: any) => {
          return (
            <Space>
              <Button type="link" size="small">
                浏览
              </Button>
              <Button
                ghost
                type="primary"
                size="small"
                onClick={() => {
                  setSelectedList((value: any) =>
                    value.filter((item: any) => item.id !== row.id),
                  );
                }}
              >
                取消
              </Button>
            </Space>
          );
        };
      }
      return item;
    });
  }, [selectedList]);

  return (
    <Flex vertical className={styles.selectedFilesList}>
      <div className={styles.title}>已选文件({selectedList.length})</div>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={selectedList}
        rowKey={'id'}
        scroll={{ y: 163 }}
        pagination={false}
      />
    </Flex>
  );
};

export default Component;
