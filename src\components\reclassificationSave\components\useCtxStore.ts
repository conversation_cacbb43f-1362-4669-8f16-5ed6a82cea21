import { autoCreateSetters, autoUseShallow, create } from '@/store';
import type { Setter } from '@/store';

interface State {
  selectFileList: Record<string, any>[];
  location: string;
  isDownloading: boolean;
}
interface SetState {
  setSelectFileList: Setter;
  setLocation: Setter;
  setIsDownloading: Setter;
}

export default autoUseShallow<State, SetState>(
  create(
    autoCreateSetters<State>({
      selectFileList: [],
      location: '',
      isDownloading: false
    }),
  ),
);
