import { useContext, useState, useMemo, useEffect } from 'react';
import { Space, Button } from 'antd';
import Context from './Context';
import styles from './index.module.less';

const Component = () => {
  const { pageSize, pageNumber, setPageNumber, pageMax, total, carouselRef } = useContext(Context);
  const [prevDisabled, setPrevDisabled] = useState(false);
  const [nextDisabled, setNextDisabled] = useState(false);

  const checkDisable = (number: number) => {
    setPrevDisabled(number <= 1);
    setNextDisabled(number >= pageMax);
  };
  const prev = () => {
    const number = pageNumber - 1;
    setPageNumber(number);
    checkDisable(number);
    carouselRef.current.prev()
  };
  const next = () => {
    const number = pageNumber + 1;
    setPageNumber(number);
    checkDisable(number);
    carouselRef.current.next()
  };

  useEffect(() => {
    checkDisable(1);
  }, [pageMax]);

  return (
    <div className={styles.pagination}>
      <Space>
        <Button disabled={prevDisabled} type="primary" size="small" onClick={prev}>
          上一页
        </Button>
        <span>
          共计 <strong>{Math.ceil(total / pageSize)}</strong> 页
        </span>
        <span>
          当前 <strong>{pageNumber}</strong> 页
        </span>
        <Button disabled={nextDisabled} type="primary" size="small" onClick={next}>
          下一页
        </Button>
      </Space>
    </div>
  );
};

export default Component;
