import { libraryAuth<PERSON><PERSON>her } from '@/api/library';
import { cwsRequest } from '@/store/useCppWebSocketStore';
import useUserStore from '@/store/useUserStore';
import { Progress } from 'antd';
import { add } from 'lodash';
import { forwardRef, useContext, useImperativeHandle } from 'react';
import Context from '../Context';
import styles from './index.module.less';
import { setPercent } from './useCtxStore';

export const Component = forwardRef((props, ref) => {
  useImperativeHandle(ref, () => ({
    download: (path: string) => {
      download(path);
    },
    pause: () => {
      pause();
    },
    resume: () => {
      resume();
    },
    abort: () => {
      abort();
    },
  }));
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [
    selectedFileList,
    setSelectedFileList,
    selectedDataBankUrl,
    downloadFileList,
    setDownloadFileList,
    downloadFileMap,
    setDownloadFileMap,
    filesSize,
    setFilesSize,
    downloadedFilesSize,
    setDownloadedFilesSize,
    setAbortFiles,
    loadingStatus,
    setLoadingStatus,
    location,
  ] = useMainPanelCtxStore!((state) => [
    state.selectedFileList,
    state.setSelectedFileList,
    state.selectedDataBankUrl,
    state.downloadFileList,
    state.setDownloadFileList,
    state.downloadFileMap,
    state.setDownloadFileMap,
    state.filesSize,
    state.setFilesSize,
    state.downloadedFilesSize,
    state.setDownloadedFilesSize,
    state.setAbortFiles,
    state.loadingStatus,
    state.setLoadingStatus,
    state.location,
  ]);
  const download = (path: string) => {
    const downloadFileList = [...selectedFileList];
    if (downloadFileList.length === 0) {
      return;
    }
    setLoadingStatus('waiting');
    libraryAuthCipher({
      bizType: '10',
      questUrl: '/api/put_file',
      method: 'POST',
    })
      .then(({ data }: any) => {
        const downloadFileMap: Record<string, any> = {};
        const downloadFilePathList: any[] = [];
        let filesSize = 0;
        downloadFileList.forEach((file: any) => {
          file.status = 'waiting';
          file.downloadedSize = 0;
          let fileTitle = file.title;
          try {
            if (!file.title.includes('.' + file.fileType)) {
              fileTitle = file.title + '.' + file.fileType;
            }
          } catch (error) {
            console.log(error);
          }
          downloadFileMap[file.id] = file;
          downloadFilePathList.push({
            fileId: file.id,
            filePath: file.filePath,
            fileName: fileTitle,
            secretKey: file.secretKey,
          });
          filesSize = add(filesSize, file.fileSize);
        });
        setDownloadFileMap({ ...downloadFileMap });
        setDownloadFileList([...downloadFileList]);
        setFilesSize(filesSize);
        setDownloadedFilesSize(0);
        setSelectedFileList((prev: any) => [...prev]);
        cwsRequest({
          module: `${config!.module}-${config!.type}`,
          method: 'confirmDownload',
          data: {
            filePathList: downloadFilePathList,
            authCipher: {
              ...data,
              token: useUserStore.getState().accessToken,
              url: config?.module == 'dataBank' ? selectedDataBankUrl : undefined,
            },
            localSavePath: path,
          },
        });

        // 文件量大会发起大量ajax请求，阻塞其它ajax数据交互，暂时注注释。
        /*
        downloadFilePathList.forEach((row) => {
          setDownloadLog({
            fileName: row.title,
            filePath: row.filePath,
            fileType: row.fileType,
            fileSource: row.source,
            securityLevel: row.safeLevel,
            fileSize: row.fileSize,
            downloadType: getDownloadType(config)
          });
        }); 
        */
      })
      .finally(() => {
        setLoadingStatus('loading');
      });
  };
  const pause = () => {
    cwsRequest({
      module: `${config!.module}-${config!.type}`,
      method: 'pauseDownload',
    }).then((res) => {
      if (res.code === 0) {
        Object.values(downloadFileMap).forEach((file: any) => {
          if (file.status === 'waiting' || file.status === 'loading') {
            file._status = 'pause';
          }
        });
        setDownloadFileMap((prev: any) => ({ ...prev }));
        setDownloadFileList((prev: any) => [...prev]);
        setSelectedFileList((prev: any) => [...prev]);
        setLoadingStatus('pause');
      }
    });
  };
  const resume = () => {
    cwsRequest({
      module: `${config!.module}-${config!.type}`,
      method: 'resumeDownload',
    }).then((res) => {
      if (res.code === 0) {
        Object.values(downloadFileMap).forEach((file: any) => {
          if (file.status === 'waiting' || file.status === 'loading') {
            file._status = '';
          }
        });
        setDownloadFileMap((prev: any) => ({ ...prev }));
        setDownloadFileList((prev: any) => [...prev]);
        setSelectedFileList((prev: any) => [...prev]);
        setLoadingStatus('loading');
      }
    });
  };
  const abort = () => {
    cwsRequest({
      module: `${config!.module}-${config!.type}`,
      method: 'abortDownload',
    }).then((res) => {
      if (res.code === 0) {
        let count = 0;
        Object.values(downloadFileMap).forEach((file: any) => {
          if (file.status === 'waiting' || file.status === 'loading') {
            file.status = 'abort';
            file._status = '';
            count++;
          }
        });
        setAbortFiles((prev: any) => prev + count);
        setDownloadFileMap((prev: any) => ({ ...prev }));
        setDownloadFileList((prev: any) => [...prev]);
        setSelectedFileList((prev: any) => [...prev]);
      }
    });
  };

  return (
    <>
      {downloadFileList.length > 0 && (
        <div className={styles.progress}>
          <Progress
            percent={setPercent(downloadedFilesSize, filesSize, loadingStatus)}
            percentPosition={{ align: 'end', type: 'inner' }}
            size={{ height: 20 }}
          />
        </div>
      )}
    </>
  );
});

export default Component;
