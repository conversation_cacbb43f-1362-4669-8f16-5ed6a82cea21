type NullString = string | null;

export interface CommonResponse<T = boolean> {
  code: number;
  data: T;
  msg: string;
}

export interface CreateDocVersionParam {
  /**文档ID */
  docId?: string;
  /** 标题 */
  title: string;
  docFormat: string;
  /**
   * 文档来源
   * 1裕邦编辑器
   * 2网络世界
   *  */
  docSource: number;
  /**文档大小(kb) */
  // size: string;
  securityLevel?: NullString;
  /**富文本内容 */
  content: string;
  maxVersion?: number | string;
  saveAs: boolean;
  htmlContent: string;
}
export interface CreateDocVersionResponse {
  id: string;
  docId: string;
  title: string;
  ownerId: string;
  teamFlag: boolean;
  editorAccount: string;
  editorName: null;
  docFormat: string;
  docSource: number;
  size: string;
  securityLevel: NullString;
  version: number;
  url: string;
  content: string;
  createTime: string;
  htmlContent: string;
}
export interface UpdateDocVersionParam
  extends Omit<CreateDocVersionParam, 'maxVersion' | 'saveAs'> {
  /**历史版本主键ID */
  id: string;
  docId: string;
  /**版本号 */
  version: number;
  /**更新类型(1:每隔几分钟调更新接口;2:版本冲突覆盖最大版本) */
  updateType: 1 | 2;
}
export interface GetDocParam {
  docId: string;
  /**
   * 阅读来源（1-个人，2-共享）,示例值(14925)
   */
  docFrom?: number;
  version?: string;
  /**
   * 数据删除标识（0未删除（数据银行），1临时删除(放入回收站)，2永久删除）
   */
  deleted?: number;
  /**工作回顾传userId */
  workReviewUserId?: string;
}

export interface Doc {
  id: string;
  title: string;
  ownerId: number;
  editorAccount: string;
  editorName: string;
  docFormat: string;
  docSource: number;
  size: string;
  securityLevel: NullString;
  version: number;
  url: string;
  content: string;
  teamFlag: boolean;
  shareFlag: boolean;
  forceFlag: boolean;
  createTime: string;
  maxVersion: number;
  htmlContent: string;
}

// 响应接口
export interface GetUserStatusRes {
  /*在线人数 */
  onlineUserCount: number;

  /*总人数 */
  totalUserCount: number;

  /*当前编辑人姓名 */
  currentEditor: string;

  /*当前编辑人Id */
  currentEditorId: string;

  /*用户状态集合 */
  userStatusList: {
    /*成员ID */
    memberId: number;

    /*用户姓名 */
    memberName: string;

    /*成员头像 */
    memberAvatar: string;

    /*成员账号 */
    memberAccount: string;

    /*用户状态(1：待接受，2：接受，3：拒绝，4退出 5在线 6离线) */
    acceptStatus: number;
  }[];
}

export interface StartShareParam {
  docId: string;
  userIds?: string[];
  groupIds?: string[];
}

// 参数接口
export interface QuitEditParams {
  /*历史版本主键ID */
  id: string;

  /*文档ID */
  docId: string;

  /*标题 */
  title: string;

  /*文档格式 */
  docFormat: string;

  /*文档来源(1：裕邦编辑器，2：网络世界) */
  docSource: number;

  /*保密等级 */
  securityLevel?: NullString;

  /*版本号 */
  version: number;

  /*富文本内容 */
  content: string;

  htmlContent: string;

  /*更新类型(1:每隔几分钟调更新接口;2:版本冲突覆盖最大版本) */
  updateType: string;
}

export interface AgreeApplyParams {
  /*历史版本主键ID */
  id: string;

  /*文档ID */
  docId: string;

  /*标题 */
  title: string;

  /*文档格式 */
  docFormat: string;

  /*文档来源(1：裕邦编辑器，2：网络世界) */
  docSource: number;

  /*保密等级 */
  securityLevel?: string;

  /*版本号 */
  version: number;

  /*富文本内容 */
  content: string;

  /*更新类型(1:每隔几分钟调更新接口;2:版本冲突覆盖最大版本) */
  updateType: string;

  /*申请者用户id */
  applyUserId: string;

  htmlContent: string;
}

// 参数接口
export interface RejectApplyParams {
  /*申请者用户id */
  applyUserId: string;

  /*文档ID */
  docId: string;
}

export interface SyncShareParam {
  docId: string;
  content: string;
  extra?: string;
}

export interface QeuryFileListParam {
  pageNo: number;
  pageSize: number;
  keyWord?: string[];
}
export interface shareHistoryParam extends QeuryFileListParam {
  // 文档id
  docId: number | string;
}
// 共享天地-文档列表
export interface shareParam extends QeuryFileListParam {
  // 群id
  groupId?: number | string;
}

// 发起审批-保存信息
interface approveParam {
  /*类型编码 */
  typeCode: string;

  /*文章主图地址 */
  mainImage?: string;

  /*文章缩略内容 */
  shortContent?: any;

  /*内容 */
  content: string;
  //标题
  title?: string;
}

export interface VersionParam {
  // 文档id
  docId: number | string;
  pageNo: number;
  pageSize: number;
}

export interface DelParam {
  docIds?: any[]; // 文档ids不传时，删除全部
  deleteType: 0 | 1 | 2; //删除类型 0取消删除 1 数据银行删除 2 回收站删除
  docSource?: number; // 1：裕邦编辑器，2：网络世界 不传是所有
}

export interface WordConvertRes {
  /*文库大全ID */
  id: string;

  /*标题 */
  title: string;

  /*用户ID */
  userId: number;

  /*账号 */
  userName: string;

  /*姓名 */
  realName: string;

  /*文件大小 （单位 B） */
  fileSize: number;

  /*创建时间 */
  creteTime: Record<string, unknown>;

  /*存储连接 */
  visitPath: string;

  filePath: string;
}
