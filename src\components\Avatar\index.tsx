import avatarPng from '@/assets/images/account/avatar.png';
import groupAvatarPng from '@/assets/images/account/groupAvatar.png';
import { getWidth } from '@/utils/common';
import { useMemo } from 'react';
import styles from './Avatar.module.less';
import AvatarProps from './interface';
const Avatar = ({ userName, Avatar, isUser, fontSize }: AvatarProps) => {
  const rander = useMemo(() => {
    if ((Avatar && typeof userName === 'string') || !isUser) {
      return (
        <div>
          <img
            width={'100%'}
            src={Avatar}
            onError={(e: any) => {
              e.target.src = isUser ? avatarPng : groupAvatarPng;
            }}
          />
        </div>
      );
    } else if (userName && typeof userName === 'string') {
      return (
        <div>
          <span style={{ fontSize: fontSize ? getWidth(fontSize) + 'px' : undefined }}>
            {userName.slice(-2)}
          </span>
        </div>
      );
    } else {
      return (
        <div>
          <img src={isUser ? avatarPng : groupAvatarPng} />
        </div>
      );
    }
  }, [userName, Avatar]);
  return <div className={styles.AvatarBox}>{rander}</div>;
};

export default Avatar;
