.title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: start;
  .left {
    width: 45%;
    display: flex;
    height: 32px;
    line-height: 32px;
    align-items: baseline;

    :global {
      span:last-child {
        color: initial;
        margin-left: 10px;
        font-size: 14px;
        b {
          color: var(--ant-color-error);
          font-weight: normal;
        }
      }
    }
  }
  .right {
    display: flex;
    align-items: start;
    :global {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }
}

.toolBar {
  padding: 16px;

  .text {
    color: var(--ant-color-primary);
  }
}

.list {
  margin: 0 8px;

  :global {
    .ant-table-tbody-virtual .ant-table-tbody-virtual-scrollbar-horizontal {
      display: none;
    }
  }
}

.success {
  color: var(--ant-color-success);
}

.uploading {
  color: var(--ant-color-primary);
}

.waiting,
.pause {
}

.error,
.abort {
  color: var(--ant-color-error);
}

.progress {
  border-top: 1px solid #efefef;
  text-align: center;
  padding: 8px;
}
.SuccessModal {
  position: absolute;
  top: 15%;
  left: 50%;
  transform: translateX(-50%);
  width: 380px;
  border-radius: 10px;
  background: #ffffff;
  overflow: hidden;
  z-index: 100000;

  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  .headerBox {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: end;
    background: #e8eaf6;
    span {
      width: 70px;
      height: 32px;
      border-radius: 4px;
      opacity: 1;
      /* 自动布局 */
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      /* 靛蓝-Indigo/Indigo-A300 */
      border: 1px solid #3d5afe;
      margin-right: 20px;
      color: #3d5afe;
      cursor: pointer;
    }
  }
  .contentBox {
    font-size: 18px;
    color: #3d3d3d;
    padding: 28px 0;
    text-align: center;
  }
  .footerBoxs{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    padding-top: 12px;
    padding-bottom: 28px;
    button{
      width: 120px;
      height: 40px;
    }
  }
}
