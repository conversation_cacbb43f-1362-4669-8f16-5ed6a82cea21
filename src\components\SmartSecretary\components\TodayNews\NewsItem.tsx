import { DetailByIdRes } from '@/api/officialSite/types';
import { Button, List } from 'antd';
import { FC } from 'react';
import styles from './index.module.less';

const NewsItem: FC<{
  articleItem: DetailByIdRes;
  changeActiveTab: (
    key: string,
    item?: any,
    articleId?: string,
    pageType?: string,
    tabItem?: any,
  ) => void;
}> = ({ articleItem, changeActiveTab }) => {
  const formatRelativeTime = (dateString: string): string => {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date string');
    }

    const now = new Date();

    const startOfDay = (d: Date) => new Date(d.getFullYear(), d.getMonth(), d.getDate());
    const normalizedDate = startOfDay(date);
    const normalizedNow = startOfDay(now);

    const diffInMilliseconds = normalizedNow.getTime() - normalizedDate.getTime();
    const diffInDays = Math.floor(diffInMilliseconds / (1000 * 60 * 60 * 24));

    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const timePart = `${hours}:${minutes}`;

    if (diffInDays === 0) {
      return `${timePart}`;
    } else if (diffInDays === 1) {
      return `昨天 ${timePart}`;
    } else if (diffInDays === 2) {
      return `前天 ${timePart}`;
    } else {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are zero-based
      const day = date.getDate().toString().padStart(2, '0');

      // Calculate the difference in years
      const diffInYears = now.getFullYear() - year;

      if (diffInYears > 0 || year < now.getFullYear()) {
        return `${year}-${month}-${day} ${timePart}`;
      } else {
        return `${month}-${day} ${timePart}`;
      }
    }
  };

  const goPage = (key: string, articleId?: string) => {
    changeActiveTab(key, null, articleId, 'todayNews');
  };

  return (
    <>
      <List.Item className={styles.todayNewsItem}>
        <List.Item.Meta
          avatar={
            articleItem.visitableMainImage ? (
              <img className={styles.avatar} src={articleItem.visitableMainImage} alt="" />
            ) : (
              <div></div>
            )
          }
          title={
            <div className={styles.titleBox}>
              <div
                className={styles.titleText}
                style={{
                  width: articleItem.visitableMainImage ? '230px' : '330px',
                }}
              >
                {articleItem.title}
              </div>
              <Button
                type="primary"
                style={{
                  width: '44px',
                  height: '28px',
                  borderRadius: '4px',
                  backgroundColor: '#3D5AFE',
                  marginRight: '5px',
                }}
                onClick={() => goPage('articleDetails', articleItem.id)}
              >
                浏览
              </Button>
            </div>
          }
          description={
            <div className={styles.descriptionBox}>
              <div
                className={styles.descriptionText}
                style={{
                  width: articleItem.visitableMainImage ? '230px' : '330px',
                }}
              >
                {articleItem.shortContent}
              </div>
              <div className={styles.descriptionTime}>
                {articleItem.createTime ? formatRelativeTime(articleItem.createTime) : ''}
              </div>
            </div>
          }
        ></List.Item.Meta>
      </List.Item>
    </>
  );
};

export default NewsItem;
