//分享文件
import MultiModal from '@/components/MultiModal';
import SelectLocationFolder from '@/components/SelectLocationFolder';
import useCppWebSocketStore, { cwsRequest } from '@/store/useCppWebSocketStore';
import { formatFileSize } from '@/utils/common';
import { Button, Input, Modal, Progress } from 'antd';
import { FC, useContext, useEffect, useRef, useState } from 'react';
import Context from '../Context';
import styles from './index.module.less';
interface downloadSoftWareProps {
  openModal: boolean;
  onClose: () => void;
  cancel?: () => void;
}
const File: FC<downloadSoftWareProps> = ({ openModal, onClose, cancel }) => {
  const { useMainPanelCtxStore, config } = useContext(Context);
  const SelectLocationFolderRef = useRef<{ open: () => void }>();
  const [selectedFileList, setSelectedFileList, selectedFileMap, setSelectedFileMap] =
    useMainPanelCtxStore!((state) => [
      state.selectedFileList,
      state.setSelectedFileList,
      state.selectedFileMap,
      state.setSelectedFileMap,
    ]);
  const [locationName, setLocationName] = useState<string>('');
  const [locationOpen, setLocationOpen] = useState(false);
  const selectLocationRef = useRef<{ getCreatedFolder: () => void }>();
  const [folderOpen, setFolderOpen] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isDisplay, setIsDisplay] = useState<boolean>(false);
  const [response] = useCppWebSocketStore((state) => [state.response]);
  const [resId, setResId] = useState<number>(0);
  const savecancel = () => {
    if (cancel) {
      cancel();
    }
  };
  const onOK = (data: any) => {
    setLocationName(data);
    setLocationOpen(false);
  };
  const ok = () => {
    SelectLocationFolderRef.current?.open();
  };
  useEffect(() => {
    if (response.data && response.id === resId) {
      const obj = JSON.parse(response.data);
      setProgress(Math.ceil(((obj.success + obj.failed) / obj.total) * 100));
      if (obj.total === obj.success + obj.failed) {
        Modal.confirm({
          title: '',
          centered: true,
          icon: null,
          cancelButtonProps: { style: { display: 'none' } },
          okButtonProps: { style: { marginTop: 30, background: '#3D5AFE' } },
          content: (
            <div>
              <p className="mb-30 text-lg font-bold">重新分类成功！</p>
              <p className="mb-30 mt-5 text-sm">
                分类文件总数 <span className="text-[#3D5AFE]">{obj.total}</span>个（成功分类
                {obj.success} 个，失败分类{obj.failed}
                个文件，成功分类文件共计{formatFileSize(obj.usedSize, 0)}）
              </p>
            </div>
          ),
          onOk() {
            onClose();
            setLocationName('');
            setResId(0);
            savecancel();
            // setChangeData((val: boolean) => !val);
          },
        });
      }
    }
  }, [response, resId]);
  const confirm = () => {
    if (locationName) {
      setIsDisplay(true);
      const data = selectedFileList.map((item: any) => {
        return {
          path: item.filePath,
          type: item.fileFormatType,
          fileSize: item.fileSize,
        };
      });
      cwsRequest({
        module: 'desktopFile',
        method: 'reclassify',
        data: {
          path: [locationName],
          fileList: data,
        },
      })
        .then((res: any) => {
          if (res.data) {
            const obj = JSON.parse(res.data);
            setResId(res.id);
            // setProgress(((obj.success + obj.failed) / obj.total) * 100);
            if (res.code !== 0) {
              return;
            }
            // if (obj.total === obj.success + obj.failed) {
            //   Modal.confirm({
            //     title: '',
            //     centered: true,
            //     icon: null,
            //     cancelButtonProps: { style: { display: 'none' } },
            //     okButtonProps: { style: { marginTop: 30, background: '#3D5AFE' } },
            //     content: <p className="mb-30 text-lg">重新分类成功！</p>,
            //     onOk() {
            //       onClose();
            //       setLocationName('');
            //       savecancel();
            //       setChangeData((val: boolean) => !val);
            //     },
            //   });
            // }
          }
        })
        .catch((e: any) => {
          console.log('err: ', e);
        });
    }
  };
  //取消
  const cancelClick = () => {
    if (response.id) {
      cwsRequest({
        module: 'desktopFile',
        method: 'cancleTask',
        data: {
          id: response.id,
        },
      })
        .then((res: any) => {
          if (res.code !== 0) {
            return;
          } else {
            onClose();
            setLocationName('');
          }
        })
        .catch((e) => {
          console.log('err: ', e);
        });
    } else {
      onClose();
    }
  };
  return (
    <div>
      <MultiModal
        layoutClassName="normal"
        width={600}
        open={openModal}
        title={
          <>
            <div className={styles.ModalTitle}>
              <div className={styles.header}>
                <div className={styles.headerTitle}>重新分类保存(裕邦操作系统版)</div>
                <div>
                  <Button onClick={cancelClick} className={styles.menubtn}>
                    关闭
                  </Button>
                </div>
              </div>
            </div>
          </>
        }
        closable={false}
        // onOk={() => {}}
        onCancel={onClose}
        footer={[
          <div className={styles.downloadBtn} key={'save'}>
            <Button type="primary" onClick={confirm}>
              确认
            </Button>
            <Button type="primary" ghost onClick={cancelClick} style={{ marginLeft: '10px' }}>
              取消
            </Button>
          </div>,
        ]}
      >
        <div className={styles.file}>
          <div className={styles.fileSelect}>
            <div className={styles.filePosition}>分类保存文件夹位置：</div>
            <div onClick={ok}>
              <Input
                placeholder="请选择安装位置"
                variant="filled"
                style={{ width: '300px' }}
                value={locationName}
                disabled={Boolean(locationName)}
              />
            </div>
          </div>
        </div>
        {isDisplay && (
          <Progress
            style={{ padding: '0px 30px 20px 30px' }}
            percent={progress}
            percentPosition={{ align: 'center', type: 'inner' }}
            size={['', 20]}
          />
        )}
        <SelectLocationFolder
          ref={SelectLocationFolderRef}
          title="选择保存位置"
          onConfirm={onOK}
          config={config}
        />
        {/* 查询下载地址 */}
      </MultiModal>
    </div>
  );
};
export default File;
