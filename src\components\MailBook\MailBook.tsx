import { getDataByResult } from '@/api/addressBook/index';
import { deepType } from '@/api/mail/chat/mailModels';
import useUserStore from '@/store/useUserStore';
import { getWidth } from '@/utils/common';
import { AutoComplete, AutoCompleteProps, Button, Form, message, Table } from 'antd';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { MyContent, MyTitle } from '../Serach';
import AddFriendModal from './AddFriendModal';
import { userCol } from './columns';
import styles from './MailBook.module.less';
export interface AddressBookData {
  id: string;
  contactName: string;
  username: string;
  email: string;
  mobile: string;
  userId: string;
  contactType: number; //1内部 2外部
  userType?: number; // 账号类型: 1母账号, 2子账号, 0官方账号
  avatar?: string;
}

interface ModalProps {
  onClose: (value: boolean) => void;
  onAdd: (data: AddressBookData[]) => void;
  getContainer?: HTMLElement | null;
  hideFooter?: boolean; // 是否隐藏底部
  notAbsolute?: boolean; // 是否取消组件的绝对定位
  selectDataList?: AddressBookData[]; //已选择数据回显
  disSelectedSelf?: boolean;
  isAllContact?: boolean;
  statusList?: number[];
  editType?: deepType;
  customTitle?: {
    subheading: string;
    mainheading: string;
  };
}

// 标题栏
const Title = ({ total, onClose, mainheading }: any) => {
  return (
    <div className={styles.title}>
      <div className={styles.leftTitle}>
        <p>{mainheading}</p>
        <span>
          共计<a>{total}</a>个
        </span>
      </div>
      <Button onClick={onClose}>关闭</Button>
    </div>
  );
};

// 搜索栏
const SearchBar = forwardRef(({ onSearch, setOpenAdd, isAllContact }: any, ref) => {
  const inputRef = useRef<any>(null);
  const [resultKeywords, setResultKeywords] = useState<string[]>([]); // 结果查询列表
  const [form] = Form.useForm();
  const [formData, setFormData] = useState<any>({ value: '' });
  const [options, setOptions] = useState<AutoCompleteProps['options']>([]);
  const optionWidth = getWidth(560);

  useImperativeHandle(ref, () => {
    return {
      resultKeywords,
      resetInput,
    };
  }, [resultKeywords]);
  /**
   * 全部查询
   */
  const searchAll = () => {
    resetInput();
    onSearch(true, []);
  };

  const resetInput = () => {
    setFormData({ value: '' });
    setResultKeywords([]);
  };

  const searchResult = () => {
    if (formData.value) {
      const newResultKeywords = [formData.value, ...resultKeywords];
      setResultKeywords(newResultKeywords);
      onSearch(true, newResultKeywords).then(() => {
        resetInput();
      });
    } else {
      onSearch(true, resultKeywords).then(() => {
        resetInput();
      });
    }
  };
  const searchCurrent = (data?: any) => {
    if (data) {
      const newResultKeywords = [data, ...resultKeywords];
      onSearch(true, newResultKeywords);
    } else {
      onSearch(true, resultKeywords);
    }
  };
  const handleClickAdd = () => {
    setOpenAdd(true);
  };

  // 优化清空输入框，还展示下拉选项问题
  const timeOutFn = (fn: any) => {
    setTimeout(() => {
      fn();
    }, 1000);
  };
  //获取焦点
  const onFocusFn = () => {
    if (formData.value) {
      searchForm(formData.value);
    } else {
      setOptions([]);
    }
  };
  //输入搜索
  const onChange = (data: string) => {
    setFormData({ value: data });
    if (!data?.trim()) {
      return timeOutFn(() => setOptions([]));
    }
    querySelectFn(data);
    querySelectFn1(data);
  };

  // 下拉搜索选择
  const querySelectFn = useCallback(
    debounce((data) => {
      searchForm(data);
    }, 500),
    [],
  );
  const querySelectFn1 = useCallback(
    debounce((data) => {
      searchCurrent(data);
    }, 500),
    [],
  );
  // 条件查询 .
  const searchForm = (data: string) => {
    const params: any = {
      pageNo: 1,
      pageSize: 10,
      blackType: 1,
      keyWordList: data,
    };
    if (!isAllContact) {
      params.contactType = 1;
    }
    getDataByResult(params).then((res: any) => {
      const list = res.data.list;
      setOptions(list);
    });
  };

  const onSelect = (data: any) => {
    console.log(data, formData.value);
    const value = data?.contactName?.includes(formData.value)
      ? data.contactName
      : data?.username?.includes(formData.value)
        ? data.username
        : data?.mobile?.includes(formData.value)
          ? data.mobile
          : data?.email;
    setFormData({ value: value });
    searchCurrent(value);
  };

  const handleKeyDown = (e: any) => {
    if (e.keyCode === 13 || e.code === 'Enter' || e.key === 'Enter') {
      searchCurrent(e.target.value);
    }
  };

  return (
    <div className={styles.serach}>
      <Form layout={'inline'} form={form}>
        <Form.Item label="" style={{ width: `${getWidth(176)}px` }}>
          <AutoComplete
            maxLength={20}
            placeholder="请输入姓名/裕邦账号"
            popupMatchSelectWidth={false}
            value={formData.value}
            onSelect={(e) => onSelect(e)}
            onChange={onChange}
            onFocus={() => onFocusFn()}
            onInputKeyDown={handleKeyDown}
          >
            {options?.map((e, index) => {
              return (
                <AutoComplete.Option key={index} value={e} style={{ width: optionWidth }}>
                  {index === 0 && <MyTitle />}
                  <MyContent
                    data={{
                      contactName: e.contactName,
                      username: e.username,
                      isFriend: e.isFriend,
                      mobile: e.mobile,
                      email: e.email,
                    }}
                  />
                </AutoComplete.Option>
              );
            })}
          </AutoComplete>
        </Form.Item>
        <Form.Item label="">
          <Button type="primary" onClick={searchAll}>
            全部查询
          </Button>
        </Form.Item>
        <Form.Item label="">
          <Button type="primary" onClick={searchResult}>
            结果查询
          </Button>
        </Form.Item>
      </Form>
      <Button type="primary" onClick={handleClickAdd}>
        添加
      </Button>
    </div>
  );
});

type SearchBarRef = {
  resultKeywords: string[];
  resetInput: () => void;
};
const AddressBook = forwardRef(
  (
    {
      selectDataList = [],
      statusList = [0, 0, 0, 0, 0],
      onClose,
      onAdd,
      hideFooter,
      notAbsolute = false,
      isAllContact = false,
      getContainer,
      editType,
      customTitle = {
        subheading: '已选好友',
        mainheading: '通讯录',
      },
      disSelectedSelf,
    }: any,
    ref,
  ) => {
    const [userInfo, setAddressData] = useUserStore((state) => [
      state.userInfo,
      state.setAddressData,
    ]);
    const [addressBookDataList, setAddressBookDataList] = useState<AddressBookData[]>([]);
    const [selectData, setSelectData] = useState<AddressBookData[]>(selectDataList);
    const [loading, setLoading] = useState<boolean>(false);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [total, setTotal] = useState<number>(0);
    const [hasMore, setHasMore] = useState<boolean>(true);
    const [openAdd, setOpenAdd] = useState<boolean>(false);
    const [messageApi, contextHolder] = message.useMessage();
    const searchBarRef = useRef<SearchBarRef>(null);
    const [mailStatus, setMailStatus] = useState<number>(0); //记录内外状态
    const [restStatus, setRestStatus] = useState<boolean>(false); //是否可重置内外状态

    useEffect(() => {
      chooseStatus();
    }, [statusList]);
    //状态是否可重置
    const checkStatusList = (t: number) => {
      for (let i = 0; i < statusList.length; i++) {
        if (i == t) {
          continue;
        }
        if (statusList[i] !== 0) {
          setRestStatus(false);
          break;
        }
        setRestStatus(true);
      }
    };

    const chooseStatus = () => {
      if (statusList.includes(1)) {
        setMailStatus(1);
      } else if (statusList.includes(2)) {
        setMailStatus(2);
      } else {
        setMailStatus(0);
      }
    };

    useEffect(() => {
      setSelectData(selectDataList);
    }, [selectDataList]);

    useImperativeHandle(ref, () => {
      return {
        getSelectData: () => selectData, // 获取当前被选择的数据
        resetSelectData,
        setSelectData,
      };
    }, [selectData]);

    const resetSelectData = () => {
      searchBarRef.current?.resetInput();
      setSelectData([]);
      setCurrentPage(1);
    };

    useEffect(() => {
      chooseStatus();
      if (editType) {
        switch (editType.index) {
          case 1:
            checkStatusList(0);
            break;
          case 3:
            checkStatusList(1);
            break;
          case 4:
            checkStatusList(2);
            break;
        }
      }
    }, [editType]);

    const fetchData = (isReload: boolean, keyWordList: string[]) => {
      setLoading(true);
      if (isReload) {
        setCurrentPage(1);
      }
      const params: any = {
        pageNo: isReload ? 1 : currentPage,
        pageSize: 10,
        blackType: 1, // 0全部，1正常，2加入黑名单
        keyWordList: keyWordList.join(),
      };
      if (!isAllContact) {
        params.contactType = 1; // 默认内部联系人
      }
      getDataByResult(params)
        .then((res: any) => {
          const list = res.data.list;
          setTotal(res.data.total);
          setAddressData(list);
          if (isReload || currentPage === 1) {
            setAddressBookDataList(list);
          } else {
            setAddressBookDataList((prevAddressBookDataList) => [
              ...prevAddressBookDataList,
              ...list,
            ]);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    };

    /**
     * 列表滚动事件
     */
    const handleScroll = debounce((e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, clientHeight, scrollHeight } = e.target as HTMLDivElement;
      if (scrollTop + clientHeight >= scrollHeight - 20 && hasMore && !loading) {
        setCurrentPage((prevCurrentPage) => prevCurrentPage + 1);
      }
    }, 300);

    useEffect(() => {
      fetchData(false, searchBarRef.current?.resultKeywords || []);
    }, [currentPage]);

    useEffect(() => {
      if (addressBookDataList.length === total && addressBookDataList.length) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
    }, [total, addressBookDataList]);

    //数据传输给父组件
    const handleClick = () => {
      onAdd(selectData);
    };

    //上表的添加方法
    const handleAdd = (record: AddressBookData) => {
      if (mailStatus == 0) {
        setSelectData([...selectData, record]);
        setMailStatus(record.contactType);
      } else if (mailStatus == record.contactType) {
        setSelectData([...selectData, record]);
      } else {
        messageApi.open({
          type: 'warning',
          content: '不能同时选择内外部联系人',
        });
      }
    };
    //下表的移除方法
    const handleDelete = (record: AddressBookData) => {
      if (setSelectData.length <= 1 && restStatus) {
        setMailStatus(0);
      }
      setSelectData(selectData.filter((item) => item.email !== record.email));
    };
    //上表表结构
    const columns = [
      ...userCol,
      {
        title: '操作',
        key: 'action',
        width: getWidth(70),
        render: (_: any, record: AddressBookData) => (
          <Button
            type="primary"
            disabled={
              !!selectData.find((item) => item.email === record.email) ||
              (record?.userType === 0 && record?.contactType == 1) ||
              (record.userId === userInfo?.id && disSelectedSelf)
            }
            onClick={() => handleAdd(record)}
            className={classNames(
              !selectData.find((item) => item.email === record.email)
                ? styles.active
                : styles.deactive,
            )}
          >
            {selectData.find((item) => item.email === record.email) ? '已选' : '多选'}
          </Button>
        ),
      },
    ];

    //下表表结构
    const columnss = [
      ...userCol,
      {
        title: '操作',
        key: 'action',
        width: getWidth(70),
        render: (_: any, record: AddressBookData) => (
          <Button type="primary" className={styles.active} onClick={() => handleDelete(record)}>
            取消
          </Button>
        ),
      },
    ];

    /**
     * 添加好友成功
     */
    const onAddSuccess = () => {
      setOpenAdd(false);
      searchBarRef.current?.resetInput();
      if (currentPage == 1) {
        fetchData(true, []);
      } else {
        setCurrentPage(1);
      }
    };

    /**
     * 设置行属性
     */
    const rowClassName = (record: any) => {
      return selectData.map((item: any) => item.id).includes(record.id) ? 'row-selected' : '';
    };

    return (
      <>
        {contextHolder}
        <div
          id="address_book"
          className={`${styles.contentBox} ${notAbsolute ? styles.notAbsolute : ''}`}
        >
          <Title total={total} onClose={onClose} mainheading={customTitle.mainheading} />
          <div className={styles.modalBody}>
            <SearchBar
              ref={searchBarRef}
              onSearch={fetchData}
              isAllContact
              setOpenAdd={() => setOpenAdd(true)}
            />
            <div className={styles.table}>
              <Table
                dataSource={addressBookDataList}
                columns={columns}
                pagination={false}
                {...(addressBookDataList.length > 7
                  ? { scroll: { scrollToFirstRowOnChange: false, y: getWidth(280) } }
                  : {})}
                rowKey={'id'}
                rowClassName={rowClassName}
                loading={loading}
                onScroll={handleScroll}
              />
            </div>
            <div className={styles.checkTable}>
              <div className={styles.checkTitle}>
                <span>
                  {customTitle.subheading}({selectData.length})
                </span>
              </div>
              <Table
                dataSource={selectData}
                columns={columnss}
                pagination={false}
                {...(selectData.length > 4 ? { scroll: { y: getWidth(160) } } : {})}
                className="select-table"
                rowKey={'id'}
              />
            </div>
            {!hideFooter && (
              <div className={styles.footer}>
                <Button type="primary" size="small" onClick={handleClick}>
                  {getContainer ? '确认添加' : '确认选择'}
                </Button>
              </div>
            )}
          </div>
          {openAdd && (
            <AddFriendModal
              open={openAdd}
              onCancel={() => setOpenAdd(false)}
              success={onAddSuccess}
            ></AddFriendModal>
          )}
        </div>
      </>
    );
  },
);

interface AddressBookProps {
  getSelectData: () => any[];
  resetSelectData: () => void;
  setSelectData: (data: any) => void;
}

const MailBook = forwardRef(
  (
    {
      onClose,
      onAdd,
      getContainer = null,
      hideFooter,
      notAbsolute = false,
      selectDataList,
      statusList,
      editType,
      customTitle,
      isAllContact = false,
      disSelectedSelf,
    }: ModalProps,
    ref,
  ) => {
    const addressBookRef = useRef<AddressBookProps>();

    useImperativeHandle(ref, () => {
      return {
        getSelectData: () => addressBookRef.current?.getSelectData(), // 获取当前被选择的数据
        resetSelectData: () => addressBookRef.current?.resetSelectData(),
        setSelectData: (data: any[]) => addressBookRef.current?.setSelectData(data),
      };
    });

    return getContainer ? (
      createPortal(
        <AddressBook
          ref={addressBookRef}
          onClose={onClose}
          onAdd={onAdd}
          getContainer={getContainer}
          hideFooter={hideFooter}
          notAbsolute={notAbsolute}
          selectDataList={selectDataList}
          statusList={statusList}
          editType={editType}
          customTitle={customTitle}
          isAllContact={isAllContact}
          disSelectedSelf={disSelectedSelf}
        />,
        getContainer,
      )
    ) : (
      <AddressBook
        ref={addressBookRef}
        onClose={onClose}
        onAdd={onAdd}
        getContainer={getContainer}
        hideFooter={hideFooter}
        notAbsolute={notAbsolute}
        selectDataList={selectDataList}
        statusList={statusList}
        editType={editType}
        customTitle={customTitle}
        isAllContact={isAllContact}
        disSelectedSelf={disSelectedSelf}
      />
    );
  },
);

export default MailBook;
