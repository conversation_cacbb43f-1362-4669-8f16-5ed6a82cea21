import request from '../index';
//获取模块未读数
export const getGroupAppUnreadMsgNum = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/group/getGroupAppUnreadMsgNum',
    data,
  });
};
//获取用户账号和子账号在线状态列表
export const getAllMemberOnline = () => {
  return request.post({
    url: '/web-api/account/manage/getAllMemberOnline',
  });
};
//清除群里的未读数
export const clearGroupUnreadNum = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/group/clearGroupAppUnreadMsgNum',
    data,
  });
};
