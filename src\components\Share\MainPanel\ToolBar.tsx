import FinalTimeFilter from '@/components/Filters/FinalTimeFilter';
import useLibraryStore from '@/store/useLibraryStore';
import { Button, Col, Row, Select, Space } from 'antd';
import { useContext, useMemo } from 'react';
import Context from '../Context';
import styles from './index.module.less';

interface Props {
  onClick?: (type: string) => void;
}

const Component = ({ onClick }: Props) => {
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [safeLevelList] = useLibraryStore((state) => [state.safeLevelList]);
  const [safeLevel, setSafeLevel, enableFinalTime, setEnableFinalTime, setFinalTime] =
    useMainPanelCtxStore!((state) => [
      state.safeLevel,
      state.setSafeLevel,
      state.enableFinalTime,
      state.setEnableFinalTime,
      state.setFinalTime,
    ]);
  const isPreview = useMemo(() => {
    return config?.module === 'preview';
  }, [config]);

  return (
    <div className={styles.toolBar}>
      <Row gutter={8}>
        <Col span={12}>
          <Space>
            <Button type="primary" onClick={() => onClick!('users')}>
              选择分享用户
            </Button>
            <Button type="primary" onClick={() => onClick!('shareFolders')}>
              选择共享群
            </Button>
            {!isPreview && (
              <Button type="primary" onClick={() => onClick!('selectFiles')}>
                {config?.module === 'netWorld'
                  ? `选择分享${config.typeName || '文件'}`
                  : '选择分享文件'}
              </Button>
            )}
          </Space>
        </Col>
        {config?.module !== 'netWorld' && (
          <Col span={12}>
            <div className={styles.right}>
              <span className={styles.label}>选择保密等级：</span>
              <Select
                popupClassName={styles.selectPopup}
                virtual={false}
                placeholder="选择保密等级"
                value={safeLevel}
                fieldNames={{ label: 'label', value: 'key' }}
                options={safeLevelList}
                onChange={(value) => {
                  setSafeLevel(value);
                  const hasFinalTime = value === '4' || value === '5' || value === '7';
                  setEnableFinalTime(hasFinalTime);
                }}
              />
            </div>
          </Col>
        )}
      </Row>
      {enableFinalTime && (
        <Row gutter={8}>
          <Col span={24}>
            <div className={`${styles.right} ${styles.finalTime}`}>
              <span className={styles.label}>选择定时时间：</span>
              <FinalTimeFilter
                onChange={(value) => {
                  setFinalTime(value);
                }}
              />
            </div>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default Component;
