import { getDiaryInfo } from '@/api/diary';
import { libraryAuth<PERSON>ip<PERSON>, previewVerify } from '@/api/library';
import MulitiModal from '@/components/MultiModal';
import { setBrowseLog } from '@/pages/LoginReview/setRecord';
import useAppStore from '@/store/useAppStore';
import { cppbRequest } from '@/store/useCppBridgeStore';
import { cwsRequest } from '@/store/useCppWebSocketStore';
import useFromModuleStore from '@/store/useFromModuleStore';
import useUserStore from '@/store/useUserStore';
import { getRandom, getWidth } from '@/utils/common';
import { shareError } from '@/utils/modal';
import { Spin } from 'antd';
import CryptoJS from 'crypto-js';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import SpinIcon from '../SpinIcon';
import Content from './Content';
import Download from './Download';
import ExpireTime from './ExpireTime';
import styles from './FilePreview.module.less';
import type { File } from './interface';
import Navigate from './Navigate';
import SafeLevel from './SafeLevel';
import Share from './Share';

interface Props {
  zIndex?: number;
  onCancel?: () => void;
}
export interface FilePreviewAPI {
  open: (file: File & { fileSource?: number }) => void;
}

const FilePreview = forwardRef(({ zIndex, onCancel }: Props, ref) => {
  useImperativeHandle(
    ref,
    (): FilePreviewAPI => ({
      open: (file) => {
        console.log('file', file);
        
        previewVerify({
          sharedId: file.userId,
          sharedEsId: file.id,
          fileUrl: CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(file.visitPath!)),
          source: file.source,
        }).then(({ data }: any) => {
          setBrowseLog({ ...file });
          setFile({
            ...file,
            shareExpireTime: data.shareExpireTime,
            verifyValue: data.viewAble,
            verifyText: data.message,
          });
        });
      },
    }),
  );
  const history = useNavigate();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [file, setFile] = useState<File>({});
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [fromModule] = useFromModuleStore((state) => [state.fromModule]);
  const { channel } = useAppStore((state: any) => state);
  const queryDiary = async () => {
    const res: any = await getDiaryInfo(file.bizId);
    console.log('res', res);
    history('/diary', { state: { diaryInfo: res.data } });
  };
  const historyPage = () => {
    switch (file.source) {
      case 5:
        queryDiary();
        break;
      case 6:
        history('/blessing');
        break;
      case 11:
        history('/addressBook');
        break;
      default:
        if (file.bizId && file.version) {
          history('/editor', {
            state: { docId: file.bizId, version: file.version },
          });
        } else {
          setOpen(true);
        }
        break;
    }
  };
  useEffect(() => {
    if (Object.keys(file).length > 0) {
      // 5-我的日记，6-备忘祝福，7-裕邦编辑，8-网络世界，9-分享，10-桌面文件，11-通讯录
      if (!isOnlyRead && file?.fileType == 'ybd' && file?.source !== -1) {
        historyPage();
        return;
      }
      if (channel === 'web') {
        setOpen(true);
        return;
      }
      if (
        !isOnlyRead &&
        file.source !== -1 &&
        (file.fileFormatType === 'library_file_type_doc' ||
          file.fileFormatType === 'library_file_type_worddoc' ||
          file.fileFormatType === 'library_file_type_table') &&
        (file.fileType === 'xlsx' ||
          file.fileType === 'xls' ||
          file.fileType === 'doc' ||
          file.fileType === 'docx' ||
          file.fileType === 'ppt' ||
          file.fileType === 'pptx' ||
          file.fileType === 'pdf' ||
          file.fileType === 'rtf')
      ) {
        setLoading(true);
        downloadFile();
        //setOpen(true);
      } else {
        setOpen(true);
      }
    }
  }, [file, fromModule]);
  const downloadFile = async () => {
    const { data } = await libraryAuthCipher({
      bizType: '10',
      questUrl: '/api/put_file',
      method: 'POST',
    });
    if (!file.title) {
      return false;
    }
    let fileTitle = file.title;
    try {
      if (!file.title.includes('.' + file.fileType)) {
        fileTitle = file.title + '.' + file.fileType;
      }
    } catch (error) {
      console.log(error);
    }
    const res = await cwsRequest({
      module: `library-preview-download`,
      method: 'confirmDownload',
      data: {
        filePathList: [
          {
            fileId: file.id,
            filePath: file.filePath,
            fileName: fileTitle,
            secretKey: file.secretKey,
            fileType: file.fileType,
          },
        ],
        authCipher: {
          ...data,
          token: useUserStore.getState().accessToken,
        },
      },
    });
    if (res.code === 0) {
      console.log(res.data[0].localpath);
      scanBtn(res.data[0].localpath);
      setLoading(false);
    }
  };
  const scanBtn = async (filePath: string) => {
    const response = await cppbRequest({
      module: 'desktopFile',
      id: getRandom(6), // 消息id 随机数
      method: 'openfile',
      data: {
        filePath,
      },
    });
    console.log('cppResponse', response);
    if (response.code !== 0) {
      shareError(response.data.status, response.data.status);
    }
  };
  const isOnlyRead = useMemo(() => {
    return (
      (file.safeLevelName && (file.safeLevelName as string).includes('只读')) ||
      fromModule === 'dataBank'
    );
  }, [file, fromModule]);
  const cancel = () => {
    if (popoverOpen) {
      setPopoverOpen(false);
      setTimeout(() => {
        setOpen(false);
        if (onCancel) {
          onCancel();
        }
      }, 600);
    } else {
      setOpen(false);
      if (onCancel) {
        onCancel();
      }
    }
  };
  const title = (
    <div className={styles.title}>
      <div className={styles.left} title={file.title}>
        {file.title}
      </div>
      <div className={styles.middle}>
        {!!file.safeLevelName && (
          <SafeLevel
            verifyValue={file.safeLevel !== 1 && file.verifyValue}
            safeLevelName={file.safeLevelName}
          />
        )}
        {!!file.shareExpireTime && <ExpireTime expireTime={file.shareExpireTime} />}
      </div>
      {file.verifyValue && (
        <div className={styles.right}>
          {!isOnlyRead && file.source !== -1 && <Download file={file} />}
          {!isOnlyRead && file.source !== -1 && <Share file={file} />}
          {!isOnlyRead && <Navigate file={file} />}
        </div>
      )}
    </div>
  );
  useEffect(() => {
    if (open === false && file.id) {
      setFile({});
    }
  }, [open]);

  return (
    <>
      <Spin
        spinning={loading}
        indicator={<SpinIcon fontSize="48" />}
        tip="文件正在加载！"
        fullscreen
        size="large"
      ></Spin>
      <MulitiModal
        open={open}
        className={styles.FilePreview}
        destroyOnClose={true}
        width={`${getWidth(1152)}px`}
        layoutClassName="normal"
        title={title}
        onCancel={cancel}
        zIndex={zIndex}
      >
        <div className={styles.content}>
          <Content file={file} />
        </div>
      </MulitiModal>
    </>
  );
});

export default FilePreview;
