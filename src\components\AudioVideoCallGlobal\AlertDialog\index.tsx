import MultiModal from '@/components/MultiModal';
import { getWidth } from '@/utils/common';
import { Button } from 'antd';
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import styles from './alertDialog.module.less';
import useAlertDialogStore from './useAlertDialogStore';
const AlertDialog = ({
  open,
  title,
  butText = '确认',
}: {
  open: boolean;
  title: string;
  butText?: string;
}) => {
  const [show, setShow] = useState<boolean>(false);
  useEffect(() => {
    if (open) {
      setShow(true);
    }
  }, [open]);
  return (
    show &&
    createPortal(
      <MultiModal
        width={getWidth(336)}
        layoutClassName="normal"
        title={null}
        top={200}
        open={show}
        closable={false}
      >
        <div className={styles.AlertModalContent}>
          <div className={styles.Mark} />
          <div className={styles.MarkBg} />
          <div className={styles.AlertModalContentTitle}>{title}</div>
          <div className={styles.confirmBut}>
            <Button
              type="primary"
              onClick={() => {
                setShow(false);
                useAlertDialogStore.reset();
              }}
            >
              {butText }
            </Button>
          </div>
        </div>
      </MultiModal>,
      document.body,
    )
  );
};
export default AlertDialog;
