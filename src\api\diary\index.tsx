import request from '../index';
import {
  addDiaryParamsType,
  delDiaryParamsType,
  queryPageParamsType,
  shareDiaryParamsType,
  updateDiaryParamsType,
} from './interface';
const base = '/web-api/diary';

/**
 * 获得用户日记分页
 * @param pageNo
 * @param pageSize
 * @param userNames
 * @param realNames
 * @param titles
 * @param contents
 * @param startTimes
 * @param endTimes
 * @param deletedEnum
 * @returns
 */
export const getDiaryInfoPage = (data: queryPageParamsType) => {
  return request.post({
    url: `${base}/diaryInfo/diaryInfoPage`,
    data,
  });
};
/**
 * 根据标题或者内容获得用户日记分页
 */
export const titleOrContentPage = (data: any) => {
  return request.post({
    url: `${base}/diaryInfo/titleOrContentPage`,
    data,
  });
};

/**
 * 创建用户日记
 * @param diaryDate
 * @param city
 * @param weather
 * @param temperature
 * @param title
 * @param content
 * @returns
 */
export const addDiaryInfo = (data: addDiaryParamsType) => {
  return request.post({
    url: `${base}/diaryInfo`,
    data,
  });
};

/**
 * 修改用户日记
 * @param id
 * @param title
 * @param content
 * @returns
 */
export const updateDiaryInfo = (data: updateDiaryParamsType) => {
  return request.put({
    url: `${base}/diaryInfo`,
    data,
  });
};

/**
 * 获取用户日记详情
 * @param id
 * @returns
 */
export const getDiaryInfo = (id: any) => {
  return request.get({
    url: `${base}/diaryInfo/${id}`,
  });
};

/**
 * 分享日记
 * @param sharedUserIds
 * @param sharedGroupIds
 * @param shareDiaryIds
 * @returns
 */
export const shareDiary = (data: shareDiaryParamsType) => {
  return request.post({
    url: `${base}/diaryInfo/share`,
    data,
  });
};

/**
 * 日记标记已读
 * @param diaryId path
 * @param groupId
 */
export const readDiary = (diaryId: string, groupId: string) => {
  return request.post({
    url: `${base}/diaryShare/read/${diaryId}`,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: { groupId },
  });
};

/**
 * 分享日记
 * @param deletedSource
 * @param deletedType
 * @param deletedAll
 * @param ids
 * @param startTime
 * @param endTime
 * @returns
 */
export const deleteDiary = (data: delDiaryParamsType) => {
  return request.post({
    url: `${base}/diaryInfo/delete`,
    data,
  });
};
