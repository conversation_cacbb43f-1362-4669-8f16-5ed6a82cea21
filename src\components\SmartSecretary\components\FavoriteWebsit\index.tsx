import { checkWebpage, getWebsiteConfigList, getWebsiteData } from '@/api/networld';
import useJavaWebSocketStore from '@/store/useJavaWebSocketStore';
import { FC, useEffect, useState } from 'react';
import FavoriteWebPage from '../FavoriteWebPage';
interface webSitesType {
  websiteUrl: string;
  iconUrl: string;
  title: string;
  id?: string;
}
const FavoriteWebsit: FC<any> = () => {
  const [website, setWebSite] = useState<webSitesType[]>([]);
  const [response] = useJavaWebSocketStore((state) => [state.response]);
  const handleClick = (item: any) => {
    const url = item?.websiteUrl.trim() || '';
    if (!url) return;
    window.open(url.includes('//') ? url : '//' + url);
    const param = {
      webpageId: item.id || '',
    };
    checkWebpage(param).then((res: any) => {});
  };
  const getWebSitData = async () => {
    const result = (await getWebsiteData({
      pageNo: 1,
      pageSize: 1000,
    })) as { data: { list: [] } };
    // 获得推荐网站
    const result1 = (await getWebsiteConfigList({
      pageNo: 1,
      pageSize: 1000,
    })) as { data: { list: [] } };
    setWebSite([...result.data.list, ...result1.data.list]);
  };
  const [current, setCurrent] = useState<number>(1);
  useEffect(() => {
    getWebSitData();
  }, [current]);

  useEffect(() => {
    // 收到推送
    if (response.noticeType === 'NET_WORLD') {
      getWebSitData();
    }
  }, [response]);
  return (
    <>
      <FavoriteWebPage dataList={website} urlType="websiteUrl"></FavoriteWebPage>
    </>
  );
};
export default FavoriteWebsit;
