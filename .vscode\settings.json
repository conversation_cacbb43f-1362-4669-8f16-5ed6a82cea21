{"editor.tabSize": 2, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "always", "source.fixAll": "explicit"}, "prettier.prettierPath": "./node_modules/prettier", "prettier.useEditorConfig": false, "prettier.configPath": "./prettier.config.cjs", "conventionalCommits.scopes": ["home", "editor", "global", "store", "dataBank", "音视频播放器", "audioVideo", "audiovideo", "api", "components", "officialsite"], "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "Codegeex.CommitMessageStyle": "Auto", "Codegeex.GenerationPreference": "line by line", "Codegeex.CompletionDelay": 2, "terminal.integrated.fontFamily": "Hack"}