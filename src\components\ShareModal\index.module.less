.toolBar {
  padding: 16px;
  padding-bottom: 0;
  .level-text {
    color: #4d70fe;
    font-size: 14px;
    margin-left: 20px;
    margin-right: 11px;
  }
  .level-value {
    height: 40px;
    padding: 10px 12px;
    background: #f3f9ff;
    border-radius: 8px;
  }
  .header-select {
    background: #f3f9ff;
    border-radius: 8px;
    font-size: 14px;
    // padding: 10px 12px;
    height: 40px;
    width: 186px;
    border: 0;
  }
  :global {
    .ant-select-selector {
      border: none;
      background: #f3f9ff;
      box-shadow: none;
    }
    .ant-select-selection-search {
      border: none;
    }
  }
}
.list {
  margin: 0 8px;
  :global {
    .ant-table-thead .ant-table-cell {
      background: var(--ant-table-hd-select-color);
    }
  }
}
.title {
  font-size: 16px;
  color: #f4511e;
  padding: 15px 20px;
}
