import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create } from '@/store';
import type { Config } from '../Context';

export interface State {
  shareFoldersOpen: boolean;
  list: Record<string, any>[];
  selectedList: Record<string, any>[];
  selectedMap: Record<string, any>;
  keywords: any[];
  temporaryData: any[];
  queryType: { current: 'all' | 'results' | 'current' };
}
export interface SetState {
  setShareFoldersOpen: Setter;
  setList: Setter;
  setSelectedList: Setter;
  setSelectedMap: Setter;
  setKeywords: Setter;
  setTemporaryData: Setter;
  setQueryType: Setter;
}

const createUseCtxStore = function (config: Config) {
  const useCtxStore = autoUseShallow<State, SetState>(
    create(
      autoCreateSetters<State>({
        shareFoldersOpen: false,
        list: [],
        selectedList: [],
        selectedMap: {},
        keywords: [],
        temporaryData: [],
        queryType: { current: 'current' },
      }),
    ),
  );
  useCtxStore.subscribe((state, prevState) => {
    if (state.queryType === prevState.queryType) {
      return;
    }
    switch (state.queryType.current) {
      case 'all':
        state.setKeywords([]);
        state.setTemporaryData([]);
        break;
      case 'results':
        state.setKeywords((value: any) => [...value, ...state.temporaryData]);
        break;
      case 'current':
        break;
    }
  });
  return useCtxStore;
};

export default createUseCtxStore;
