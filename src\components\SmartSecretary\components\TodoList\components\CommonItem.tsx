/**
 * 待办事项-邮箱
 */
import { dealInvite } from '@/api/audioVideo/videoMeeting';
import { meetingReissue } from '@/api/todoList';
import { TodoListResProps } from '@/api/todoList/module';
import FilePreview, { FilePreviewAPI } from '@/components/FliePreview';
import { Context } from '@/components/SmartSecretary/context';
import VideoCall from '@/components/VideoCall';
import VoiceCall from '@/components/VoiceCall';
import useUserStore from '@/store/useUserStore';
import { getWidth } from '@/utils/common';
import getMediaUrl from '@/utils/getMediaUrl';
import getCamera from '@/webRTC/getCamera';
import { Avatar, Badge, Button, Image, List, message } from 'antd';
import { FC, useContext, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from '../index.module.less';
import useTodoList from '../useTodoList';

const TodoItem: FC<{ data: TodoListResProps; onInit: () => void }> = ({ data, onInit }) => {
  const extra1 = JSON.parse(data.extra1); // 待办事项的额外属性
  const { goPage, getType, updateTodo } = useTodoList(data);
  const { className, typeTitle, avatar, primaryBtnText } = getType();
  const history = useNavigate();
  const [loading, setLoading] = useState(false);
  const [callType, setCallType] = useState<'audio' | 'video' | ''>(''); // 设置拨打类型
  const filePreviewRef = useRef<FilePreviewAPI>();
  const { changeActiveTab } = useContext(Context);

  const [addressList, setAddressList] = useState<
    {
      addressee: string;
      mobile: string;
      realName: string;
      addresseeStatus: number;
      addresseeName: string;
      userId: string;
      avatar: string;
      sourceType: 1 | 2;
      callRoomId: string;
      bizMsgId: string;
    }[]
  >([]);

  // 已读、忽略
  const ignoreClick = async (params: any) => {
    try {
      setLoading(true);
      await updateTodo(data.id, params);
      setLoading(false);
      onInit();
    } catch (error) {
      setLoading(false);
    }
  };

  // 加入视频会议
  const agreeOrRefusedMeeting = async (meetingId: string) => {
    try {
      const result = await getCamera(false);
      if (result.length === 0) {
        return false;
      }
      await dealInvite({
        meetingId,
        flg: true,
        sourceType: 3,
      });
      setLoading(false);
      history('/VideoMeeting/MeetingRoom', {
        state: {
          meetingId,
          userType: 'normal',
        },
      });
    } catch (error) {
      setLoading(false);
    }
  };

  // 重新发起会议
  const resetMetting = async () => {
    const clientType = useUserStore.getState().clientType;
    const deviceId = useUserStore.getState().deviceId;
    try {
      const res = (await meetingReissue({
        id: extra1.meetingIdStr,
        sourceType: 3,
        initiatorDeviceType: clientType,
        initiatorDeviceId: deviceId,
      })) as {
        data: any;
      };
      history('/VideoMeeting/MeetingRoom', {
        state: {
          meetingId: res.data,
          userType: 'Emcee', // 主持人
        },
      });
    } catch (error) {
      setLoading(false);
    }
  };

  // 主按钮点击事件
  const onClick = async (params: any) => {
    console.log('extra1', extra1);
    if (!extra1 && ![3, 117].includes(data.todoType)) {
      message.warning('历史数据有bug，原因是【extra1】没有返回数据，请联系java');
      return;
    }
    setLoading(true);
    switch (data.todoType) {
      // 邮箱
      case 100:
        goPage('/mailHomePage', {
          fromModule: 'todoList',
          mailType: { index: extra1.receiptNotRead },
        });
        setLoading(false);
        break;
      // 语音通话
      case 101:
        call('audio', 2);
        break;
      // 视频通话
      case 102:
        call('video', 2);
        break;

      // 视频通话正文邮件
      case 103:
        call('video', 1);
        break;

      // 语音通话正文邮件
      case 104:
        call('audio', 2);
        break;
      // 视频会议
      case 105:
        // 重新发起
        if (extra1.meetingEnd) {
          resetMetting();
        } else {
          // 加入会议
          agreeOrRefusedMeeting(extra1.meetingIdStr);
        }
        break;
      // 今日新闻
      case 117:
        changeActiveTab('articleDetails', null, data.bizMsgId, 'todayNews');
        break;
      // 审批
      case 3:
        goPage('/approval', {
          approvalType: {
            index: 202,
          },
        });
        setLoading(false);
        break;
      // 其他文件跳文库大全
      default:
        preview({ ...extra1, id: extra1.esId });
        setTimeout(() => {
          setLoading(false);
        }, 1000);
        break;
    }
    // 设为已读状态
    if (!data.status) {
      await ignoreClick(params);
    } else {
      setLoading(false);
    }
  };

  // 拨打语音通话/视频通话
  const call = async (type: 'audio' | 'video', sourceType: 1 | 2) => {
    const result = await getCamera(true);
    if (result.length === 0) {
      return false;
    }
    setAddressList([
      {
        addressee: data.sendEmail,
        mobile: '',
        realName: data.sendRealName,
        addresseeStatus: 1,
        addresseeName: data.sendEmail,
        userId: data.sendUserId,
        avatar: data.groupAvatar || data.sendAvatar,
        sourceType,
        callRoomId: extra1.roomIdStr || extra1.roomId,
        bizMsgId: data.bizMsgId,
      },
    ]);
    setCallType(type);
  };

  // 挂断通话
  const callCancel = () => {
    setCallType('');
    setLoading(false);
  };

  // 浏览文件
  const preview = (data: any) => {
    filePreviewRef.current?.open({ ...data, fileSource: 13 });
  };

  // 获得主按钮
  const getPrimaryBtn = () => {
    const params = {
      status: 1, // 已读：1 未读：0
      ignoreMark: data.ignoreMark ? 1 : 0, // 已忽略：1 未忽略：0
    };

    return (
      <Button
        className={styles.btnPrimary}
        onClick={() => onClick(params)}
        type="primary"
        size="small"
        loading={loading}
        disabled={data.status && [101, 102, 103, 104].includes(data.todoType)}
      >
        {primaryBtnText}
      </Button>
    );
  };

  // 获得左侧区域展示
  const getLeftContent = () => {
    // 今日新闻
    if (data.todoType === 117) {
      return extra1 ? (
        <div className={styles.leftContent}>
          <Image
            src={getMediaUrl(extra1.visitableMainImage)}
            preview={false}
            fallback="data:image/png;base64,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"
          />
        </div>
      ) : null;
    } else {
      return (
        <div className={styles.leftContent}>
          <Badge dot={!data.status}>
            <Avatar shape="square" size={getWidth(62)} src={avatar} />
          </Badge>
          {typeTitle && <div className={`${styles.typeTitle} ${className}`}>{typeTitle}</div>}
        </div>
      );
    }
  };

  return (
    <>
      <List.Item>
        <List.Item.Meta
          avatar={getLeftContent()}
          title={
            <div className={styles.titleLine}>
              <div className={styles.title}>{data.title}</div>
              <div className={styles.rightActions}>
                {!data.status && !data.ignoreMark && (
                  <Button
                    className={styles.btnDefault}
                    onClick={() =>
                      !data.ignoreMark &&
                      ignoreClick({ status: data.status ? 1 : 0, ignoreMark: 1 })
                    }
                    size="small"
                    disabled={data.ignoreMark as boolean}
                    loading={loading}
                  >
                    {data.ignoreMark ? '已忽略' : '忽略'}
                  </Button>
                )}
                {getPrimaryBtn()}
              </div>
            </div>
          }
          description={
            <div className={styles.contentLine}>
              <div className={styles.content}>
                {data.todoType != 117 && (
                  <div>
                    {data.groupName
                      ? data.groupName
                      : `${data.sendRealName || '母账户'} ${data.sendUsername}`}
                  </div>
                )}
                {data.title2 && <div>{data.title2}</div>}
              </div>
              <div className={styles.createTime}>{data.updateTime || '-'}</div>
            </div>
          }
        />
      </List.Item>
      {/* 语音通话/邮箱正文音频通话 */}
      {callType === 'audio' && (
        <VoiceCall
          open={callType === 'audio'}
          title={'语音通话'}
          addressData={addressList}
          sourceType={addressList[0].sourceType}
          onCancel={callCancel}
          CallBackParams={{
            callRoomId: addressList[0].callRoomId,
            bizMsgId: addressList[0].bizMsgId,
          }}
        ></VoiceCall>
      )}
      {/* 视频通话/邮箱正文视频通话 */}
      {callType === 'video' && (
        <VideoCall
          open={callType === 'video'}
          title={'视频通话'}
          addressData={addressList}
          onCancel={callCancel}
          sourceType={addressList[0].sourceType}
          CallBackParams={{
            callRoomId: addressList[0].callRoomId,
            bizMsgId: addressList[0].bizMsgId,
          }}
        ></VideoCall>
      )}
      {/* 文件预览 */}
      <FilePreview ref={filePreviewRef} />
    </>
  );
};
export default TodoItem;
