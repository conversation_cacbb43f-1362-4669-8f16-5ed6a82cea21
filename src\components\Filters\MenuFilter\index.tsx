import { Button, Space } from 'antd';
import Menu, { MenuItem } from 'rc-menu';
import { useLocation } from 'react-router-dom';
import styles from './index.module.less';

interface Item {
  key: string;
  label: string;
}
export interface Props {
  items: Item[];
  selectedKeys: string[];
  onSelect: (key: string, isSubmit: boolean) => void;
  onSubmit: (type: 'reset' | 'ok' | 'close') => void;
}

const Component = ({ items, selectedKeys, onSelect, onSubmit }: Props) => {
  const location = useLocation();
  const toggleMenuItem = ({ key }: any) => {
    onSelect(key, true);
  };
  return (
    <div className={styles.menuFilter}>
      <Menu
        prefixCls="ant-dropdown-menu"
        selectedKeys={selectedKeys}
        onClick={toggleMenuItem}
        className={styles.filterBox}
      >
        {items.map((item) => {
          return (
            <MenuItem key={item.key}>
              <Space>
                {selectedKeys.includes(item.key) && (
                  <Button type="primary" size="small">
                    已选
                  </Button>
                )}
                {!selectedKeys.includes(item.key) && (
                  <Button type="primary" size="small" ghost>
                    多选
                  </Button>
                )}
                <span>{item.label}</span>
              </Space>
            </MenuItem>
          );
        })}
      </Menu>
      <div className={styles.footer}>
        <Button type="primary" ghost onClick={() => onSubmit('reset')}>
          重置
        </Button>
        <Button type="primary" ghost onClick={() => onSubmit('close')}>
          {location.pathname === '/library' ||
          location.pathname === '/file' ||
          location.pathname === '/soft'
            ? '关闭'
            : '确认'}
        </Button>
      </div>
    </div>
  );
};

export default Component;
