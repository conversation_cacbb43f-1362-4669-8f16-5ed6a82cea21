import { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { ActionSheet, ActionSheetProps, Popup, PopupProps } from 'react-vant';
import styles from './index.module.less';
function useSimulateMouseEvents(targetRef: any) {
  useEffect(() => {
    const handleWheel = (event: {
      deltaY: any;
      clientX: any;
      clientY: any;
      preventDefault: () => void;
    }) => {
      const deltaY = event.deltaY;
      console.log(deltaY);
      // 创建新的 MouseEvent，这里以 mousemove 为例
      const mouseMoveEvent = new MouseEvent('mousemove', {
        bubbles: true,
        cancelable: true,
        clientX: event.clientX,
        clientY: event.clientY + deltaY, // 根据滚动方向调整坐标
      });

      // 分发事件到目标元素
      document.querySelector('.rv-picker-column')!.dispatchEvent(mouseMoveEvent);
      // 如果需要阻止默认行为
      //event.preventDefault();
    };

    targetRef.current?.addEventListener('wheel', handleWheel);

    return () => {
      targetRef.current?.removeEventListener('wheel', handleWheel);
    };
  }, [targetRef.current]);
}
const CustomPopup = (Props: PopupProps) => {
  const [teleport, setTeleport] = useState<HTMLDivElement>();
  const resetStaticstylesref = useRef(null);
  const ref = useRef(null);
  const [isRander, setIsRander] = useState<boolean>(false);
  useSimulateMouseEvents(ref);
  useEffect(() => {
    if (document.querySelector('#smartSecretaryRoot')) {
      setTeleport(document.querySelector('#smartSecretaryRoot') as HTMLDivElement);
      setTimeout(() => {
        setIsRander(true);
      });
    }
  }, []);

  return (
    <>
      {teleport &&
        createPortal(
          <div className={styles.resetStaticstyles} ref={resetStaticstylesref}>
            {resetStaticstylesref.current && isRander && (
              <Popup {...Props} teleport={resetStaticstylesref.current}>
                <div ref={ref}>{Props.children}</div>
              </Popup>
            )}
          </div>,
          teleport,
        )}
    </>
  );
};
const CustomActionSheet = (Props: ActionSheetProps) => {
  const [teleport, setTeleport] = useState<HTMLDivElement>();
  const resetStaticstylesref = useRef(null);
  useEffect(() => {
    if (document.querySelector('#smartSecretaryRoot')) {
      setTeleport(document.querySelector('#smartSecretaryRoot') as HTMLDivElement);
    }
  }, []);
  return (
    <>
      {teleport &&
        createPortal(
          <div className={styles.resetStaticstyles} ref={resetStaticstylesref}>
            {
              <ActionSheet {...Props} teleport={resetStaticstylesref.current}>
                {Props.children}
              </ActionSheet>
            }
          </div>,
          teleport,
        )}
    </>
  );
};
export { CustomActionSheet as ActionSheet, CustomPopup as Popup };
