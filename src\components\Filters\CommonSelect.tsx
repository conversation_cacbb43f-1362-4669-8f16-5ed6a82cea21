import { useEffect, useState } from 'react';
import MenuFilter from './MenuFilter';

const Component = ({ setSelectedKeys, selectedKeys, confirm, close, clearFilters, list }: any) => {
  const [items, setItems] = useState([...list]);
  const toggleItem = (key: string) => {
    if (selectedKeys.includes(key)) {
      setSelectedKeys(selectedKeys.filter((k: string) => k !== key));
    } else {
      setSelectedKeys([...selectedKeys, key]);
    }
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
    } else if (type === 'close') {
      // TODOLIST, 这里是要改的，但因为没改到这里来，我先按原有逻辑处理bug
      confirm();
      close();
    }
  };

  useEffect(() => {
    clearFilters();
  }, [list]);

  return (
    <MenuFilter items={items} selectedKeys={selectedKeys} onSelect={toggleItem} onSubmit={submit} />
  );
};

export default Component;
