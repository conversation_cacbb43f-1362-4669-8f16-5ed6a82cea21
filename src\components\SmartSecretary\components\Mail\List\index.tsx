import { contactsMailsPage } from '@/api/mail/chat';
import useAppStore from '@/store/useAppStore';
import useIntelligentSecretaryStore from '@/store/useIntelligentSecretaryStore';
import { getWidth } from '@/utils/common';
import { Card, List } from 'antd';
import { FC, useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { FromGroup, FromPreson, SendGroup, SendPreson } from '../Card';
import styles from './index.module.less';
const MailList: FC<any> = () => {
  const { channel } = useAppStore((state: any) => state);
  const [listData, setListData] = useState<any>([]);
  const [hasMore, setHasMore] = useState(true);
  const [pageNo, setPageNo] = useState(1);
  const [total, setTotal] = useState(0);
  const [queryData, temporaryData, queryType] = useIntelligentSecretaryStore((state) => [
    state.queryData,
    state.temporaryData,
    state.queryType,
  ]);

  useEffect(() => {
    fetchList(1);
  }, [queryType]);

  const fetchList = (val: number) => {
    const obj = {
      pageNo: val,
      pageSize: 20,
      queryScene: 1,
      searchKeywords:
        queryType.current === 'current'
          ? temporaryData
          : queryType.current === 'result'
            ? [...queryData, ...temporaryData]
            : [''],
    };
    contactsMailsPage(obj).then((res: any) => {
      if (res?.code === 0) {
        setTotal(res.data.total);
        setListData(val === 1 ? res.data.list : [...listData, ...res.data.list]);
        setHasMore(res.data.list.length === 20 ? true : false);
        setPageNo(val);
      }
    });
  };

  return (
    <div className={styles.mailContainer}>
      <div className={styles.mailList}>
        <div
          id="scrollableMainDiv"
          className={styles.scrollableDiv}
          style={{ height: window.innerHeight - getWidth(channel === 'web' ? 410 : 350) }}
        >
          <InfiniteScroll
            dataLength={listData.length} // 已加载的数据长度
            next={() => fetchList(pageNo + 1)} // 加载更多数据的函数
            hasMore={hasMore} // 是否还有更多数据
            loader={false}
            scrollableTarget="scrollableMainDiv"
          >
            <List
              header={null}
              footer={null}
              dataSource={listData}
              className={styles.mailList}
              renderItem={(item: any, index: number) =>
                item.mailStatus === 1 ? (
                  //已发
                  <List.Item>
                    <Card bordered={false} className={styles.mailCard}>
                      {item.groupFlag ? (
                        <SendGroup item={item} index={index} />
                      ) : (
                        <SendPreson item={item} index={index} />
                      )}
                    </Card>
                  </List.Item>
                ) : (
                  //已收
                  <List.Item>
                    <Card bordered={false} className={styles.mailCard}>
                      {item.groupFlag ? (
                        <FromGroup item={item} index={index} />
                      ) : (
                        <FromPreson item={item} index={index} />
                      )}
                    </Card>
                  </List.Item>
                )
              }
            />
          </InfiniteScroll>
        </div>
      </div>
    </div>
  );
};
export default MailList;
