import { getDataByResult } from '@/api/addressBook';
import { Button, Space, Table } from 'antd';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import Context from '../Context';
import { getColumns } from './columns';
import styles from './index.module.less';

const Component = () => {
  const { useUsersCtxStore, useMainPanelCtxStore } = useContext(Context);
  const [
    usersOpen,
    list,
    setList,
    selectedList,
    setSelectedList,
    selectedMap,
    setSelectedMap,
    keywords,
    temporaryData,
    queryType,
  ] = useUsersCtxStore!((state) => [
    state.usersOpen,
    state.list,
    state.setList,
    state.selectedList,
    state.setSelectedList,
    state.selectedMap,
    state.setSelectedMap,
    state.keywords,
    state.temporaryData,
    state.queryType,
  ]);
  const [userMap] = useMainPanelCtxStore!((state) => [state.userMap]);
  const [loading, setLoading] = useState(false);
  const [pageNumber, setPageNumber] = useState({ current: 1 });
  const [pageSize] = useState(100);
  const [total, setTotal] = useState(0);
  const [loadedList, setLoadedList] = useState<any[]>([]);
  const didMountRef = useRef(false);
  const columns = useMemo(() => {
    return getColumns().map((item) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              {list.length === 0 && <span>操作</span>}
              {list.length > 0 && (
                <Button
                  type="primary"
                  size="small"
                  ghost={selectedList.length < list.length}
                  onClick={() => {
                    if (selectedList.length === list.length) {
                      const map = { ...userMap };
                      setSelectedMap(map);
                      setSelectedList(Object.values(map));
                    } else {
                      const map: any = {};
                      list.forEach((item: any) => {
                        map[item.id] = item;
                      });
                      setSelectedMap(map);
                      setSelectedList(Object.values(map));
                    }
                  }}
                >
                  全部选择
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value, row) => {
          let element = null;
          if (selectedMap[row.id]) {
            element = (
              <Button
                type="primary"
                size="small"
                disabled={Boolean(userMap[row.id])}
                onClick={() => {
                  const map = { ...selectedMap };
                  delete map[row.id];
                  setSelectedMap(map);
                  setSelectedList(Object.values(map));
                }}
              >
                已选
              </Button>
            );
          } else {
            element = (
              <Button
                type="primary"
                size="small"
                ghost
                onClick={() => {
                  const map = { ...selectedMap };
                  map[row.id] = row;
                  setSelectedMap(map);
                  setSelectedList(Object.values(map));
                }}
              >
                多选
              </Button>
            );
          }
          return element;
        };
      }
      return item;
    });
  }, [list, selectedList, selectedMap, userMap]);
  const getList = () => {
    getDataByResult(
      {
        pageNo: pageNumber.current,
        pageSize,
        keyWordList:
          queryType.current === 'current'
            ? [...keywords, ...temporaryData].join(',')
            : keywords.join(','),
        blackType: 1,
        contactType: 1,
      },
      { setLoading },
    ).then(({ data }: any) => {
      console.log('dsadaf');
      const { total, list } = data;
      const nextLoadedList = [...loadedList, ...list];
      setTotal(total);
      setLoadedList(nextLoadedList);
      setList([...nextLoadedList]);
      if (total > pageSize * pageNumber.current) {
        setPageNumber((value) => ({ current: value.current + 1 }));
      }
    });
  };
  useEffect(() => {
    if (usersOpen) {
      const map = { ...userMap };
      setSelectedMap(map);
      setSelectedList(Object.values(map));
    }
  }, [usersOpen, keywords, userMap, temporaryData]);
  useEffect(() => {
    if (!loadedList.length || loadedList.length < total) {
      getList();
    }
  }, [pageNumber]);
  useEffect(() => {
    if (didMountRef.current) {
      setTotal(0);
      setLoadedList([]);
      setList([]);
      setPageNumber({ current: 1 });
    } else {
      didMountRef.current = true;
    }
  }, [keywords, temporaryData]);

  return (
    <div className={styles.list}>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={list}
        loading={loading}
        rowKey={'id'}
        scroll={{ y: 491 }}
        pagination={false}
      />
    </div>
  );
};

export default Component;
