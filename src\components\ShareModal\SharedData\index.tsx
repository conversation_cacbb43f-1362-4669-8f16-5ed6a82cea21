import { Table } from 'antd';
import { FC } from 'react';
import styles from '../index.module.less';

interface SelectDataProps {
  dataSource: any[] | undefined;
  columns: any[];
  dataTitle: string;
}

const Component: FC<SelectDataProps> = ({ dataSource, columns, dataTitle }) => {
  return (
    <div className={styles.list}>
      <div className={styles.title}>
        已选{dataTitle}({(dataSource || []).length})
      </div>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        scroll={{ x: 700, y: 163 }}
        pagination={false}
      />
    </div>
  );
};

export default Component;
