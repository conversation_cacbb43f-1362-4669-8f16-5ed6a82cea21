.title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: start; 
  .left {
    width: 45%;
    display: flex;
    height: 32px;
    line-height: 32px;
    align-items: baseline;

    :global {
      span:last-child {
        color: initial;
        margin-left: 10px;
        font-size: 14px;
        b {
          color: var(--ant-color-error);
          font-weight: normal;
        }
      }
    }
  }
  .right {
    display: flex;
    align-items: start;
    :global {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }
}

.toolBar {
  padding: 16px;

  .text{
    color: var(--ant-color-primary);
  }
}

.list {
  margin: 0 8px;

  :global{
    .ant-table-tbody-virtual .ant-table-tbody-virtual-scrollbar-horizontal{
      display: none;
    }
  }
}

.success {
  color: var(--ant-color-success);
}

.uploading {
  color: var(--ant-color-primary);
}

.waiting,
.pause {
}

.error,
.abort {
  color: var(--ant-color-error);
}

.progress {
  border-top: 1px solid #efefef;
  text-align: center;
  padding: 8px;
}
