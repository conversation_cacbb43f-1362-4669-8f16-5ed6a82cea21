/**
 * 选择员工公共组件
 */

import Drawer from '@/components/Drawer';
import useAddressBookStore from '@/store/useAddressBook';
import { Button } from 'antd';
import { FC, useEffect, useState } from 'react';
import Header from '../../publicComponents/Header';
import Search from '../Search';
import Footer from './Footer';
import styles from './index.module.less';
import List from './List';
interface Props {
  title?: string; // 头部标题
  open: boolean; // 开关标识
  onClose: () => void; // 关闭窗口
  onSubmit: (list: any[]) => void; // 提交表单
  mode: 'single' | 'multiple'; // 单选/多选
  type?: 1 | 2 | null; // 通讯录类型（1：联系人，2：群）
  defaultValue?: string | string[] | any[]; // 默认选中的好友 传username。格式如： '5802666698-43' | ['5802666698-43', '5802666698-207'] | [{ username: '5802666698-43' }]
  dataSource?: any[]; // 数据源（可能只是通讯录中的一部分人）
  disabledList?: any[]; // 禁用的联系人列表[{ username: '5802666698-43',... }]
}
const Index: FC<Props> = ({
  title = '通讯录',
  open,
  onClose,
  onSubmit,
  mode,
  type,
  defaultValue,
  dataSource,
  disabledList,
}) => {
  const [selectList, setSelectList] = useState<any[]>([]);
  const [addressBookList] = useAddressBookStore((state) => [state.addressBookList]); // 通讯录列表：包含联系人和群组
  const [renderList, setRenderList] = useState(dataSource || addressBookList); // 用于控制渲染的列表
  const [keyWords, setKeyWords] = useState('');

  useEffect(() => {
    setRenderList(dataSource);
  }, [dataSource]);

  useEffect(() => {
    let list: any[] = [];
    if (!defaultValue) return;
    if (typeof defaultValue === 'string') {
      list = [defaultValue];
    }
    if (typeof defaultValue === 'object') {
      if (typeof defaultValue[0] === 'string') {
        list = [...defaultValue];
      } else {
        list = defaultValue.map((item) => item.username);
      }
    }
    getSelectList(list);
  }, [defaultValue]);

  // 设置默认选中的好友
  const getSelectList = (selList: any[]) => {
    const res = renderList.filter((item: any) => selList.includes(item.username));
    setSelectList(res);
  };

  // 提交选中
  const handlderSubmit = () => {
    onSubmit(selectList);
  };

  // 渲染头部功能按钮
  const renderActionList = () => {
    const btnList = [
      <Button key="goBack" onClick={onClose}>
        返回
      </Button>,
    ];
    if (mode === 'multiple') {
      const choiceBtn =
        selectList.length === renderList.length ? (
          <Button key="cancelChoice" onClick={() => setSelectList([])}>
            取消选择
          </Button>
        ) : (
          <Button key="allChoice" onClick={() => setSelectList([...renderList])}>
            全部选择
          </Button>
        );
      btnList.unshift(choiceBtn);
    }
    return btnList;
  };

  return (
    <Drawer
      title={<Header title={title} onCancel={onClose} actionList={renderActionList()} />}
      onClose={onClose}
      open={open}
      footer={<Footer total={selectList.length} onSubmit={handlderSubmit} />}
    >
      <div className={styles.employeeListContainer}>
        <Search
          value={keyWords}
          onChange={(e) => setKeyWords(e.target.value)}
          onSearch={() => setKeyWords('')}
        />
        <List
          mode={mode}
          type={type}
          searchValue={keyWords}
          employees={renderList}
          selectList={selectList}
          setSelectList={setSelectList}
          disabledList={disabledList}
        />
      </div>
    </Drawer>
  );
};

export default Index;
