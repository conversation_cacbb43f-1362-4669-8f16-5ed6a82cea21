import { queryGroupByPage } from '@/api/addressBook';
import { AutoComplete, AutoCompleteProps, Button, Col, Form, Row, Space } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useContext, useState } from 'react';
import Context from '../Context';
import styles from './index.module.less';

const Component = () => {
  const { useShareFoldersCtxStore } = useContext(Context);
  const [setQueryType, setTemporaryData] = useShareFoldersCtxStore!((state) => [
    state.setQueryType,
    state.setTemporaryData,
  ]);
  const [form] = Form.useForm();
  const handleFilter = () => {
    return form.validateFields().then(({ keywords }) => {
      setTemporaryData([keywords]);
    });
  };
  const queryAll = () => {
    setQueryType({ current: 'all' });
    form.resetFields();
  };
  const queryResults = () => {
    handleFilter()
      .then(() => {
        setQueryType({ current: 'results' });
      })
      .then(() => {
        form.resetFields();
      });
  };
  //todo
  const [options, setOptions] = useState<AutoCompleteProps['options']>([]);
  const onSelect = (data: any) => {
    const value = data.groupName;
    form.setFieldsValue({ keywords: value });
    handleFilter().then(() => {
      setQueryType({ current: 'current' });
    });
  };

  const handleKeyDown = (e: any) => {
    if (e.keyCode === 13 || e.code === 'Enter' || e.key === 'Enter') {
      handleFilter().then(() => {
        setQueryType({ current: 'current' });
      });
    }
  };

  const onSearch = (data: string) => {
    const params: any = {
      pageNo: 1,
      pageSize: 10,
      keyWordList: [data],
    };
    queryGroupByPage(params).then((res: any) => {
      const list = res.data.list;
      setOptions(list);
    });
    querySelectFn();
  };
  const querySelectFn = useCallback(
    debounce(() => {
      handleFilter().then(() => {
        setQueryType({ current: 'current' });
      });
    }, 500),
    [],
  );
  return (
    <div className={styles.filter}>
      <Form form={form} labelCol={{ span: 0 }}>
        <Row gutter={8}>
          <Col span={6}>
            <Form.Item label="" name="keywords">
              {/* <Input placeholder="请输入" /> */}
              <AutoComplete
                placeholder="请输入"
                popupMatchSelectWidth={false}
                onSelect={(e) => onSelect(e)}
                className={styles.searchInput}
                onInputKeyDown={handleKeyDown}
                onSearch={onSearch}
              >
                {options?.map((e, index) => {
                  return (
                    <AutoComplete.Option key={index} value={e}>
                      {e.groupName}
                    </AutoComplete.Option>
                  );
                })}
              </AutoComplete>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Space>
              <Button type="primary" onClick={queryAll}>
                全部查询
              </Button>
              <Button type="primary" onClick={queryResults}>
                结果查询
              </Button>
            </Space>
          </Col>
          <Col span={12}>
            <div className={styles.rightBar}>
              <Space></Space>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default Component;
