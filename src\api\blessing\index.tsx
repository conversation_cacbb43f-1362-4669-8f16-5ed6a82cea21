import api, { Extra } from '../index';
import {
  addBlessParams,
  getMBPageParams,
  memoParams,
  shareMBParams,
  updateBlessParams,
} from './interface';

// 获取备忘列表
export const getMemoPage = (data: getMBPageParams, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/note/memo/getPage',
      data,
    },
    extra,
  );
};

// 添加备忘
export const addMemo = (data: memoParams, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/note/memo/add',
      data,
    },
    extra,
  );
};

// 修改备忘
export const updateMemo = (data: memoParams, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/note/memo/updateById',
      data,
    },
    extra,
  );
};

// 分享备忘
export const shareMemo = (data: shareMBParams, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/note/memo/share',
      data,
    },
    extra,
  );
};

// 删除备忘
export const deleteDataByType = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/note/deleteDataByType',
      data,
    },
    extra,
  );
};

// 获取祝福列表
export const getBlessPage = (data: getMBPageParams, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/note/blessing/getPage',
      data,
    },
    extra,
  );
};

// 新增祝福
export const addBless = (data: addBlessParams, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/note/blessing/batchAdd',
      data,
    },
    extra,
  );
};

// 修改祝福
export const updateBless = (data: updateBlessParams, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/note/blessing/updateById',
      data,
    },
    extra,
  );
};

// 分享祝福
export const shareBless = (data: shareMBParams, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/note/blessing/share',
      data,
    },
    extra,
  );
};

// 获取备忘祝福所有内容数据
export const getContentList = (params: { querySource: number }) => {
  return api.get({
    url: '/web-api/note/getAllContentList',
    params,
  });
};
