export const mainList = new Array(10000).fill(0).map((value, index) => {
  return {
    id: index,
    userId: 8,
    userName: '***********',
    nickName: '汤唯',
    title: '裕邦智慧办公平台宣传片视频',
    source: '内部共享文件夹',
    saftLevel: '只读分享',
    fileType: 'MP4',
    fileSize: '12MB',
    uploadTime: '2024-08-08 12:12:12',
    bizId: '666',
    metaDataId: '666',
    imgIsStandard: 8,
    tenantId: 8,
    filePath: '/写真集.mp4',
    creator: '汤唯',
    createTime: '2024-08-08 12:12:12',
    updater: '汤唯',
    updateTime: '2024-08-08 12:12:12',
  };
});

export const userList = new Array(10000).fill(0).map((value, index) => {
  return {
    id: `${index}`,
    name: '汤唯',
    account: '***********',
    tel: '***********',
    email: '<EMAIL>',
  };
});

export const sharedFolderList = new Array(20000).fill(0).map((value, index) => {
  return {
    id: `${index}`,
    name: '相亲相爱一家人',
    count: 40,
    members: ['林俊杰', '张学友', '刘德华', '黎明', '霍建华'],
  };
});

export const sharedFileList = new Array(50000).fill(0).map((value, index) => {
  return {
    id: `${index}`,
    username: '汤唯',
    title: '裕邦企业文化视频裕邦企业文化视频',
    format: '裕邦文档',
    source: '本地文件',
    size: '8M',
  };
});
