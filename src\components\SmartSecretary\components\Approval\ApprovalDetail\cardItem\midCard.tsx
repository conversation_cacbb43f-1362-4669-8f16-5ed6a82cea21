import { approvalRes, ApprovalStatus } from '@/api/Approval/module';
import { parserHtmlToString } from '@/utils/parser';
import { Card, Descriptions, Typography } from 'antd';
import classNames from 'classnames';
import { FC, useEffect, useState } from 'react';
import CollapseFiles from '../collapse/CollapseFiles';
import { getApprovalName } from '../index';
import styles from './index.module.less';
const { Paragraph } = Typography;

interface Props {
  approvalRes: approvalRes;
}

const MidCard: FC<Props> = ({ approvalRes }) => {
  const [expanded, setExpanded] = useState<boolean>(false);
  const [expanded1, setExpanded1] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  useEffect(() => {
    if (loading) {
      setTimeout(() => {
        setLoading(false);
      }, 500);
    }
  }, [loading]);
  useEffect(() => {
    setLoading(true);
  }, [approvalRes]);
  return (
    <Card key={approvalRes.id} hoverable={true} className={styles.midcard} loading={loading}>
      <span className={classNames(styles.rbt, approvalRes.currentStatus !== 2 ? styles.red : null)}>
        {ApprovalStatus[approvalRes.currentStatus as keyof typeof ApprovalStatus]}
      </span>
      <Descriptions column={1} className={styles.descriptions} colon={false}>
        <Descriptions.Item label={`${approvalRes.fromUserLevel}:`}>
          {approvalRes.fromUserRealName || approvalRes.fromUserName}
        </Descriptions.Item>
        <Descriptions.Item label="审批事项：">
          {expanded ? (
            <div dangerouslySetInnerHTML={{ __html: getApprovalName(approvalRes) }}></div>
          ) : (
            <Paragraph
              rootClassName="paragraphBox"
              ellipsis={{
                rows: 1,
                expandable: 'collapsible',
                expanded: expanded,
                onExpand: (_, info) => setExpanded(info.expanded),
                symbol: expanded ? (
                  <span className={styles.symbolSpan}>收起</span>
                ) : (
                  <span className={styles.symbolSpan}>展开</span>
                ),
              }}
            >
              {parserHtmlToString(getApprovalName(approvalRes))}
            </Paragraph>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="审批内容：">
          {expanded1 ? (
            <div dangerouslySetInnerHTML={{ __html: approvalRes.middleContent }}></div>
          ) : (
            <Paragraph
              rootClassName="paragraphBox"
              ellipsis={{
                rows: 1,
                expandable: 'collapsible',
                expanded: expanded1,
                onExpand: (_, info) => setExpanded1(info.expanded),
                symbol: expanded1 ? (
                  <span className={styles.symbolSpan}>收起</span>
                ) : (
                  <span className={styles.symbolSpan}>展开</span>
                ),
              }}
            >
              {parserHtmlToString(approvalRes.middleContent)}
            </Paragraph>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="审批附件：">
          <CollapseFiles items={approvalRes.fileDOList || []}></CollapseFiles>
        </Descriptions.Item>
        <Descriptions.Item label="审批时间：">{approvalRes.approvalTime}</Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default MidCard;
