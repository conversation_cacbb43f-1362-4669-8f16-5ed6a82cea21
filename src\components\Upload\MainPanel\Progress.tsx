import { libraryAuth<PERSON><PERSON><PERSON> } from '@/api/library';
import { cwsRequest } from '@/store/useCppWebSocketStore';
import { Progress } from 'antd';
import CryptoJS from 'crypto-js';
import { add } from 'lodash';
import { forwardRef, useContext, useImperativeHandle } from 'react';
import Context from '../Context';
import styles from './index.module.less';
import { setPercent } from './useCtxStore';

export const Component = forwardRef((props, ref) => {
  useImperativeHandle(ref, () => ({
    upload: () => {
      upload();
    },
    pause: () => {
      pause();
    },
    resume: () => {
      resume();
    },
    abort: () => {
      abort();
    },
  }));
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [
    selectedFileList,
    setSelectedFileList,
    uploadFileList,
    setUploadFileList,
    uploadFileMap,
    setUploadFileMap,
    setUploadFilePathList,
    filesSize,
    setFilesSize,
    uploadedFilesSize,
    setUploadedFilesSize,
    setErrorFiles,
    setAbortFiles,
    loadingStatus,
    setLoadingStatus,
    timestamp,
  ] = useMainPanelCtxStore!((state) => [
    state.selectedFileList,
    state.setSelectedFileList,
    state.uploadFileList,
    state.setUploadFileList,
    state.uploadFileMap,
    state.setUploadFileMap,
    state.setUploadFilePathList,
    state.filesSize,
    state.setFilesSize,
    state.uploadedFilesSize,
    state.setUploadedFilesSize,
    state.setErrorFiles,
    state.setAbortFiles,
    state.loadingStatus,
    state.setLoadingStatus,
    state.timestamp,
  ]);
  // const state = useUserStore();
  const getSecretKey = (fileFormatType: string) => {
    return '';

    // if (
    //   fileFormatType === 'library_file_type_audio' ||
    //   fileFormatType === 'library_file_type_video'
    // ) {
    //   return '';
    // }
    // return CryptoJS.lib.WordArray.random(16 / 2).toString(CryptoJS.enc.Hex);
  };
  const upload = () => {
    const uploadFileList = [...selectedFileList];
    if (uploadFileList.length === 0) {
      return;
    }
    const uploadFileMap: Record<string, any> = {};
    const uploadFilePath2AttrsList: any[] = [];
    const uploadFilePathList: any[] = [];
    let filesSize = 0;
    uploadFileList.forEach((file: any) => {
      file.status = 'waiting';
      file.uploadedSize = 0;
      file.secretKey = getSecretKey(file.fileFormatType);
      filesSize = add(filesSize, file.fileSize);
      uploadFileMap[file.id] = file;
      uploadFilePathList.push({ filePath: file.filePath, MD5: file.id, secretKey: file.secretKey });
      if (
        ['library_file_type_pic', 'library_file_type_audio', 'library_file_type_video'].includes(
          file.fileFormatType,
        )
      ) {
        uploadFilePath2AttrsList.push({
          fileFormatType: file.fileFormatType,
          filePath: file.filePath,
          MD5: file.id,
        });
      }
    });
    setLoadingStatus('waiting');
    cwsRequest({
      module: 'desktopFile',
      method: 'getFilesAttrs',
      data: {
        filePathList: uploadFilePath2AttrsList,
      },
    }).then((res: any) => {
      if (res.code !== 0) {
        let count = 0;
        Object.values(uploadFileMap).forEach((file: any) => {
          file.status = 'error';
          count++;
        });
        setErrorFiles(count);
        return;
      }
      const items = JSON.parse(res.data) || [];
      items.forEach((item: any) => {
        const file = uploadFileMap[item.MD5];
        if (item.fileFormatType === 'library_file_type_pic') {
          file.width = item.width;
          file.height = item.height;
          if (item.thumbnail) {
            const thumbnail = item.thumbnail;
            thumbnail.id = CryptoJS.MD5(thumbnail.filePath + '_' + timestamp).toString();
            //thumbnail.fileName = thumbnail.filePath.split('/').pop();
            thumbnail.status = 'waiting';
            thumbnail.secretKey = getSecretKey('library_file_type_pic');
            file.thumbnailId = thumbnail.id;
            uploadFileMap[thumbnail.id] = thumbnail;
            uploadFilePathList.push({
              filePath: thumbnail.filePath,
              MD5: thumbnail.id,
              secretKey: thumbnail.secretKey,
            });
            filesSize = add(filesSize, thumbnail.fileSize);
          }
        } else if (item.fileFormatType === 'library_file_type_audio') {
          file.duration = item.duration;
        } else if (item.fileFormatType === 'library_file_type_video') {
          file.duration = item.duration;
          if (item.thumbnail) {
            const thumbnail = item.thumbnail;
            thumbnail.id = CryptoJS.MD5(thumbnail.filePath + '_' + timestamp).toString();
            //thumbnail.fileName = thumbnail.filePath.split('/').pop();
            thumbnail.status = 'waiting';
            thumbnail.secretKey = getSecretKey('library_file_type_pic');
            file.thumbnailId = thumbnail.id;
            uploadFileMap[thumbnail.id] = thumbnail;
            uploadFilePathList.push({
              filePath: thumbnail.filePath,
              MD5: thumbnail.id,
              secretKey: thumbnail.secretKey,
            });
            filesSize = add(filesSize, thumbnail.fileSize);
          }
        }
      });
      libraryAuthCipher({
        bizType: '10',
        questUrl: '/api/put_file',
        method: 'POST',
      })
        .then(({ data }: any) => {
          setUploadFileMap({ ...uploadFileMap });
          setUploadFileList([...uploadFileList]);
          setUploadFilePathList([...uploadFilePathList]);
          setFilesSize(filesSize);
          setUploadedFilesSize(0);
          setSelectedFileList((prev: any) => [...prev]);
          cwsRequest({
            module: `${config!.module}-${config!.type}`,
            method: 'uploadfile',
            data: {
              filePathList: uploadFilePathList,
              authCipher: data,
            },
          });
        })
        .finally(() => {
          setLoadingStatus('loading');
        });
    });
  };
  const pause = () => {
    return cwsRequest({
      module: `${config!.module}-${config!.type}`,
      method: 'pauseupload',
    }).then((res: any) => {
      if (res.code === 0) {
        Object.values(uploadFileMap).forEach((file: any) => {
          if (file.status === 'waiting' || file.status === 'loading') {
            file._status = 'pause';
          }
        });
        setUploadFileMap((prev: any) => ({ ...prev }));
        setUploadFileList((prev: any) => [...prev]);
        setSelectedFileList((prev: any) => [...prev]);
        setLoadingStatus('pause');
      }
    });
  };
  const resume = () => {
    return cwsRequest({
      module: `${config!.module}-${config!.type}`,
      method: 'goonupload',
    }).then((res: any) => {
      if (res.code === 0) {
        Object.values(uploadFileMap).forEach((file: any) => {
          if (file.status === 'waiting' || file.status === 'loading') {
            file._status = '';
          }
        });
        setUploadFileMap((prev: any) => ({ ...prev }));
        setUploadFileList((prev: any) => [...prev]);
        setSelectedFileList((prev: any) => [...prev]);
        setLoadingStatus('loading');
      }
    });
  };
  const abort = () => {
    return cwsRequest({
      module: `${config!.module}-${config!.type}`,
      method: 'abortupload',
    }).then((res: any) => {
      if (res.code === 0) {
        let count = 0;
        Object.values(uploadFileMap).forEach((file: any) => {
          if (file.status === 'waiting' || file.status === 'loading') {
            file.status = 'abort';
            file._status = '';
            count++;
          }
        });
        setAbortFiles((prev: any) => prev + count);
        setUploadFileMap((prev: any) => ({ ...prev }));
        setUploadFileList((prev: any) => [...prev]);
        setSelectedFileList((prev: any) => [...prev]);
      }
    });
  };

  return (
    <>
      {uploadFileList.length > 0 && (
        <div className={styles.progress}>
          <Progress
            percent={setPercent(uploadedFilesSize, filesSize, loadingStatus)}
            percentPosition={{ align: 'end', type: 'inner' }}
            size={{ height: 20 }}
          />
        </div>
      )}
    </>
  );
});

export default Component;
