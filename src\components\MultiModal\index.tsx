import { Modal } from 'antd';
import type { ReactNode } from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import type { DraggableData, DraggableEvent } from 'react-draggable';
import Draggable from 'react-draggable';
import styles from './index.module.less';
import createUseCtxStore from './useCtxStore';
interface Props {
  layoutGroup?: string;
  layoutClassName: 'modalLeft' | 'modalRight' | 'normal';
  title: ReactNode;
  open: boolean;
  footer?: ReactNode | null;
  className?: string;
  children?: ReactNode;
  onCancel?: () => void;
  closable?: boolean;
  destroyOnClose?: boolean;
  mask?: boolean;
  top?: number | string;
  width?: number | string;
  draggable?: boolean;
  zIndex?: number;
}

const Component = (props: Props) => {
  const [useCtxStore] = useState(() => createUseCtxStore(props.layoutGroup));
  const { modalLeft, modalRight } = useCtxStore();
  const [disable, setDisable] = useState<boolean>(false);
  const [bounds, setBounds] = useState({ left: 0, top: 0, bottom: 0, right: 0 });
  const draggleRef = useRef<HTMLDivElement>(null);
  const onStart = (_event: DraggableEvent, uiData: DraggableData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    const targetDiv = _event.target as HTMLDivElement;
    if (targetDiv.className !== 'drag') {
      return;
    }
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };
  const wrapperClassName = useMemo(() => {
    let className = props.className
      ? `${styles.multiModalWrapper} ${props.className}`
      : styles.multiModalWrapper;
    className =
      (modalLeft && modalRight) || props.layoutClassName === 'normal'
        ? `${className} ${props.layoutClassName}`
        : className;
    return className;
  }, [modalLeft, modalRight, props.layoutClassName, props.className]);
  useEffect(() => {
    if (props.layoutClassName === 'normal') {
      return;
    }
    useCtxStore.setState({
      [props.layoutClassName]: props.open,
    });
  }, [props.layoutClassName, props.open]);

  return (
    <Modal
      style={{ top: props.top ?? '3.75rem' }}
      classNames={{
        wrapper: wrapperClassName,
      }}
      width={props.width ?? '45rem'}
      keyboard={false}
      maskClosable={false}
      mask={props.mask ?? props.layoutClassName === 'modalRight'}
      title={
        props.draggable ? (
          <div
            className="drag"
            style={{ width: '100%', cursor: 'move' }}
            onMouseOver={() => {
              if (disable) {
                setDisable(false);
              }
            }}
            onMouseOut={() => {
              setDisable(true);
            }}
          >
            {props.title}
          </div>
        ) : (
          props.title
        )
      }
      closable={props.closable ?? true}
      closeIcon={<span>关闭</span>}
      open={props.open}
      onCancel={() => {
        if (props.onCancel) {
          props.onCancel();
        }
      }}
      footer={props.footer || null}
      destroyOnClose={props.destroyOnClose ?? false}
      zIndex={props.zIndex || 1000}
      modalRender={(modal) => {
        return props.draggable ? (
          <Draggable
            disabled={disable}
            bounds={bounds}
            nodeRef={draggleRef}
            onStart={(Events, uiData) => onStart(Events, uiData)}
          >
            <div ref={draggleRef}>{modal}</div>
          </Draggable>
        ) : (
          modal
        );
      }}
    >
      {props.children}
    </Modal>
  );
};

export default Component;
