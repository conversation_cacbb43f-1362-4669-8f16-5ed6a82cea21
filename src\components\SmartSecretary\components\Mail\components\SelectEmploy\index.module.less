.employeeListContainer {
  background: #fff;
  padding: 0 20px;
  .emplopeeList {
    .title {
      font-weight: bold;
      font-size: 16px;
    }
    .username {
      font-size: 12px;
      color: #8b9cfe;
    }
    button {
      font-size: 12px;
      padding: 0 6px;
      border-radius: 4px;
      height: auto;
    }
    .defaultBtn {
      color: #3d5afe;
      border: 1px #3d5afe solid;
    }
  }
}
.footer {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 16px;
  justify-content: space-between;
  div:first-child {
    color: #a6a6a6;
  }
  button {
  }
}
:global {
  .ant-drawer .ant-drawer-body {
    // padding: 0 20px !important;
    padding: 0 !important;
  }
}
