import { cwsRequest } from '@/store/useCppWebSocketStore';
import { AutoComplete, AutoCompleteProps, Button, Col, Form, Row, Space } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useContext, useState } from 'react';
import Context from '../Context';
import styles from './index.module.less';

const Component = () => {
  const { useSelectFilesCtxStore } = useContext(Context);
  const [setFormFilterData, setQueryType, setColumnsFilterData] = useSelectFilesCtxStore!(
    (state) => [state.setFormFilterData, state.setQueryType, state.setColumnsFilterData],
  );
  const [form] = Form.useForm();
  const handleFilter = () => {
    return form.validateFields().then((filterData) => {
      setFormFilterData({ ...filterData });
    });
  };
  const queryAll = () => {
    setQueryType({ current: 'all' });
    form.resetFields();
  };
  const queryResults = () => {
    handleFilter()
      .then(() => {
        setQueryType({ current: 'results' });
      })
      .then(() => {
        form.resetFields();
        setColumnsFilterData({
          fileFormatTypeList: [],
          fileSourceList: [],
          minFileSize: '',
          maxFileSize: '',
        });
      });
  };
  //todo
  const [fileOptions3, setFileOptions3] = useState<AutoCompleteProps['options']>([]);
  const onFileSelect = () => {
    handleFilter().then(() => {
      setQueryType({ current: 'current' });
    });
  };
  const handleKeyDown = (e: any) => {
    if (e.keyCode === 13 || e.code === 'Enter' || e.key === 'Enter') {
      handleFilter().then(() => {
        setQueryType({ current: 'current' });
      });
    }
  };
  const onFileSearch3 = (e: any) => {
    cwsRequest({
      module: 'desktopFile',
      method: 'matchFileName',
      data: { pageNumber: 1, pageSize: 10, searchString: e },
    })
      .then((res: any) => {
        if (res.code !== 0) {
          return;
        }
        const data = JSON.parse(res.data);
        setFileOptions3(
          data.map((item: any) => {
            return { label: item.length > 20 ? `${item.slice(0, 20)}...` : item, value: item };
          }),
        );
      })
      // item.replace(new RegExp(inputValue, 'gi'), match => `<span style="color: red;">${match}</span>`),
      .catch((e: any) => {
        console.log('err: ', e);
      });
    querySelectFn();
  };
  const querySelectFn = useCallback(
    debounce(() => {
      handleFilter().then(() => {
        setQueryType({ current: 'current' });
      });
    }, 500),
    [],
  );
  return (
    <div className={styles.filter}>
      <Form form={form}>
        <Row gutter={8}>
          <Col span={10}>
            <Form.Item labelCol={{ span: 8 }} label="文件名称" name="title">
              {/* <Input placeholder="请输入" /> */}
              <AutoComplete
                placeholder="请输入文件名称查询"
                options={fileOptions3}
                onSelect={onFileSelect}
                onSearch={onFileSearch3}
                popupMatchSelectWidth={false}
                onInputKeyDown={handleKeyDown}
              ></AutoComplete>
            </Form.Item>
          </Col>
          <Col span={6}></Col>
          <Col span={8}>
            <Space>
              <Button type="primary" onClick={queryAll}>
                全部查询
              </Button>
              <Button type="primary" onClick={queryResults}>
                结果查询
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default Component;
