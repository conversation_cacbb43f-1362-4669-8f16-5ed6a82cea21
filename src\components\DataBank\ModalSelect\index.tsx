// 选择文件（左右布局）组件
import ConfirmDialog from '@/components/Common/ConfirmDialog.tsx';
import { getWidth } from '@/utils/common';
import { Button, Divider, Modal, Table, message } from 'antd';
import { debounce } from 'lodash';
import { forwardRef, useContext, useEffect, useImperativeHandle, useState } from 'react';

import ModalSearch from './ModalSearch';
import ModalTitle from './ModalTitle';
import RootContext from './context';
import styles from './index.module.less';

interface childProps {
  isModalOpen: boolean; //弹框控制显示
  setIsModalOpen: any; //显示set
  columns: any; //左侧列配置
  data: any; //左侧数据
  rightData: any; //右侧数据
  rightColums: any; //右侧列配置
  setRightData?: any; //左侧【确认选择】需要回填右侧数据
  setSelectedRightData?: any; // 缓存首次选择的数据
  secondConfirmTitle?: any; //二次确认的标题
  secondConfirmOkText?: any; //二次确认的文本
  content?: any; //二次确认的文本(特殊化处理了，实际给的是二次确认弹框用的)
  isCancelDel?: boolean; //是否为取消删除
}
const ModalSelect = forwardRef(
  (
    {
      isModalOpen,
      setIsModalOpen,
      columns,
      data,
      rightData,
      rightColums,
      setRightData,
      setSelectedRightData,
      secondConfirmTitle,
      secondConfirmOkText,
      content,
      isCancelDel = false,
    }: childProps,
    ref: any,
  ) => {
    const [messageApi, contextHolder] = message.useMessage();
    const [isLeftWrapVisible, setIsLeftWrapVisible] = useState(true);
    const [isRightWrapVisible, setIsRightWrapVisible] = useState(true);
    const [isConfirmOpen, setIsConfirmOpen] = useState(false);
    const [width, setWidth] = useState(getWidth(1496));
    const { confirmSelect, queryList, total } = useContext(RootContext);
    const [loading, setLoading] = useState<boolean>(false);
    const [hasMore, setHasMore] = useState<boolean>(true);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [isScroll, setIsScroll] = useState<any>(false);
    const rowClassName = (record: any) => {
      return record.isSelected ? styles.rowSelected : '';
    };
    useEffect(() => {
      if (isModalOpen && !isLeftWrapVisible && !isRightWrapVisible) {
        setIsModalOpen(false);
        return;
      }
    }, [isModalOpen, setIsModalOpen, isLeftWrapVisible, isRightWrapVisible]);

    useEffect(() => {
      if (data.length === total && data.length) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
    }, [total, data]);
    const resetCurrentPage = () => {
      setCurrentPage(1);
      setIsScroll(true);
    };
    const closeModal = (isLeft: boolean) => {
      if (isLeft) {
        setIsLeftWrapVisible(false);
      } else {
        setIsRightWrapVisible(false);
      }
      setWidth(width / 2);
    };
    const confirmDel = () => {
      confirmSelect().then(() => {
        setIsConfirmOpen(false);
      });
    };
    const confirmLeftSelect = () => {
      const selectdData = data.filter((itm: any) => itm.isSelected);
      if (!selectdData.length) {
        messageApi.warning('请选择数据！');
        return;
      }
      setRightData(
        selectdData.map((itm: any) => {
          return { ...itm, isSelected: false };
        }),
      );
      setSelectedRightData &&
        setSelectedRightData(
          selectdData.map((itm: any) => {
            return { ...itm, isSelected: false };
          }),
        );
    };
    const confirmDelAsk = () => {
      if (!rightData.length) {
        messageApi.warning('请选择要删除的数据');
        return;
      }
      setIsConfirmOpen(true);
    };
    /**
     * 列表滚动事件
     */
    const handleScroll = debounce(async (e: React.UIEvent<HTMLDivElement>) => {
      // 防止其它的查询误触发
      if (isScroll) return setIsScroll(false);

      const { scrollTop, clientHeight, scrollHeight } = e.target as HTMLDivElement;
      if (scrollTop + clientHeight >= scrollHeight - 20 && hasMore && !loading) {
        setLoading(true);
        setCurrentPage((prevCurrentPage) => prevCurrentPage + 1);
        try {
          await queryList({ pageSize: 10, pageNo: currentPage + 1 }, true);
          setLoading(false);
        } catch (error) {
          setLoading(false);
        }
      }
    }, 300);
    useImperativeHandle(ref, () => {
      return {
        resetCurrentPage,
      };
    }, [resetCurrentPage]);
    return (
      <>
        {contextHolder}
        <Modal
          title=""
          open={isModalOpen}
          closable={false}
          footer={null}
          width={width}
          centered
          zIndex={1000}
          wrapClassName={styles.modalWrap}
          onCancel={() => setIsModalOpen(false)}
        >
          {isLeftWrapVisible && (
            <div className={styles.modalLeft}>
              {/* title */}
              <ModalTitle closeModal={closeModal} />
              <Divider />
              {/* 查询条件 */}
              <ModalSearch isLeft={true} />
              {/* 表格部分 */}
              <div className={styles.customTable}>
                <Table
                  size="small"
                  columns={columns}
                  pagination={false}
                  dataSource={data}
                  rowClassName={rowClassName}
                  rowKey={(record) => record.id}
                  {...(data.length > 7
                    ? {
                        scroll: {
                          scrollToFirstRowOnChange: false,
                          y: getWidth(407),
                          x: getWidth(745),
                        },
                      }
                    : {})}
                  loading={loading}
                  onScroll={handleScroll}
                />
              </div>
              <Divider />
              <div className={styles.btnsWrap}>
                <Button
                  type="primary"
                  className={styles.btnsParmary}
                  onClick={() => confirmLeftSelect()}
                >
                  确认选择
                </Button>
              </div>
            </div>
          )}
          {isRightWrapVisible && (
            <div className={styles.modalRight}>
              {/* title */}
              <ModalTitle isLeft={false} closeModal={closeModal} />
              <Divider />
              {/* 查询条件 */}
              <ModalSearch isLeft={false} />
              {/* 表格部分 */}
              <div className={`${styles.customTable} ${styles.customTableRight}`}>
                <Table
                  size="small"
                  columns={rightColums}
                  pagination={false}
                  dataSource={rightData}
                  rowClassName={rowClassName}
                  rowKey={(record) => record.id}
                  {...(rightData.length > 7
                    ? {
                        scroll: {
                          scrollToFirstRowOnChange: false,
                          y: getWidth(407),
                          x: getWidth(745),
                        },
                      }
                    : {})}
                />
              </div>
              <Divider />
              <div className={styles.btnsWrap}>
                <Button
                  type="primary"
                  className={styles.btnsParmary}
                  onClick={() => confirmDelAsk()}
                >
                  {isCancelDel ? '取消删除' : '确认删除'}
                </Button>
              </div>
            </div>
          )}
        </Modal>
        <ConfirmDialog
          open={isConfirmOpen}
          onOk={confirmDel}
          onCancel={() => setIsConfirmOpen(false)}
          title={secondConfirmTitle}
          okText={secondConfirmOkText}
          content={content}
        ></ConfirmDialog>
        {/* <ModalDel
        isConfirmOpen={isConfirmOpen}
        confirmDel={confirmDel}
        setIsConfirmOpen={setIsConfirmOpen}
        title={secondConfirmTitle}
        onConfirmText={secondConfirmOkText}
      >
        {content}
      </ModalDel> */}
      </>
    );
  },
);

export default ModalSelect;
