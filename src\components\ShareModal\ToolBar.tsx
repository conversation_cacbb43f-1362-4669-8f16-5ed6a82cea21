import { getWidth } from '@/utils/common';
import { Button, Select, Space } from 'antd';
import { useContext, useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import ShareContext from './ShareContext';
import { ModalType } from './ShareIndex/typing';

interface Props {
  onClick?: (type: ModalType) => void;
  dataTitle: string;
  secure?: boolean;
  minSecureLevel: number;
  disableGroup?: boolean;
  hasColumns?: boolean;
  onSetSecure?: (data: any) => void;
}

const Component = ({
  onSetSecure,
  onClick,
  secure,
  minSecureLevel,
  dataTitle,
  disableGroup,
  hasColumns,
}: Props) => {
  const [secureValue, setSecureValue] = useState(0);
  const handleChange = (value: string) => {
    setSecureValue(Number(value || 0));
    onSetSecure!(value || 0);
  };

  const shareContext = useContext(ShareContext);

  const options = [
    { label: '普通邮件', value: '0', disabled: minSecureLevel !== undefined && minSecureLevel > 0 },
    { label: '普通转发', value: '1', disabled: minSecureLevel !== undefined && minSecureLevel > 1 },
    { label: '普通分享', value: '2', disabled: minSecureLevel !== undefined && minSecureLevel > 2 },
    { label: '定时分享', value: '3', disabled: minSecureLevel !== undefined && minSecureLevel > 3 },
    { label: '只读分享', value: '4', disabled: minSecureLevel !== undefined && minSecureLevel > 4 },
    { label: '定时只读', value: '5', disabled: minSecureLevel !== undefined && minSecureLevel > 5 },
    {
      label: '阅后只读即撤',
      value: '6',
      disabled: minSecureLevel !== undefined && minSecureLevel > 6,
    },
    { label: '定时阅后只读即撤', value: '7' },
  ];

  const selectRef = useRef(null);

  useEffect(() => {
    if (onSetSecure) {
      setSecureValue(minSecureLevel);
      onSetSecure(minSecureLevel);
    }
  }, [minSecureLevel]);

  return (
    <div className={styles.toolBar}>
      <Space>
        <Button
          type="primary"
          onClick={() => onClick!(ModalType.User)}
          style={shareContext.shareUserOpen ? { background: 'var(--yb-button-select-color)' } : {}}
        >
          选择分享用户
        </Button>
        {!disableGroup && (
          <Button
            type="primary"
            onClick={() => onClick!(ModalType.Group)}
            style={
              shareContext.shareGroupOpen ? { background: 'var(--yb-button-select-color)' } : {}
            }
          >
            选择共享群
          </Button>
        )}
        {hasColumns && (
          <Button
            type="primary"
            onClick={() => onClick!(ModalType.Data)}
            style={shareContext.selectOpen ? { background: 'var(--yb-button-select-color)' } : {}}
          >
            选择{dataTitle}
          </Button>
        )}

        {secure && (
          <div>
            <span>保密等级：</span>
            <Select
              className={styles.headerSelect}
              dropdownStyle={{ width: getWidth(160) }}
              onChange={handleChange}
              options={options}
              ref={selectRef}
              value={String(secureValue)}
              placeholder="请选择"
              placement={'bottomLeft'}
              allowClear
              popupClassName={styles.dropDown}
            />
          </div>
        )}
      </Space>
    </div>
  );
};

export default Component;
