import { getWidth } from '@/utils/common';
import { Button, Form, Input, List, Space } from 'antd';
import type { FilterDropdownProps } from 'antd/es/table/interface';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import { useImmer } from 'use-immer';
import styles from './index.module.less';

export interface FieldNames {
  value?: string;
  label?: string;
  groupLabel?: string;
  options?: string;
}

export interface DataSelectProps<T> extends FilterDropdownProps {
  mode?: 'single' | 'mutiple';
  title: string;
  filedNames: FieldNames;
  defaultData?: T[];
  fetchData?: (params: any) => Promise<any>;
  selectKeys?: string[];
  onConfirm: (data: string[]) => void;
  onReset: () => void;
  visible: boolean;
  searchKey?: string; // 搜索关键字 属性
}

const DataSelect = ({
  mode,
  title,
  filedNames,
  defaultData,
  selectKeys,
  close,
  onConfirm,
  fetchData,
  onReset,
  setSelectedKeys,
  searchKey,
}: DataSelectProps<any>) => {
  useEffect(() => {
    if (defaultData) {
      setData(defaultData);
    }
    if (selectKeys) {
      _setSelectedKeys(selectKeys);
    }
  }, [defaultData, selectKeys]);

  const [data, setData] = useState<any>([]);

  // TODO: 分页查询
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(1000);
  const [keywords, setKeywords] = useState<string[]>([]);

  const getList = () => {
    const params: any = { pageNo, pageSize };
    if (keywords && keywords.length && searchKey) {
      params[searchKey] = keywords;
    }
    fetchData &&
      fetchData(params).then((res: any) => {
        setData(res.data.list || res.data);
      });
  };
  useEffect(() => {
    if (fetchData) {
      getList();
    }
  }, [keywords]);

  const [selectedData, setSelectedData] = useImmer<any[]>([]);
  const [_selectedKeys, _setSelectedKeys] = useImmer<string[]>([]);

  const [form] = Form.useForm();

  const key = filedNames?.value || 'value';

  const label = filedNames?.label || 'label';

  const handleSelect = (item: any) => {
    const index = _selectedKeys.findIndex((sItem: string) => sItem === item[key]);
    if (index > -1) {
      // 已选
      setSelectedData((draft) => {
        draft.splice(index, 1);
      });
      _setSelectedKeys((draft) => {
        draft.splice(index, 1);
      });
    } else {
      setSelectedData((draft) => {
        draft.push(item);
      });
      _setSelectedKeys((draft) => {
        draft.push(item[key]);
      });
    }
  };

  const handleCancelSelect = (item: any) => {
    setSelectedData((draft) => draft.filter((sItem: any) => sItem[key] !== item[key]));
    _setSelectedKeys((draft) => draft.filter((sItem: string) => sItem !== item[key]));
  };

  const searchAll = () => {
    // 全部查询
    const keyword = form.getFieldValue('keyword');
    if (keyword.trim()) {
      setKeywords([keyword]);
    } else {
      setKeywords([]);
    }
  };

  const searchResult = () => {
    // 结果查询
    const keyword = form.getFieldValue('keyword');
    if (keyword.trim()) {
      setKeywords([...keywords, keyword]);
    } else {
      setKeywords([]);
    }
  };

  const confirm = () => {
    setSelectedKeys && setSelectedKeys(_selectedKeys);
    onConfirm(_selectedKeys);
    close();
  };

  const onClose = () => {
    setSelectedData([]);
    _setSelectedKeys([]);
    onReset();
  };

  return (
    <div className={classNames(styles.dataWrapper, mode === 'single' ? styles.single : undefined)}>
      <div className={styles.header}>
        <div className={styles.col}>
          <h3>选择{title}</h3>
          {/* <Button type="primary" ghost>
            关闭
          </Button> */}
        </div>
        {mode !== 'single' && (
          <div className={styles.col}>
            <h3>已选{title}</h3>
          </div>
        )}
      </div>
      <div className={styles.content}>
        <div className={styles.col}>
          <div className={styles.filter}>
            <Form form={form} layout="inline">
              <Form.Item label="" name="keyword">
                <Input placeholder="请输入关键字" style={{ width: getWidth(133) }} allowClear />
              </Form.Item>
              <Form.Item label="">
                <Button type="primary" size="middle" onClick={searchAll}>
                  全部查询
                </Button>
              </Form.Item>
              <Form.Item label="">
                <Button type="primary" onClick={searchResult}>
                  结果查询
                </Button>
              </Form.Item>
            </Form>
          </div>
          <List className={styles.list}>
            {data.map((item: any) => {
              return (
                <List.Item
                  className={classNames(
                    styles.item,
                    _selectedKeys.includes(item[key]) ? styles.active : undefined,
                  )}
                  key={item[key]}
                >
                  <Button
                    type="primary"
                    ghost
                    size="small"
                    onClick={handleSelect.bind(undefined, item)}
                  >
                    {_selectedKeys.includes(item[key])
                      ? '已选'
                      : mode === 'single'
                        ? '选择'
                        : '多选'}
                  </Button>
                  <span>{item[label]}</span>
                </List.Item>
              );
            })}
          </List>
        </div>
        {mode !== 'single' && (
          <div className={styles.col}>
            <List className={styles.list}>
              {selectedData.map((item: any) => {
                return (
                  <List.Item className={styles.item} key={item[key]}>
                    <Button
                      type="primary"
                      ghost
                      size="small"
                      onClick={handleCancelSelect.bind(undefined, item)}
                    >
                      取消
                    </Button>
                    <span>{item[label]}</span>
                  </List.Item>
                );
              })}
            </List>
          </div>
        )}
      </div>
      <div className={styles.footer}>
        <Space>
          {mode === 'single' ? (
            <Button type="primary" ghost onClick={close}>
              取消
            </Button>
          ) : (
            <Button type="text" onClick={onClose}>
              重置
            </Button>
          )}
          <Button type="primary" onClick={confirm}>
            确定
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default DataSelect;
