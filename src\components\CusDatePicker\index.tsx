import { FC, useEffect, useState } from 'react';
import { <PERSON><PERSON>, Drawer } from 'antd';
import { DrawerClassNames } from 'antd/es/drawer/DrawerPanel';
import Styles from './index.module.less';

export type selectDataType = {
  year: number;
  month: number;
  day: number;
  fullDate: Date;
}

export type CusDataPickerType =  {
  isShowCusDate: boolean;
  title?: string
  onSelectDate: (selectData: selectDataType) => void;
}

/**
 * 自定义级联日期选择器
 * @constructor
 */
const CusDatePicker: FC<CusDataPickerType> = ({ isShowCusDate, title, onSelectDate }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [dayArray, setDayArray] = useState<number[]>([]);
  const [selectYear, setSelectYear] = useState<number>(new Date().getFullYear());
  const [selectMonth, setSelectMonth] = useState<number>(new Date().getMonth() + 1);
  const [selectDay, setSelectDay] = useState<number>(new Date().getDate());
  const [hasMounted, setHasMounted] = useState(false);

  /**
   * 根据年份和月份获取天数
   * @param year 年
   * @param month 月
   */
  const getDaysInMonth = (year: number, month: number): number => {
    const nextMonthFirstDay = new Date(year, month, 1);
    const lastDayOfPrevMonth = new Date(nextMonthFirstDay.setDate(0));
    return lastDayOfPrevMonth.getDate();
  }

  //设置年数组
  const yearArray: number[] = [];
  for (let year = new Date().getFullYear(); year <= 2099; year++) {
    yearArray.push(year);
  }

  //设置月数组
  const monthArray: number[] = [];
  for (let month: number = 1; month <= 12; month++) {
    monthArray.push(month);
  }

  useEffect(() => {
    //设置日数组
    const dayArrayTemp: number[] = [];
    for(let day: number = 1; day <= getDaysInMonth(selectYear, selectMonth); day++) {
      dayArrayTemp.push(day);
    }
    setDayArray(dayArrayTemp);
  }, [selectYear, selectMonth]);

  useEffect(() => {
    if (hasMounted) {
      setIsVisible(!isVisible);
    } else {
      setHasMounted(true);
    }
  }, [isShowCusDate]);

  const onFinish = () => {
    const selectDate: selectDataType = {
      year: selectYear,
      month: selectMonth,
      day: selectDay,
      fullDate: new Date(selectYear, selectMonth - 1, selectDay)
    }
    onSelectDate(selectDate);
    setIsVisible(false);
  }

  if(!isVisible) return null;

  const classNames: DrawerClassNames = {
    body: Styles.body,
    header: Styles.headerStyle,
    content: Styles.body,
  };

  return (
    <>
      <Drawer
        zIndex={3000}
        placement="bottom"
        open={isVisible}
        getContainer={false}
        style={{ position: 'absolute' }}
        key="bottom"
        height={338}
        keyboard={false}
        maskClosable={false}
        classNames={classNames}
      >
        <div className={Styles.customHeaderBox}>
          <Button className={Styles.closeButton} onClick={() => setIsVisible(false)}>关闭</Button>
          <div className={Styles.titleBox}>{title || '异常日期'}</div>
          <Button type="link" onClick={onFinish}>完成</Button>
        </div>
        <div className={Styles.customContainer}>
          <div className={Styles.customBody}>
            <div className={Styles.itemBox}>
              <div className={Styles.itemTitle}>请选择</div>
              <div className={Styles.scrollBox}>
                {yearArray.map((year) =>
                  <div key={year}
                       className={Styles.item}
                       onClick={() => setSelectYear(year)}
                       style={{
                         color: year === selectYear ? '#3D5AFE' : ''
                       }}
                  >{year}年</div>)
                }
              </div>
            </div>
            <div className={Styles.itemBox}>
              <div className={Styles.itemTitle}>请选择</div>
              <div className={Styles.scrollBox}>
                {monthArray.map((month) =>
                  <div key={month}
                       className={Styles.item}
                       onClick={() => setSelectMonth(month)}
                       style={{
                         color: month === selectMonth ? '#3D5AFE' : ''
                       }}
                  >{month}月</div>)
                }
              </div>
            </div>
            <div className={Styles.itemBox}>
              <div className={Styles.itemTitle}>请选择</div>
              <div className={Styles.scrollBox}>
                {dayArray.map((day) =>
                  <div key={day}
                       className={Styles.item}
                       onClick={() => setSelectDay(day)}
                       style={{
                         color: day === selectDay ? '#3D5AFE' : ''
                       }}
                  >{day}日</div>)
                }
              </div>
            </div>
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default CusDatePicker;




