import { CaretDownOutlined } from '@ant-design/icons';
import { Button, Flex, Input, Select, message } from 'antd';
import {
  FC,
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

import styles from '../../index.module.less';

import { deleteMember, deleteMobileConfig, deletedBill } from '@/api/account';
import ConfirmDialog from '@/components/Common/ConfirmDialog.tsx';
import ModalSelect from '@/components/DataBank/ModalSelect';
import RootContext from '@/components/DataBank/ModalSelect/context';
import ModalTip from '@/components/DataBank/ModalTip/index.tsx';
import TimePopover from '@/components/DataBank/TimeDelModal/index.tsx';
import useUserStore from '@/store/useUserStore';
import { tipsConfigAll, titleConfigMap } from './tipsConfig';
import { useSelectChange } from './useSelectChange';

interface childProps {
  queryData: any;
  getActiveIndex: any;
  activeIndex: string;
  fromModule?: 'dataBank' | 'recycle';
}
type delType =
  | 'delSelect'
  | 'delForverSelect'
  | 'delCancelSelect'
  | 'delAll'
  | 'delForverAll'
  | 'delCancelAll';

const SearchHead: FC<childProps> = forwardRef(
  ({ queryData, getActiveIndex, activeIndex, fromModule }, ref) => {
    const goBack = () => {
      window.history.back();
    };
    const options = [
      { value: '1', label: '工作手机配置记录' },
      { value: '2', label: '备用手机配置记录' },
      { value: '3', label: '支付历史记录' },
      { value: '4', label: '子账户配置' },
    ];
    const [messageApi, contextHolder] = message.useMessage();
    const [isModalOpen, setIsModalOpen] = useState<any>(false);
    const [isTipOpen, setisTipOpen] = useState<any>(false);
    const [isConfirmOpen, setIsConfirmOpen] = useState<any>(false);
    const [version, setVersion] = useState<any>(1);
    const selectType = useRef<delType>('delSelect');
    const [titleConfig, setTitleConfigConfig] = useState(titleConfigMap[activeIndex]);
    const [mobileVal, setMobileVal] = useState<string>('');
    const [mobileStore, setMobileStore] = useState<any>([]); // 缓存手机号结果查询
    const [realName, setRealName] = useState<string>('');
    const [nameStore, setNameStore] = useState<any>([]); // 缓存名字结果查询
    const [payChannelCode, setPayChannelCode] = useState<string>('');
    const [queryInfo, setQueryInfo] = useState<any>({
      pageNo: 1,
      pageSize: 20,
      mobile: [],
      realName: [],
      payChannelCode: '', // 支付方式
      // payRemark: '', // 支付备注
    });
    const childRef: any = useRef(null);
    const loginUserInfo = useUserStore((state) => state.userInfo);

    useImperativeHandle(
      ref,
      () => ({
        setSearchVal,
      }),
      [open],
    );
    // 获取table表头过滤的请求
    const setSearchVal = (obj: any) => {
      setQueryInfo({ ...queryInfo, ...obj });
    };

    const {
      data,
      rightData,
      columns,
      rightColums,
      queryResult,
      queryAll,
      setRightData,
      formSearchList,
      resetTableFn,
    } = useSelectChange(activeIndex, fromModule);
    const delCount = useRef(rightData.length); // 更新删除count
    const [tipsConfig, setTipsConfig] = useState<any>(tipsConfigAll(activeIndex, delCount.current)); // 当前模块的大对象
    const [mapInfo, setleMapInfo] = useState(tipsConfig[selectType.current]); // 当前对象的某个操作信息

    const selectFn = (val: string) => {
      getActiveIndex(val);
      setQueryInfo({
        pageNo: 1,
        pageSize: 20,
        mobile: [],
        payChannelCode: '', // 支付方式
        // payRemark: '', // 支付备注
      });
    };
    const handleOpen = (isOpen: boolean) => {
      setIsModalOpen(isOpen);
      setVersion(version + 1);
    };
    // 支付方式变更
    const handleSelectChange = (val: any) => {
      setPayChannelCode(val);
    };

    // 全部查询
    const allQueryFn = () => {
      if (activeIndex === '1' || activeIndex === '2') {
        setMobileStore([mobileVal]);
        setQueryInfo({ ...queryInfo, mobile: [mobileVal] });
      } else if (activeIndex === '3') {
        setQueryInfo({ ...queryInfo, payChannelCode });
      } else if (activeIndex === '4') {
        setNameStore([realName]);
        setQueryInfo({ ...queryInfo, realName: [realName] });
      }
    };

    // 结果查询
    const resultQueryFn = () => {
      if (activeIndex === '1' || activeIndex === '2') {
        setMobileStore([...mobileStore, mobileVal]);
        setQueryInfo({ ...queryInfo, mobile: [...mobileStore, mobileVal] });
      } else if (activeIndex === '3') {
        setQueryInfo({ ...queryInfo, payChannelCode });
      } else if (activeIndex === '4') {
        setNameStore([...nameStore, realName]);
        setQueryInfo({ ...queryInfo, realName: [...nameStore, realName] });
      }
    };

    // 选择删除[数据银行] 选择永久删除[回收站] 选择取消删除[回收站]
    const delSelect = (key: delType) => {
      selectType.current = key;
      setleMapInfo(tipsConfig[key]);
      setTitleConfigConfig(titleConfigMap[activeIndex]);
      setIsModalOpen(true);
    };
    // 全部删除[数据银行]
    const delAll = (key: delType) => {
      if (!data.length) {
        return messageApi.warning('没有可操作的数据！');
      }
      selectType.current = key;
      setleMapInfo(tipsConfig[key]);
      setIsConfirmOpen(true);
    };
    // 全部删除确认【全部删除】
    const confirmDel = async () => {
      let api = null;
      const obj: any = {
        // 	配置类型：1-配置备用手机,2-修改工作手机,示例值(1)
        optType: activeIndex == '2' ? 1 : 2,
        // 删除标识,0-未删除，1-临时删除，2-永久删除,示例值(1)
        deleted: selectType.current == 'delAll' ? 1 : selectType.current == 'delCancelAll' ? 0 : 2,
        ids: rightData.map((itm: any) => itm.id),
      };
      if (activeIndex === '1' || activeIndex === '2') {
        api = deleteMobileConfig;
        delete obj.ids;
      } else if (activeIndex === '4') {
        api = deleteMember;
      } else {
        api = deletedBill;
        obj.userId = loginUserInfo?.id || '';
        obj.type = fromModule == 'recycle' ? 'RECYCLE_BIN' : 'BANK_DATA';
        delete obj.optType;
      }
      await api(obj);
      setIsConfirmOpen(false);
      setisTipOpen(true);
      setQueryInfo({ ...queryInfo });
    };
    // 确认删除【选择删除】
    const confirmSelect = async (timeObj?: any, type?: string) => {
      let api = null;
      let obj: any = {
        // 	配置类型：1-配置备用手机,2-修改工作手机,示例值(1)
        optType: activeIndex == '2' ? 1 : 2,
        // 删除标识,0-未删除，1-临时删除，2-永久删除,示例值(1)
        deleted:
          selectType.current == 'delSelect' ? 1 : selectType.current == 'delCancelSelect' ? 0 : 2,
        ids: rightData.map((itm: any) => itm.id),
      };
      // 数据银行/回收站
      if (timeObj) {
        obj = {
          ...obj,
          ...timeObj,
          deleted: type == 'delSelect' ? 1 : type == 'delCancelSelect' ? 0 : 2,
        };
      }
      if (activeIndex === '1' || activeIndex === '2') {
        api = deleteMobileConfig;
        // delete obj.ids;
      } else if (activeIndex === '4') {
        api = deleteMember;
      } else {
        api = deletedBill;
        obj.userId = loginUserInfo?.id || '';
        obj.type = fromModule == 'recycle' ? 'RECYCLE_BIN' : 'BANK_DATA';
        delete obj.optType;
      }
      await api(obj);
      setIsModalOpen(false);
      setisTipOpen(true);
      setQueryInfo({ ...queryInfo });
      timeObj && childRef.current.callbackHandle();
    };

    // 重置搜索
    const resetFn = () => {
      setQueryInfo({
        pageNo: 1,
        pageSize: 20,
        mobile: [],
        payChannelCode: '',
        // payRemark: '',
      });
      setMobileVal('');
      setMobileStore([]);
      setRealName('');
      setNameStore([]);
      setPayChannelCode('');
    };

    // 按时间删除
    const confirmDelTime = async (params: any) => {
      if (fromModule == 'dataBank') {
        confirmSelect(params, 'delSelect');
      } else {
        confirmSelect(params, 'delForverAll');
      }
    };
    // 按时间取消删除
    const cancelDeleteTime = (params: any) => {
      confirmSelect(params, 'delCancelSelect');
    };

    useEffect(() => {
      resetFn();
    }, [activeIndex]);

    // 触发接口重新查询列表
    useEffect(() => {
      queryData({ ...queryInfo });
    }, [queryInfo]);

    useEffect(() => {
      delCount.current = rightData.length;
      setTipsConfig(tipsConfigAll(activeIndex, delCount.current));
    }, [rightData]);

    // 更新删除的文件数量
    useEffect(() => {
      setleMapInfo(tipsConfig[selectType.current]);
    }, [tipsConfig]);

    useEffect(() => {
      if (!isModalOpen) {
        resetTableFn();
        setRightData([]);
      }
    }, [isModalOpen]);

    return (
      <>
        {contextHolder}
        {/* 搜索菜单栏 */}
        <div className={styles.headerSearch}>
          <div className={styles.searchLeft}>
            <Button type="primary" className={styles.close} onClick={goBack}>
              返回
            </Button>
            {(activeIndex == '1' || activeIndex == '2') && (
              <Flex className={`${styles.flexBox} ml-3`}>
                <span>{activeIndex == '1' ? '工作手机' : '备用手机'}</span>
                <Input
                  placeholder={`请输入${activeIndex == '1' ? '工作' : '备用'}工作手机查询`}
                  value={mobileVal}
                  onChange={(e) => setMobileVal(e.target.value)}
                />
              </Flex>
            )}
            {activeIndex == '3' && (
              <Flex className={`${styles.flexBox} ml-3`}>
                <span>支付方式</span>
                <Select
                  style={{ width: 180 }}
                  value={payChannelCode}
                  onChange={handleSelectChange}
                  options={[
                    { value: 'wx_native', label: '微信' },
                    { value: 'alipay_qr', label: '支付宝' },
                  ]}
                />
                {/* <span className="ml-3">备注</span>
                <Input
                  placeholder="请输入"
                  value={queryInfo.payRemark}
                  onChange={(e) => setQueryInfo({ ...queryInfo, payRemark: e.target.value })}
                /> */}
              </Flex>
            )}
            {activeIndex == '4' && (
              <Flex className={`${styles.flexBox} ml-3`}>
                <span>姓名</span>
                <Input
                  placeholder="请输入"
                  value={realName}
                  onChange={(e) => setRealName(e.target.value)}
                />
              </Flex>
            )}

            <div className={styles.btns}>
              <Button type="primary" className="mr-3" onClick={allQueryFn}>
                全部查询
              </Button>
              <Button type="primary" onClick={resultQueryFn}>
                结果查询
              </Button>
            </div>
          </div>
          <div className={styles.searchRight}>
            <Flex className={`${styles.flexBox} mr-[10px]`}>
              <Select
                style={{ width: 200 }}
                value={activeIndex}
                placeholder="选择"
                suffixIcon={<CaretDownOutlined style={{ color: 'rgba(0, 0, 0, 0.85)' }} />}
                onChange={selectFn}
                popupClassName="optionsWrap"
                options={options}
              />
            </Flex>
            {/* 数据银行 */}
            {fromModule == 'dataBank' && (
              <div className="flex">
                <Button type="primary" className="mr-3" onClick={() => delSelect('delSelect')}>
                  选择删除
                </Button>
                <Button type="primary" onClick={() => delAll('delAll')}>
                  全部删除
                </Button>
                <TimePopover
                  confirmDelTime={confirmDelTime}
                  ref={childRef}
                  from="dataBank"
                ></TimePopover>
              </div>
            )}
            {/* 回收站 */}
            {fromModule == 'recycle' && (
              <div className="flex">
                <Button
                  type="primary"
                  className="mr-3"
                  onClick={() => delSelect('delForverSelect')}
                >
                  选择永久删除
                </Button>
                <Button type="primary" className="mr-3" onClick={() => delAll('delForverAll')}>
                  全部永久删除
                </Button>
                <Button
                  type="primary"
                  className="mr-3"
                  onClick={() => delSelect('delCancelSelect')}
                >
                  选择取消删除
                </Button>
                <Button type="primary" onClick={() => delAll('delCancelAll')}>
                  取消全部删除
                </Button>
                <TimePopover
                  confirmDelTime={confirmDelTime}
                  ref={childRef}
                  from="recycle"
                  text="按时间永久删除"
                  operateType={2}
                ></TimePopover>
                <TimePopover
                  confirmDelTime={cancelDeleteTime}
                  ref={childRef}
                  from="recycle"
                  text="按时间取消删除"
                  operateType={0}
                  confirmText="确认取消删除"
                ></TimePopover>
              </div>
            )}
          </div>
        </div>
        <RootContext.Provider
          value={{
            ...useContext(RootContext),
            titleConfig,
            queryResult,
            queryAll,
            confirmSelect,
            formConfig: formSearchList,
            data,
          }}
        >
          <ModalSelect
            isModalOpen={isModalOpen}
            setIsModalOpen={handleOpen}
            columns={columns}
            data={data}
            rightData={rightData}
            rightColums={rightColums}
            key={version}
            setRightData={setRightData}
            secondConfirmTitle={mapInfo.secondConfirmTitle}
            secondConfirmOkText={mapInfo.secondConfirmOkText}
            content={mapInfo.delSelectSpan}
          ></ModalSelect>
        </RootContext.Provider>
        <ModalTip isTipOpen={isTipOpen} setisTipOpen={setisTipOpen} title={mapInfo.tipTitle}>
          {mapInfo.tipContent}
        </ModalTip>
        <ConfirmDialog
          open={isConfirmOpen}
          onOk={confirmDel}
          onCancel={() => setIsConfirmOpen(false)}
          title={mapInfo.secondConfirmTitle}
          okText={mapInfo.secondConfirmOkText}
          content={mapInfo.delSelectSpan}
        ></ConfirmDialog>
      </>
    );
  },
);

export default SearchHead;
