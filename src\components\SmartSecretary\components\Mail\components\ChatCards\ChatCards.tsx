import { MailBookDataType, MailChatAttachVOList } from '@/api/mail/chat/mailModels';
import sendAttach from '@/assets/images/mail/sendAttach.png';
import sendContent from '@/assets/images/mail/sendContent.png';
import sendTitle from '@/assets/images/mail/sendTitle.png';
import type { FilePreviewAPI } from '@/components/FliePreview';
import FliePreview from '@/components/FliePreview';
import useInView from '@/hooks/useInView';
import Collapse from '@/pages/Mail/components/EditCard/Collapse';
import Preview from '@/pages/Mail/components/Preview/Index';
import useUserStore from '@/store/useUserStore';
import { formatDate } from '@/utils/date';
import { parserHtmlToString } from '@/utils/parser';
import { Flex, message } from 'antd';
import classNames from 'classnames';
import { FC, useEffect, useRef, useState } from 'react';
import CollapseFiles from '../EditCard/CollapseFiles';
import styles from './ChatCards.module.less';
import CountDown from './CountDown/Index';
import ItemSpan from './ItemSpan/Index';

type ChildProps = {
  item: MailBookDataType;
  onInView?: (data: MailBookDataType) => void;
  handClickReply: (type: number, data: MailBookDataType) => void;
  showDetail?: boolean;
  checkReadFlag?: (data: MailBookDataType) => void;
};

/**
 * 卡片
 */
const ChatCards: FC<ChildProps> = ({
  item,
  onInView,
  handClickReply,
  showDetail = false,
  checkReadFlag,
}) => {
  const filePreviewRef = useRef<FilePreviewAPI>();
  const [targetRef, inView] = useInView({ root: null, rootMargin: '30px', threshold: 1 }, true);
  const imgList = [sendTitle, sendContent, sendAttach];
  const [mailFile, setMailFile] = useState<MailChatAttachVOList[]>([]);
  const [contentFile, setContentFile] = useState<MailChatAttachVOList[]>([]);
  const [imageFile, setimageFile] = useState<MailChatAttachVOList[]>([]);

  useEffect(() => {
    if (item.mailChatAttachVOList?.length > 0) {
      setMailFile(
        item.mailChatAttachVOList.filter((item) => !item.mediaType || item.mediaType == 0),
      ); //普通
      setContentFile(
        item.mailChatAttachVOList.filter(
          (item) => item.mediaType && item.mediaType !== 0 && item.mediaType !== 3,
        ),
      ); //音视频正文
      setimageFile(
        item.mailChatAttachVOList.filter((item) => item.mediaType && item.mediaType === 3),
      );
    }
  }, [item]);

  useEffect(() => {
    if (inView) {
      // 邮件卡片进入可视区域
      if (onInView) {
        //判断是否是未读状态，只有未读需改变状态
        onInView(item);
      }
    }
  }, [inView]);
  const [expanded, setExpanded] = useState<boolean>(false);
  const [showDetails, setShowDetails] = useState<boolean>(showDetail);
  useEffect(() => {
    setExpanded(showDetails);
  }, [showDetails]);

  const editRevoke = () => {
    checkReadFlag && checkReadFlag(item);
  };

  const [pre, setPre] = useState<any>({
    show: false,
    fileName: '',
    filePath: '',
  });
  const handleClick = (e: any) => {
    setPre({
      show: true,
      fileName: '无标题',
      filePath: e.target.getAttribute('src'),
    });
  };
  const handleImage = (val: any) => {
    setPre({
      show: true,
      fileName: val.attachmentName,
      filePath: val.attachmentUrl,
    });
  };

  useEffect(() => {
    if (showDetails) {
      setTimeout(() => {
        const parent = document.getElementById(`div-${item.id}`);
        const parentDiv = parent!.querySelector('#myDiv');
        const allImgs = parentDiv!.querySelectorAll('img');
        allImgs.forEach((img, index) => {
          img.addEventListener('click', handleClick);
        });
      }, 300);
    }
  }, [showDetails]);
  const userInfo = useUserStore((state) => state.userInfo);
  const preview = (val: any) => {
    if (item.mailStatus === 2 && item.revokeFlag === 1) {
      return message.info('该邮件已撤回，无法播放');
    }
    filePreviewRef.current?.open({
      id: val.attachmentId || '302470223',
      title: val.attachmentName,
      fileSize: val.attachmentSize,
      visitPath: val.attachmentUrl,
      fileType: val.fileType || val.attachmentName.split('.')[1] || '',
      fileFormatType: val.fileFormatType,
      safeLevel: item.mailStatus === 1 ? 0 : item.confidentialityStatus,
      filePath: val.filePath || val.attachmentUrl,
      userId: userInfo?.id,
      source: 0,
    });
  };

  return (
    <div
      id={`div-${item.id}`}
      ref={targetRef}
      key={item.id as string}
      className={classNames(styles.card, item.mailStatus == 1 ? styles.send : '')}
    >
      <Flex vertical>
        <div className={styles.cardHeader} onClick={() => setShowDetails(!showDetails)}>
          <Flex className={styles.itemPar} justify={'space-between'}>
            <Flex>
              <span className={classNames(styles.editSpan)}>
                <span className={styles.textSpan}>
                  {item.mailStatus == 2 ? '发件人' : '收件人'}
                </span>
                ：
              </span>
              {item.mailStatus == 2 ? (
                <span
                  className={styles.coll}
                  title={
                    `${item.senderName ? item.senderName : ''}` +
                    `${item.username ? item.username : item.senderAddress}`
                  }
                >
                  {`${item.senderName ? item.senderName : ''}` +
                    `${item.username ? item.username : item.senderAddress}`}
                </span>
              ) : (
                <div className={styles.headerContent}>
                  <Collapse
                    items={[
                      ...item.addressList.map(
                        (i) =>
                          `${i.addresseeName ? i.addresseeName : ''}` +
                          `${i.username ? i.username : i.addressee}`,
                      ),
                      ...item.addressGroupList.map(
                        (i) =>
                          `${i.groupName}(${i.contactPersonVOS?.length ? i.contactPersonVOS.length : 0})`,
                      ),
                    ]}
                    expand={showDetails}
                  ></Collapse>
                </div>
              )}
            </Flex>
            <Flex justify={'flex-end'} align={'center'}>
              <span className={styles.titleSpan}>
                {item.mailStatus == 2 ? '已收邮件' : '已发邮件'}
              </span>
            </Flex>
          </Flex>
          <Flex className={styles.itemPar} justify={'space-between'}>
            <Flex>
              <span className={styles.editSpan}>
                <span className={styles.textSpan}>{'时间'}</span>：
              </span>
              <span>{formatDate(item.sendTime, 'YYYY-MM-DD HH:mm:ss')}</span>
            </Flex>
            <Flex align={'center'}>
              <ItemSpan status={item.confidentialityStatus}></ItemSpan>
            </Flex>
          </Flex>
          {showDetails && (
            <>
              <Flex className={styles.itemPar}>
                <span className={styles.editSpan}>
                  <span className={styles.textSpan}>
                    {item.mailStatus == 1 ? '发件人' : '收件人'}
                  </span>
                  ：
                </span>
                {item.mailStatus == 1 ? (
                  <span
                    className={styles.coll}
                    title={
                      `${item.senderName ? item.senderName : ''}` +
                      `${item.username ? item.username : item.senderAddress}`
                    }
                  >
                    {`${item.senderName ? item.senderName : ''}` +
                      `${item.username ? item.username : item.senderAddress}`}
                  </span>
                ) : (
                  <div className={styles.headerContent}>
                    <Collapse
                      items={[
                        ...item.addressList.map(
                          (i) =>
                            `${i.addresseeName ? i.addresseeName : ''}` +
                            `${i.username ? i.username : i.addressee}`,
                        ),
                        ...item.addressGroupList.map(
                          (i) =>
                            `${i.groupName}(${i.contactPersonVOS?.length ? i.contactPersonVOS.length : 0})`,
                        ),
                      ]}
                      expand={showDetails}
                    ></Collapse>
                  </div>
                )}
              </Flex>
              {(item.copyAddressList.length > 0 || item.copyAddressGroupList.length > 0) && (
                <Flex className={styles.itemPar}>
                  <span className={styles.editSpan}>
                    <span className={styles.textSpan}>抄送</span>：
                  </span>
                  <Flex justify={'flex-start'} align={'flex-start'} gap={'small'} wrap>
                    {item.copyAddressList.map((i) => {
                      return (
                        <span
                          key={i.addressee}
                          className={styles.coll}
                          title={
                            `${i.addresseeName ? i.addresseeName : ''}` +
                            `${i.username ? i.username : i.addressee}`
                          }
                        >
                          {`${i.addresseeName ? i.addresseeName : ''}` +
                            `${i.username ? i.username : i.addressee}`}
                        </span>
                      );
                    })}
                    {item.copyAddressGroupList.map((i) => {
                      return (
                        <span
                          key={i.groupId}
                          className={styles.coll}
                          title={`${i.groupName}(${i.contactPersonVOS?.length ? i.contactPersonVOS.length : 0})`}
                        >{`${i.groupName}(${i.contactPersonVOS?.length ? i.contactPersonVOS.length : 0})`}</span>
                      );
                    })}
                  </Flex>
                </Flex>
              )}
              {item.secretAddressList.length > 0 && (
                <Flex className={styles.itemPar} justify={'flex-start'}>
                  <span className={styles.editSpan}>
                    <span className={styles.textSpan}>密送</span>：
                  </span>
                  <Flex justify={'flex-start'} align={'flex-start'} gap={'small'} wrap>
                    {item.secretAddressList.map((i) => {
                      return (
                        <span
                          key={i.addressee}
                          className={styles.coll}
                          title={
                            `${i.addresseeName ? i.addresseeName : ''}` +
                            `${i.username ? i.username : i.addressee}`
                          }
                        >
                          {`${i.addresseeName ? i.addresseeName : ''}` +
                            `${i.username ? i.username : i.addressee}`}
                        </span>
                      );
                    })}
                  </Flex>
                </Flex>
              )}
            </>
          )}
        </div>
        <div className={styles.cardContent}>
          <Flex vertical>
            <Flex className={styles.itemPar} justify={'flex-start'} align={'center'}>
              <img className={styles.imgStyles} src={imgList[0]} alt="" />
              <span className={styles.titleBox}>{item.mailTitle}</span>
            </Flex>
            <Flex className={styles.itemPar} justify={'flex-start'}>
              <img className={styles.imgStyles} src={imgList[1]} alt="" />
              <div className={styles.contentBox}>
                {expanded ? (
                  <>
                    <div id="myDiv" dangerouslySetInnerHTML={{ __html: item.mailContent }}></div>
                    {imageFile.length > 0 &&
                      imageFile.map((item) => {
                        return (
                          <img
                            src={item.attachmentUrl}
                            key={item.id}
                            style={{ marginBottom: '5px' }}
                            onClick={(e: any) => {
                              handleImage(item);
                            }}
                          />
                        );
                      })}
                    {contentFile.length > 0 &&
                      contentFile.map((item) => {
                        return (
                          <span className={styles.videoSpan} onClick={() => preview(item)}>
                            {item.attachmentName}
                          </span>
                        );
                      })}
                    <Flex justify={'flex-end'} style={{ margin: '5px' }}>
                      <span
                        className={styles.symbolSpan}
                        onClick={(e) => {
                          setExpanded(false);
                        }}
                      >
                        折叠
                      </span>
                    </Flex>
                  </>
                ) : (
                  <>
                    <div className={styles.paragraph}>{parserHtmlToString(item.mailContent)}</div>
                    {imageFile.length > 0 && (
                      <img
                        src={imageFile[0].attachmentUrl}
                        key={imageFile[0].id}
                        style={{ marginBottom: '5px' }}
                        onClick={(e: any) => {
                          handleImage(imageFile[0]);
                        }}
                      />
                    )}
                    {contentFile.length > 0 && (
                      <span className={styles.videoSpan} onClick={() => preview(contentFile[0])}>
                        {contentFile[0].attachmentName}
                      </span>
                    )}
                    <Flex justify={'flex-end'} style={{ margin: '5px' }}>
                      <span
                        className={styles.symbolSpan}
                        onClick={(e) => {
                          setExpanded(true);
                        }}
                      >
                        展开
                      </span>
                    </Flex>
                  </>
                )}
              </div>
            </Flex>
            <Flex className={styles.itemPar} justify={'space-between'}>
              <Flex>
                <img className={styles.imgStyles} src={imgList[2]} alt="" />
                <div className={styles.annexBox}>
                  {mailFile.length > 0 && (
                    <CollapseFiles
                      sendType={item.mailStatus === 1 ? 'send' : 'from'}
                      items={mailFile}
                      safeLevel={item.confidentialityStatus}
                      revokeFlag={item.revokeFlag}
                    ></CollapseFiles>
                  )}
                </div>
              </Flex>
              <Flex className={styles.annexSpan} gap={5}>
                <span
                  className={styles.huifu}
                  onClick={(e) => {
                    handClickReply(1, item);
                  }}
                >
                  {item.mailStatus === 1 ? '续发' : '回复'}
                </span>
                <span
                  className={styles.huifu}
                  onClick={(e) => {
                    handClickReply(2, item);
                  }}
                >
                  转发
                </span>
              </Flex>
            </Flex>
            {[4, 5, 7].includes(item.confidentialityStatus) &&
              !item.revokeFlag &&
              item.timedTime && (
                <Flex className={styles.itemPar} justify="flex-end" gap={10}>
                  <CountDown
                    duration={+item.timedTime}
                    currentTime={item.serverCurrentTime as number}
                  ></CountDown>
                  {item.mailStatus == 2 &&
                    [6, 7].includes(item.confidentialityStatus) &&
                    item.revokeFlag == 0 && (
                      <span
                        className={styles.redeSpan}
                        onClick={(e) => {
                          editRevoke();
                        }}
                      >
                        已阅
                      </span>
                    )}
                </Flex>
              )}
          </Flex>
        </div>
      </Flex>
      <FliePreview ref={filePreviewRef} zIndex={1000} />
      {pre.show && (
        <Preview
          fileName={pre.fileName}
          filePath={pre.filePath}
          onCancel={() => {
            setPre({ show: false, fileName: '', filePath: '' });
          }}
        ></Preview>
      )}
    </div>
  );
};

export default ChatCards;
