.timeFilter {
  width: 410px;  

  .item {
    padding: 8px 16px;
    border-bottom: 1px solid #f2f3f5;
    &:nth-of-type(1){
      padding-top: 16px;
    }
    &:nth-of-type(4){
      padding-bottom: 0;
    }

    .line {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      column-gap: 8px;      
    }

    .describe {
      margin-top: 8px;
      font-size: 12px;
      color: #999;
    }
  }
  .footer {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 16px;
  }

  :global {
    .ant-select {
      width: 176px;
    }
  }
}

.selectPopup{
  border-radius: 2px;
  padding: 1px !important;
}
.cascaderPopup{
  border-radius: 2px;
  padding: 1px !important;
}
