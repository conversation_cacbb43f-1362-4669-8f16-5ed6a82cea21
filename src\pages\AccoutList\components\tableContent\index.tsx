import Pagination from '@/components/Pagination';
import useTableHeight from '@/hooks/useTableHeight';
import { Table } from 'antd';
import { FC, useEffect, useState } from 'react';

interface ChildProps {
  columns: any;
  data: any;
  total: number;
  onReceivePage: any;
  setTableQuery: any;
}
const TableContent: FC<ChildProps> = ({ columns, data, total, onReceivePage, setTableQuery }) => {
  const [curNumber, setCurNumber] = useState<number>(1);

  const onChange = (page: number) => {
    setCurNumber(page);
    onReceivePage(page);
  };
  useEffect(() => {
    setCurNumber(1);
  }, [columns]);
  const tableContainerRef = useTableHeight();
  return (
    <>
      <div ref={tableContainerRef}>
        <Table
          size="small"
          scroll={{ y: `calc(100vh - ${100}px)` }}
          columns={columns}
          dataSource={data}
          rowKey={() => Math.random()}
          pagination={false}
        />
        <Pagination
          total={total}
          current={curNumber}
          onChange={onChange}
          pageSize={20}
          showSizeChanger={false}
        />
      </div>
    </>
  );
};

export default TableContent;
