.todoList {

}
.todayNews {
  padding-left: 12px;
  .scrollableDivBox {
    overflow: auto;
  }

  .todayNewsItem {
    width: 100%;
    height: 98px;
    .avatar {
      width: 91px;
      height: 67px;
      border-radius: 8px;
    }

    .titleBox {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 100%;

      .titleText {
        width: 230px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 16px;
        font-weight: 500;
        line-height: 18px;
        letter-spacing: -1px;
        color: #292929;
      }
    }

    .descriptionBox {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 36px;

      .descriptionText {
        width: 230px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2; /* 设置为2，表示最多显示两行 */
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 12px;
        font-weight: normal;
        line-height: 18px;
        letter-spacing: 0;
        color: rgba(0, 0, 0, 0.65);
      }

      .descriptionTime {
        font-size: 14px;
        font-weight: normal;
        line-height: 16px;
        display: flex;
        align-items: center;
        letter-spacing: -1px;
        color: rgba(0, 0, 0, 0.25);
        margin-right: 5px;
      }
    }
  }
}
