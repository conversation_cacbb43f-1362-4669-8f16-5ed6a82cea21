import { useContext } from 'react';
import { Row, Col, Form, Input, Space, Button } from 'antd';
import Context from '../Context';
import styles from './index.module.less';

const Component = () => {
  const { useSelectFilesCtxStore } = useContext(Context);
  const [setFormFilterData, setQueryType] = useSelectFilesCtxStore!((state) => [
    state.setFormFilterData,
    state.setQueryType,
  ]);
  const [form] = Form.useForm();
  const handleFilter = () => {
    return form.validateFields().then((filterData) => {
      setFormFilterData({ ...filterData });
    });
  };
  const queryAll = () => {
    handleFilter().then(() => {
      setQueryType({ current: 'all' });
    });
  };
  const queryResults = () => {
    handleFilter().then(() => {
      setQueryType({ current: 'results' });
    });
  };

  return (
    <div className={styles.filter}>
      <Form form={form}>
        <Row gutter={8}>
          <Col span={7}>
            <Form.Item labelCol={{ span: 5 }} label="姓名" name="realName">
              <Input placeholder="请输入" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item labelCol={{ span: 8 }} label="文件名称" name="title">
              <Input placeholder="请输入" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Space>
              <Button type="primary" onClick={queryAll}>
                全部查询
              </Button>
              <Button type="primary" onClick={queryResults}>
                结果查询
              </Button>
            </Space>
          </Col>          
        </Row>
      </Form>
    </div>
  );
};

export default Component;
