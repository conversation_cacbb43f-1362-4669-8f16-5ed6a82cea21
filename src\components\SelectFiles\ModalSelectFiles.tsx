import { useEffect, useMemo, useState } from 'react';
import { Button } from 'antd';
import MultiModal from '@/components/MultiModal';
import Context from './Context';
import FilesList from './FilesList';
import Filter from './Filter';
import createUseCtxStore from './useCtxStore';
import styles from './index.module.less';

interface Props {
  open: boolean;
  sceneType?: '' | 'share';
  onCancel: () => void;
  onOk: (list: any[]) => void;
}

const Component = ({ open, sceneType = '', onCancel, onOk }: Props) => {
  const [useCtxStore] = useState(() => createUseCtxStore());
  const [list, selectedList, setSelectedList] = useCtxStore((state) => [
    state.list,
    state.selectedList,
    state.setSelectedList,
  ]);
  const okDisabled = useMemo(() => {
    return selectedList.length === 0;
  }, [selectedList]);
  const cancel = () => {
    onCancel();
  };
  const ok = () => {
    setSelectedList([]);
    onOk([
      ...selectedList.map((item: any) => {
        return Object.assign(item, { status: 'init' });
      }),
    ]);
  };
  useEffect(() => {
    if (open === false) {
      useCtxStore.reset();
    }
  }, [open]);

  const title = (
    <div className={styles.title}>
      <span>选择文件</span>
      <span>
        共计 <b>{list.length}</b> 个，已选择 <b>{selectedList.length}</b> 个
      </span>
    </div>
  );

  return (
    <Context.Provider value={{ useCtxStore, sceneType }}>
      <MultiModal
        className={styles.modalSelectFiles}
        layoutClassName="modalLeft"
        destroyOnClose={true}
        title={title}
        open={open}
        onCancel={cancel}
        footer={[
          <Button key="submit" type="primary" disabled={okDisabled} onClick={ok}>
            确认选择
          </Button>,
        ]}
      >
        <Filter />
        <FilesList />
      </MultiModal>
    </Context.Provider>
  );
};

export default Component;
