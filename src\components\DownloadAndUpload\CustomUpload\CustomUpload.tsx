import { FC, useState, useEffect } from 'react';
import styles from './CustomUpload.module.less';
import FlieDownload from '../FlieDownload/FlieDownload';
import FileChoose from '../FileChoose/FileChoose';
import FileUpload from '../FileUpload/FileUpload';
interface ModalProps {
  onClose?: () => void; // 关闭弹框回调方法
}
const CustomUpload = (ModalProps: ModalProps) => {
  const [isShowFileChoose, setIsShowFileChoose] = useState<boolean>(false);
  const [Data, setData] = useState<any>([]);
  useEffect(() => {}, []);
  return (
    <>
      <div className={styles.CustomDownload}>
        <FileUpload
          data={Data}
          onCloseModal={ModalProps.onClose}
          showFileChoose={() => {
            setIsShowFileChoose(true);
          }}
        />
        {isShowFileChoose ? (
          <FileChoose
            setData={setData}
            isOpen={isShowFileChoose}
            onClose={() => {
              setIsShowFileChoose(false);
            }}
          />
        ) : null}
      </div>
    </>
  );
};

export default CustomUpload;
