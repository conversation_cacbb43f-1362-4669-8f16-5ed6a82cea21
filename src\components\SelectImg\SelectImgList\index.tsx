/**
 * @description 选择图片列表组件
 * <AUTHOR>
 * @date 2024-08-30
 */
import { AlbumListItem } from '@/components/AlbumList/interface';
import { getImg } from '@/utils/common';
import { formatDate } from '@/utils/date';
import { Button } from 'antd';
import { FC } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './index.module.less';

const SelectImgList: FC<any> = (AlbumListProps) => {
  const { renderList, pager, prevPage, nextPage, hidePager, isVisible, showSelectImg } =
    AlbumListProps;
  const navigate = useNavigate();
  const showImg = (item: any) => {
    if (item.senderUserId || item.shareGroupId) {
      return getImg(item.coverPhotoHashUrl);
    } else {
      return getImg(item.coverPhotoUrl);
    }
  };
  return (
    <div className={styles.albumContainOut}>
      {isVisible && (
        <Button
          type="primary"
          ghost
          onClick={() => navigate('/share/category')}
          style={{ margin: '16px 24px 0px 24px', background: '#fff' }}
        >
          返回
        </Button>
      )}
      <div className={`${styles.albumContain} ${hidePager ? styles.netAlbumContain : ''}`}>
        {renderList.map((item: AlbumListItem) => (
          <div
            key={
              item.shareId
                ? `${item.albumsId}-${item.shareId}`
                : item.albumsId || item.albumsTemplateId
            }
            className={styles.albumItem}
            onClick={() => {
              showSelectImg(
                item.albumsId,
                item.albumsTemplateId,
                item.senderUserId,
                item.albumsCategorySingle,
              );
            }}
          >
            <div className={styles.albumName}>
              {item.albumsName || item.albumsTemplateName || ''}
            </div>
            {item.createTime && (
              <div className={styles.timeRow}>{formatDate(item.createTime, 'YYYY-MM-DD')}</div>
            )}
            <div className={styles.imageWrap}>
              <img
                className={styles.image}
                src={(item.albumsId && showImg(item)) || item.albumsTemplateCoverUrl || ''}
              />
            </div>
            <div className={styles.imageRight} />

            {item.senderUserId ? <div className={styles.shareTag}>分享</div> : null}
            {AlbumListProps.renderItemBtn?.(item)}
          </div>
        ))}
      </div>
      {AlbumListProps.RightBtn}
      {!hidePager && pager.total > 0 && (
        <div className={styles.pager}>
          <Button disabled={pager.pageNo === 1} onClick={prevPage}>
            上一页
          </Button>
          <div>
            <span className={styles.blueFont}>{pager.total}</span> 个相册 共{' '}
            <span className={styles.blueFont}>{Math.ceil(pager.total / pager.pageSize)}</span> 页
            当前第 <span className={styles.blueFont}>{pager.pageNo}</span> 页
          </div>
          <Button
            disabled={pager.pageNo >= Math.ceil(pager.total / pager.pageSize)}
            onClick={nextPage}
          >
            下一页
          </Button>
        </div>
      )}
    </div>
  );
};
export default SelectImgList;
