.CustomModal {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background-color: transparent;
  z-index: 1000;
  .ModalContent {
    position: absolute;
    width: auto;
    height: auto;
    min-width: 300px;
    display: inline-block;
    z-index: 1001;
    left: 50%;
    top: 20%;
    transform: translateX(-50%);
    & > div {
      display: flex;
      align-items: center;
      & > div {
        flex: none;
        background-color: #fff;
        &:first-of-type {
          margin-right: 8px;
        }
        &:last-of-type {
          margin-right: 0;
        }
      }
    }
  }
  .mask {
    position: absolute;
    width: 100%;
    inset: 0;
    z-index: 1000;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    pointer-events: none;
  }
}
