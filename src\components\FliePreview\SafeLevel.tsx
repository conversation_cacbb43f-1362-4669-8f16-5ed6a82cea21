import { Button, Popover } from 'antd';
import { useEffect, useState } from 'react';
import styles from './FilePreview.module.less';

interface Props {
  safeLevelName: string;
  verifyValue?: boolean;
}

const Component = ({ safeLevelName, verifyValue = false }: Props) => {
  const [popoverOpen, setPopoverOpen] = useState(false);
  return verifyValue ? (
    <Popover
      rootClassName={styles.popover}
      content={
        <div>
          <p>
            此文件阅读后或者倒计时结束后会<b>立即撤回</b>
          </p>
          <p>
            <Button type="link" onClick={() => setPopoverOpen(false)}>
              继续浏览
            </Button>
            <Button type="primary" onClick={() => setPopoverOpen(false)}>
              我已阅读
            </Button>
          </p>
        </div>
      }
      title="是否已经浏览此文件？"
      trigger="hover"
      open={popoverOpen}
      onOpenChange={(value, b) => {
        if (value) {
          setPopoverOpen(value);
        }
      }}
    >
      <span className={styles.safeLevel}>{safeLevelName}</span>
    </Popover>
  ) : (
    <span className={styles.safeLevel}>{safeLevelName}</span>
  );
};

export default Component;
