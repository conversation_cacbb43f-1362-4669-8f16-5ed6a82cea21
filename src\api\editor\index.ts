import { Doc } from 'yjs';
import http from '../index';
import {
  AgreeApplyParams,
  CommonResponse,
  CreateDocVersionParam,
  CreateDocVersionResponse,
  DelParam,
  GetDocParam,
  GetUserStatusRes,
  QeuryFileListParam,
  QuitEditParams,
  RejectApplyParams,
  shareHistoryParam,
  shareParam,
  StartShareParam,
  UpdateDocVersionParam,
  VersionParam,
  WordConvertRes,
} from './type';

/**保存版本 */
export function createDocVersion(data: CreateDocVersionParam) {
  return http.post<CommonResponse<CreateDocVersionResponse>>({
    url: '/web-api/editor/version/create',
    data,
  });
}

/**更新版本,不增加版本号
 * 用于前端自动保存的提交
 */
export function updateDocVersion(data: UpdateDocVersionParam) {
  return http.put<CommonResponse>({ url: '/web-api/editor/version/update', data });
}

/** --------------强制编辑-------------- */
/**
 * 创建强制编辑房间
 */
export function createForceEditRoom(data: StartShareParam) {
  return http.post<CommonResponse>({ url: '/web-api/editor/force/create', data });
}

/**
 * 解散强制编辑房间
 * */
export function destroyForceEditRoom(docId: string) {
  return http.put<CommonResponse>({ url: `/web-api/editor/force/cancel/${docId}` });
}

/**
 * 获取分享房间成员
 */
export function getForceMembers(docId: string) {
  return http.get<CommonResponse<GetUserStatusRes>>({
    url: `/web-api/editor/force/userStatus/${docId}`,
    params: { time: Date.now() },
  });
}
/** --------------强制编辑 end-------------- */

/** --------------分享编辑-------------- */
/**
 * 创建分享编辑房间
 */
export function createDistributeEditRoom(data: StartShareParam) {
  return http.post<CommonResponse>({ url: '/web-api/editor/share/create', data });
}

/**
 * 解散共享编辑房间
 */
export function destroyDistributeEditRoom(docId: string) {
  return http.put<CommonResponse>({ url: `/web-api/editor/share/cancel/${docId}` });
}

/**
 * 被邀请人接受分享编辑
 */
export function memberAcceptDistributeEditRoomInvite(docId: string) {
  return http.put<CommonResponse>({ url: `/web-api/editor/share/member/accept/${docId}` });
}

/**
 * 被邀请人拒接共享编辑
 */
export function memberRejectDistributeEditRoomInvite(docId: string) {
  return http.put<CommonResponse>({ url: `/web-api/editor/share/member/refuse/${docId}` });
}

/**
 * 获取分享房间成员
 */
export function getDistributeMembers(docId: string) {
  return http.get<CommonResponse<GetUserStatusRes>>({
    url: `/web-api/editor/share/userStatus/${docId}`,
    params: { time: Date.now() },
  });
}
/** --------------分享编辑 end-------------- */

/** --------------共享编辑-------------- */
/**
 * 创建共享编辑房间
 */
export function createShareEditRoom(data: StartShareParam) {
  return http.post<CommonResponse>({ url: '/web-api/editor/team/create', data });
}

/**
 * 解散共享编辑房间
 */
export function destroyShareEditRoom(docId: string) {
  return http.put<CommonResponse>({ url: `/web-api/editor/team/cancel/${docId}` });
}

/**
 * 申请编辑权限
 * @param docId 共享文档ID
 * @returns
 */
export function applyShareEdit(docId: string) {
  return http.post<CommonResponse>({ url: `/web-api/editor/team/apply/${docId}` });
}

/**
 * 放弃编辑权限
 * @param data 共享文档
 * @returns
 */
export function quitShareEdit(data: QuitEditParams) {
  return http.post<CommonResponse>({ url: `/web-api/editor/team/quit`, data });
}

/**
 * 被邀请人接受共享编辑
 */
export function memberAcceptShareEditRoomInvite(docId: string) {
  return http.put<CommonResponse>({ url: `/web-api/editor/team/member/accept/${docId}` });
}

/**
 * 被邀请人拒接共享编辑
 */
export function memberRejectShareEditRoomInvite(docId: string) {
  return http.put<CommonResponse>({ url: `/web-api/editor/team/member/refuse/${docId}` });
}

/**
 * 获取共享房间成员
 */
export function getMembers(docId: string) {
  return http.get<CommonResponse<GetUserStatusRes>>({
    url: `/web-api/editor/team/userStatus/${docId}`,
    params: { time: Date.now() },
  });
}

/**同意交出编辑权 */
export function currentEditOwnerAgree(data: AgreeApplyParams) {
  return http.post<CommonResponse>({ url: `/web-api/editor/team/apply/agree`, data });
}

/**拒绝交出编辑权 */
export function currentEditOwnerDiscard(data: RejectApplyParams) {
  return http.post<CommonResponse>({ url: `/web-api/editor/team/apply/reject`, data });
}
/** --------------共享编辑 end-------------- */

/**文件列表 */
export function qeuryFileList(data: QeuryFileListParam) {
  return http.post<CommonResponse>({ url: '/web-api/editor/read/record/page', data });
}

/**文件特定版本内容 */
export function getFileContentAndDetail(data: GetDocParam) {
  return http.post<CommonResponse<Doc>>({ url: '/web-api/editor/doc/get', data });
}

/**根据id获取所有历史版本 */
export function getVersionListById(data: VersionParam) {
  return http.get<CommonResponse>({ url: '/web-api/editor/version/page', params: data });
}
/**文编辑记录列表 */
export function qeuryFileHistoryList(data: QeuryFileListParam) {
  return http.post<CommonResponse>({ url: '/web-api/editor/edit/record/page', data });
}
// 获得文档共享编辑记录分页
export function queryShareRecordList(data: shareHistoryParam) {
  return http.post<CommonResponse>({ url: '/web-api/editor/team/edit/record/page', data });
}
//  数据银行和回收站删除编辑器数据
export function delEidtorData(data: DelParam) {
  return http.post<CommonResponse>({ url: '/web-api/editor/center/operate', data });
}
/**数据银行-裕邦编辑文档列表 */
export function getDatabankList(data: QeuryFileListParam) {
  return http.post<CommonResponse>({ url: '/web-api/editor/center/doc/list', data });
}
/**回收站-裕邦编辑文档列表 */
export function getRecycleList(data: QeuryFileListParam) {
  return http.post<CommonResponse>({ url: '/web-api/editor/center/recycle/doc/list', data });
}
/*共享天地-裕邦编辑文档列表 */
export function getShareList(data: shareParam) {
  return http.post<CommonResponse>({ url: '/web-api/editor/world/page', data });
}
/*发起审批-保存信息 */
export function saveApproveInfo(data: approveParam) {
  return http.post<Response>({ url: '/web-api/approval/article/save', data });
}

/*转换并保存到文库大全*/
export function transformToWordAndSave(data: { docId: string }) {
  return http.post<CommonResponse<WordConvertRes>>({
    url: '/web-api/editor/doc/word/convert',
    data,
  });
}

export async function transformToWord(data: { docId: string }) {
  try {
    const res = await fetch('/api/web-api/editor/doc/word/download', {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
        responseType: 'arraybuffer',
      },
    });
    const blob = await res.blob();
    const url = URL.createObjectURL(new Blob([blob], { type: res.headers.get('content-type')! }));
    const a = document.createElement('a');
    a.target = '_banck';
    a.href = url;
    a.download = `下载名称`;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);

    return Promise.resolve();
  } catch (e) {
    return Promise.reject(e);
  }
}

/**
 * 获取红头文件号
 */
export function getDocNum(data: { docId: string }) {
  return http.post<Response>({ url: `/web-api/editor/doc/getDocNum`, data });
}
