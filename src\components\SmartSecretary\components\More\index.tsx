/** 图片 */
import App21 from '@/assets/images/home/<USER>/approval.png';
import App17 from '@/assets/images/home/<USER>/auth.png';
import App7 from '@/assets/images/home/<USER>/bwzf.png';
import App26 from '@/assets/images/home/<USER>/chat.png';
import App13 from '@/assets/images/home/<USER>/gxtd.png';
import App16 from '@/assets/images/home/<USER>/market.png';
import App3 from '@/assets/images/home/<USER>/msxc.png';
import App24 from '@/assets/images/home/<USER>/sphy.png';
import App25 from '@/assets/images/home/<USER>/todo.png';
import App8 from '@/assets/images/home/<USER>/txl.png';
import App6 from '@/assets/images/home/<USER>/wdrj.png';
import App1 from '@/assets/images/home/<USER>/wkdq.png';
import App14 from '@/assets/images/home/<USER>/ybbj.png';
import App2 from '@/assets/images/home/<USER>/ybyx.png';
import App11 from '@/assets/images/home/<USER>/zmwj.png';

import App31 from '@/assets/images/home/<USER>/peixunguanli.png';
import App29 from '@/assets/images/home/<USER>/shipinzhuanwenzi.png';
import App27 from '@/assets/images/home/<USER>/tonghua.png';
import App32 from '@/assets/images/home/<USER>/tongzhiwenjian.png';
import App38 from '@/assets/images/home/<USER>/webbianji.png';
import App36 from '@/assets/images/home/<USER>/webjietu.png';
import App37 from '@/assets/images/home/<USER>/webtupian.png';
import App35 from '@/assets/images/home/<USER>/webwangye.png';
import App34 from '@/assets/images/home/<USER>/webwangzhan.png';
import App28 from '@/assets/images/home/<USER>/wenzijiaodui.png';
import App30 from '@/assets/images/home/<USER>/xinwen.png';
import App33 from '@/assets/images/home/<USER>/zhanghuguanli.png';

import Permission from '@/const/permission';
import useIntelligentSecretaryStore from '@/store/useIntelligentSecretaryStore';
import { Button, Image } from 'antd';
import classNames from 'classnames';
import { FC, useContext, useState } from 'react';
import { Context } from '../../context';
import styles from './index.module.less';
interface MoreListProps {
  close: () => void;
}
const MoreList: FC<MoreListProps> = ({ close }) => {
  const [shareGroupId] = useIntelligentSecretaryStore((state) => [state.shareGroupId]);
  const AppList = [
    {
      img: App25,
      txt: '待办事项',
      key: 'todo',
      status: 'fixed',
    },
    {
      img: App1,
      txt: '文库大全',
      key: 'library',
      permisison: Permission.LIBRARY,
      status: 'fixed',
    },
    {
      img: App26,
      txt: '裕邦聊天',
      key: 'im',
      status: 'fixed',
    },
    {
      img: App2,
      txt: '裕邦邮箱',
      key: 'mail',
      permisison: Permission.YB_MAIL,
    },
    {
      img: App8,
      txt: '通讯录',
      key: 'addressBook',
      permisison: Permission.ADDRESS_BOOK,
    },
    {
      img: App24,
      txt: '音视频会议',
      key: 'videoMeeting',
      permisison: Permission.VIDEO_MEET,
    },
    {
      img: App27,
      txt: '音视频通话',
      key: 'audioCall',
    },
    {
      img: App28,
      txt: '音频转文字',
      key: 'audio',
    },
    {
      img: App29,
      txt: '视频转文字',
      key: 'video',
    },
    {
      img: App3,
      txt: '魔术相册',
      key: 'magicAlbum',
      permisison: Permission.MAGIC_ALBUM,
    },
    {
      img: App14,
      txt: '裕邦编辑',
      key: 'editor',
      permisison: Permission.YB_EDIT,
    },
    {
      img: App30,
      txt: '今日新闻',
      permisison: Permission.ENTERPRISE_NEWS,
      key: 'todayNews',
    },
    {
      img: App31,
      txt: '培训管理',
    },
    {
      img: App32,
      txt: '通知文件',
      key: 'notificationFile',
    },
    {
      img: App17,
      txt: '权限管理',
      permisison: Permission.PERMISSION_MANAGE,
    },
    {
      img: App33,
      txt: '账户管理',
    },
    {
      img: App34,
      txt: '网站收藏',
      key: 'favoriteWebsit',
    },
    {
      img: App35,
      txt: '网页收藏',
      key: 'favoriteWebPage',
    },
    {
      img: App36,
      txt: '一键截图',
      key: 'screenShot',
    },
    {
      img: App37,
      txt: '图片收藏',
      key: 'picturesFavorite',
    },
    {
      img: App38,
      txt: '网页编辑',
      key: 'webEditDocument',
    },
    {
      img: App6,
      txt: '我的日记',
      key: 'diary',
      permisison: Permission.MY_DIARY,
    },
    {
      img: App7,
      txt: '备忘祝福',
      key: 'blessing',
      permisison: Permission.MEMO_BLESSING,
    },
    {
      img: App13,
      txt: '共享天地',
      key: 'share',
      permisison: Permission.SHARE_WORLD,
    },
    {
      img: App11,
      txt: '桌面文件',
      key: 'file',
      permisison: Permission.DESKTOP_FILE,
    },
    {
      img: App16,
      txt: '市场管理',
      key: 'market',
    },
    {
      img: App21,
      txt: '审批管理',
      key: 'approval',
      permisison: Permission.APPROVAL_MANAGE,
    },
  ];
  const groupAppList = [
    {
      img: App1,
      txt: '文库大全',
      key: 'library',
      url: '/library',
      permisison: Permission.LIBRARY,
      status: 'fixed',
    },
    {
      img: App26,
      txt: '裕邦聊天',
      key: 'im',
      status: 'fixed',
    },
    {
      img: App2,
      txt: '裕邦邮箱',
      key: 'mail',
      url: '/mailHomePage',
      status: 'fixed',
      permisison: Permission.YB_MAIL,
    },
    {
      img: App8,
      txt: '通讯录',
      key: 'addressBook',
      url: '/addressBook',
      permisison: Permission.ADDRESS_BOOK,
    },
    {
      img: App28,
      txt: '音频转文字',
      key: 'audio',
    },
    {
      img: App29,
      txt: '视频转文字',
      key: 'video',
    },
    {
      img: App3,
      txt: '魔术相册',
      key: 'magicAlbum',
      url: '/magicAlbum',
      permisison: Permission.MAGIC_ALBUM,
    },
    {
      img: App14,
      txt: '裕邦编辑',
      key: 'editor',
      url: '/editor',
      permisison: Permission.YB_EDIT,
    },
    {
      img: App6,
      txt: '我的日记',
      key: 'diary',
      url: '/diary',
      permisison: Permission.MY_DIARY,
    },
    {
      img: App34,
      txt: '网站收藏',
      key: 'favoriteWebsit',
    },
    {
      img: App35,
      txt: '网页收藏',
      key: 'favoriteWebPage',
    },
    {
      img: App36,
      txt: '一键截图',
      key: 'screenShot',
    },
    {
      img: App37,
      txt: '图片收藏',
      key: 'picturesFavorite',
    },
    {
      img: App38,
      txt: '网页编辑',
      key: 'webEditDocument',
    },
  ];
  const [moreAppList] = useState(shareGroupId ? groupAppList : AppList);
  const { changeActiveTab } = useContext(Context);

  const handleClick = (item: any) => {
    console.log('item', item);
    changeActiveTab(item.key, item);
  };
  return (
    <div className={classNames(styles.moreList, shareGroupId ? styles.groupMoreClass : undefined)}>
      <div className={styles.menuList}>
        {moreAppList.map((item, index) => {
          return item ? (
            <div className={styles.menuItem} key={index} onClick={() => handleClick(item)}>
              <Image src={item.img} preview={false} className={styles.icon}></Image>
              <span className={styles.txt}>{item.txt}</span>
            </div>
          ) : (
            <div className={styles.menuItem} key={index} style={{ cursor: 'default' }}>
              <div className={styles.icon}></div>
              <span className={styles.txt} style={{ opacity: 0 }}>
                defule
              </span>
            </div>
          );
        })}
      </div>
      <Button type="primary" ghost size="large" className={styles.closeBtn} onClick={close}>
        关闭
      </Button>
    </div>
  );
};

export default MoreList;
