.paper {
  background: #dddddd;
  padding: 40px 20px 35px 20px;
  .cropper {
    width: 100%;
    object-fit: cover;
    text-align: center;
    :global {
      .ReactCrop__child-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .ReactCrop__drag-handle{
        display: none;
      }
    }
    img {
      max-width: 100%;
      height: 600px;
      // display: inline-block;
    }
  }
  .cropFooter {
    display: flex;
    justify-content: space-between;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.45);
    :global {
      button {
        font-size: 18px !important;
      }
    }
  }
}
