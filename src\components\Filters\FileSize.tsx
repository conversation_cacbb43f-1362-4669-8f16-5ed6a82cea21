import { useEffect, useMemo } from 'react';
import FileSizeFilter from './FileSizeFilter';

const Component = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  close,
  clearFilters,
  resetList,
  config,
}: any) => {
  const value = useMemo(() => {
    console.log('selectedKeys', selectedKeys);
    return selectedKeys[0] ?? {};
  }, [selectedKeys]);
  const change = (value: any) => {
    if (Object.keys(value).find((key) => value[key])) {
      setSelectedKeys([
        {
          ...value,
        },
      ]);
    } else {
      setSelectedKeys([]);
    }
    submit('ok'); // 为了change直接触发接口请求
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
      submit('ok');
    } else if (type === 'close') {
      close();
      config?.setFileSizeOpen(false);
    } else if (type === 'ok') {
      confirm({ closeDropdown: false });
      // close();
    }
  };
  // 切换table 重置搜索
  useEffect(() => {
    if (resetList) {
      clearFilters();
    }
  }, [resetList]);

  return <FileSizeFilter value={value} onChange={change} onSubmit={submit} />;
};

export default Component;
