/**
 * 用于动态计算，并设置表格容器的高度
 * 用法：
 * import withDynamicHeight from '@/components/WithDynamicHeight';
 * const DynamicTable = withDynamicHeight(你的组件);
 * <DynamicTable />
 */

import { getHeight } from '@/utils/common';
import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
interface WithDynamicHeightProps {
  children?: React.ReactNode;
}

const withDynamicHeight = <P extends WithDynamicHeightProps>(
  WrappedComponent: React.ComponentType<P>,
  calHeight?: number,
) => {
  const EnhancedComponent = forwardRef((props: any, ref) => {
    const tableContainerRef = useRef<HTMLDivElement | null>(null);

    const WrappedComponentRef = useRef<any>(null);

    useEffect(() => {
      const calculateTableHeight = () => {
        if (tableContainerRef.current) {
          const containerTop = tableContainerRef.current.offsetTop;
          const innerHeight = window.innerHeight;

          const height = innerHeight - containerTop - getHeight(120) - (calHeight || 0);

          const body = tableContainerRef.current.querySelector('.ant-table-body') as HTMLElement;

          if (body) {
            body.style.height = `${height}px`;
          }
        }
      };

      calculateTableHeight();

      // 监听窗口大小变化
      window.addEventListener('resize', calculateTableHeight);

      return () => {
        window.removeEventListener('resize', calculateTableHeight);
      };
    }, [WrappedComponent]);

    // 使用 useImperativeHandle 处理 forwardRef，透传 WrappedComponent 的 ref
    useImperativeHandle(ref, () => ({
      ...WrappedComponentRef?.current, // 透传 WrappedComponent 的 ref
    }));
    return (
      <div ref={tableContainerRef} className="table-container">
        <WrappedComponent ref={WrappedComponentRef} {...props} />
      </div>
    );
  });
  return EnhancedComponent;
};

export default withDynamicHeight;
