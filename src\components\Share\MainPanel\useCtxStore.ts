import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create } from '@/store';
import type { Config } from '../Context';

export interface State {
  mainPanelOpen: boolean;
  userList: any[];
  userMap: Record<string, any>;
  sharedFolderList: any[];
  sharedFolderMap: Record<string, any>;
  selectedFileList: any[];
  selectedFileMap: Record<string, any>;
  safeLevel: string;
  finalTime: string;
  enableFinalTime: boolean;
}
export interface SetState {
  setMainPanelOpen: Setter;
  setUserList: Setter;
  setUserMap: Setter;
  setSharedFolderList: Setter;
  setSharedFolderMap: Setter;
  setSelectedFileList: Setter;
  setSelectedFileMap: Setter;
  setSafeLevel: Setter;
  setFinalTime: Setter;
  setEnableFinalTime: Setter;
}

const getMapById = (list: any[]) => {
  const map: Record<string, any> = {};
  list.forEach((item: any) => {
    map[item.id] = item;
  });
  return map;
};

const createUseCtxStore = function (config: Config) {
  const useCtxStore = autoUseShallow<State, SetState>(
    create(
      autoCreateSetters<State>({
        mainPanelOpen: false,
        userList: [],
        userMap: {},
        sharedFolderList: [],
        sharedFolderMap: {},
        selectedFileList: config.module === 'preview' ? config.fileList! : [],
        selectedFileMap: config.module === 'preview' ? getMapById(config.fileList!) : {},
        safeLevel: '1',
        finalTime: '',
        enableFinalTime: false,
      }),
    ),
  );

  return useCtxStore;
};

export default createUseCtxStore;
