import { cwsRequest } from '@/store/useCppWebSocketStore';
import useLibraryStore from '@/store/useLibraryStore';
import { useEffect, useRef, useState } from 'react';
import MenuFilter from './MenuFilter';

const Component = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  close,
  clearFilters,
  config,
}: any) => {
  const self = useRef<HTMLDivElement>(null);
  const [localSourceList] = useLibraryStore((state) => [state.localSourceList]);
  const [items, setItems] = useState([]);
  const toggleItem = (key: string) => {
    if (selectedKeys.includes(key)) {
      setSelectedKeys(selectedKeys.filter((k: string) => k !== key));
    } else {
      setSelectedKeys([...selectedKeys, key]);
    }
    submit('ok');
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
      submit('ok');
    } else if (type === 'ok') {
      confirm({ closeDropdown: false });
    } else if (type === 'close') {
      close();
      config?.setFileSourceOpen(false);
    }
  };
  //文件来源调用c++接口
  useEffect(() => {
    let observer: IntersectionObserver;
    if (self.current && config?.module === 'desktopFile') {
      // 创建并初始化IntersectionObserver对象
      observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // 元素与视口交叉
            getData();
          } else {
            // 元素离开视口
          }
        });
      });
      observer.observe(self.current);
    }
    return () => {
      if (self.current && observer) {
        observer.observe(self.current);
      }
    };
  }, [self.current]);
  const getData = () => {
    cwsRequest({
      module: 'desktopFile',
      method: 'getFileSourceDirectories',
      data: null,
    }).then((res: any) => {
      if (res.code === 0) {
        const data = JSON.parse(res.data) || [];
        console.log('data: ', data);
        setItems(
          data.map((item: any) => {
            return {
              key: item.value,
              label: item.label,
            };
          }),
        );
      }
    });
  };
  return (
    <div ref={self}>
      <MenuFilter
        items={config?.module === 'desktopFile' ? items : localSourceList}
        selectedKeys={selectedKeys}
        onSelect={toggleItem}
        onSubmit={submit}
      />
    </div>
  );
};

export default Component;
