import { useState, useEffect } from 'react';
import { Spin } from 'antd';
import { preview } from '@/api/library';
import CryptoJS from 'crypto-js';
import type { File } from '../interface';
import ErrorPreview from '../ErrorPreview';
import styles from './index.module.less';

interface Props {
  file: File;
}

const Component = ({ file }: Props) => {
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
  const visitPathBase64 = CryptoJS.enc.Base64.stringify(
    CryptoJS.enc.Utf8.parse(file.visitPath || ''),
  );
  useEffect(() => {
    const iframe: HTMLIFrameElement | null = document.getElementById(
      'iframePreview',
    ) as HTMLIFrameElement;
    const run = () => {
      preview({
        fileName:
          file.title?.split('.').pop() === file.fileType
            ? file.title
            : `${file.title}.${file.fileType}`,
        fileUrl: visitPathBase64,
        fileSize: 1 * 1024 * 1024, // file.fileSize
        aesKey: file.secretKey,
      }).then((res: any) => {
        if (res.includes('正在生成预览，请稍候')) {
          console.log(res);
          setTimeout(() => {
            run();
          }, 3000);
        } else if (res.includes('系统暂不支持在线预览')) {
          console.log(res);
          setError(`该(${file.fileType})文件，系统暂不支持在线预览。`);
        } else if (res.includes('系统暂不支持在线预览') || res.includes('没有预览权限')) {
          console.log(res);
          setError(`没有预览权限。`);
        } else {
          setLoading(false);
          if (iframe) {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
            iframeDoc?.open();
            iframeDoc?.write(res);
            iframeDoc?.close();
          }
        }
      });
    };
    run();
  }, []);
  return (
    <>
      {error === '' && (
        <Spin wrapperClassName={styles.spin} spinning={loading}>
          <iframe id="iframePreview" className={styles.iframePreview} />
        </Spin>
      )}
      {error !== '' && <ErrorPreview errorText={error} />}
    </>
  );
};

export default Component;
