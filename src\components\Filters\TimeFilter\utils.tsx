import dayjs from 'dayjs';

const mergeChildren = (options: any, year?: number, month?: number) => {
  if (!year) {
    return options.map((item: any) => {
      return {
        ...item,
        children: mergeChildren(months, item.value),
      };
    });
  }
  if (!month) {
    return options.map((item: any) => {
      return {
        ...item,
        children: getDays(year, item.value),
      };
    });
  }
};

export const getDays = (year: number, month: number) => {
  const days = dayjs(new Date(year, month - 1)).daysInMonth();
  const options = [];
  for (let i = 1; i <= days; i++) {
    const value = i.toString().padStart(2, '0');
    options.push({ label: `${value}日`, value });
  }
  return options;
};

export const years = (() => {
  const options = [];
  for (let i = 1960; i < 2034; i++) {
    const value = i.toString();
    options.push({ label: `${value}年`, value });
  }
  return options;
})();

//当前年份及以后
export const yearss = (() => {
  const options = [];
  const now = new Date();
  const currentYear = now.getFullYear();
  for (let i = currentYear; i < currentYear + 10; i++) {
    const value = i.toString();
    options.push({ label: `${value}年`, value });
  }
  return options;
})();

export const months = (() => {
  const options = [];
  for (let i = 1; i < 13; i++) {
    const value = i.toString().padStart(2, '0');
    options.push({ label: `${value}月`, value });
  }  
  return options;
})();

export const hours = (() => {
  const options = [];
  for (let i = 1; i < 25; i++) {
    const value = i.toString().padStart(2, '0');
    options.push({ label: `${value}时`, value });
  }
  return options;
})();

export const minutes = (() => {
  const options = [];
  for (let i = 0; i < 60; i++) {
    const value = i.toString().padStart(2, '0');
    options.push({ label: `${value}分`, value });
  }
  return options;
})();

export const seconds = (() => {
  const options = [];
  for (let i = 0; i < 60; i++) {
    const value = i.toString().padStart(2, '0');
    options.push({ label: `${value}秒`, value });
  }
  return options;
})();

export const source = mergeChildren(years);

export const formatTimeFilterValue = (t: any, format: string = 'YYYY-MM-DD HH:mm:ss') => {
  const result = { startTime: '', endTime: '' };
  if (t.rangeStartYear && t.rangeEndYear) {
    result.startTime = dayjs(
      new Date(
        t.rangeStartYear,
        t.rangeStartMonth ? t.rangeStartMonth - 1 : 0,
        t.rangeStartDay ?? 1,
      ),
    ).format(format);
    if (
      t.rangeStartYear == t.rangeEndYear &&
      !t.rangeStartMonth &&
      !t.rangeEndMonth &&
      !t.rangeStartDay &&
      !t.rangeEndDay
    ) {
      result.endTime = dayjs(new Date(t.rangeStartYear, 11, 31).setHours(23, 59, 59, 999)).format(
        format,
      );
      console.log(result.endTime);
    } else if (
      t.rangeStartYear == t.rangeEndYear &&
      t.rangeStartMonth == t.rangeEndMonth &&
      t.rangeStartDay == t.rangeEndDay
    ) {
      result.endTime = dayjs(
        new Date(
          t.rangeEndYear,
          t.rangeEndMonth ? t.rangeEndMonth - 1 : 0,
          t.rangeEndDay ?? 1,
        ).setHours(23, 59, 59, 999),
      ).format(format);
    } else {
      result.endTime = dayjs(
        new Date(t.rangeEndYear, t.rangeEndMonth ? t.rangeEndMonth - 1 : 11, t.rangeEndDay ?? 31),
      ).format(format);
    }
  } else if (t.startTime) {
    result.startTime = dayjs(new Date(t.startTime[0], t.startTime[1] - 1, t.startTime[2])).format(
      format,
    );
  } else if (t.endTime) {
    result.endTime = dayjs(new Date(t.endTime[0], t.endTime[1] - 1, t.endTime[2])).format(format);
  } else if (t.year) {
    result.startTime = dayjs(
      new Date(t.year, t.month ? t.month - 1 : 0, t.day ?? 1, t.hour ?? 0),
    ).format(format);
    result.endTime = dayjs(
      new Date(
        t.year,
        t.month ? t.month - 1 : 11,
        t.day ?? dayjs(new Date(t.year, t.month ? t.month - 1 : 11)).daysInMonth(),
        t.hour ? t.hour + 1 : 24,
      ),
    ).format(format);
  }
  return result;
};
