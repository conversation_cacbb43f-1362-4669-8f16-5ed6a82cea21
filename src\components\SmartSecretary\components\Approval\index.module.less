.ApprovalContainer {
  width: 100%;
  .headRow {
    padding: 16px 16px 0 16px;
    // border-block-end: var(--ant-line-width) var(--ant-line-type) var(--ant-color-split);
    .headBtn {
      // flex: 1;
      width: 120px;
      height: 32px;
      line-height: 32px;
      background: rgba(0, 0, 0, 0.05);
      color: rgba(0, 0, 0, 0.65);
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 15px;
      &.active {
        background: rgba(61, 90, 254, 0.15);
        color: #3d5afe;
      }
      &:last-child {
        margin-right: 0;
      }
      &:hover {
        background: rgba(61, 90, 254, 0.15);
        color: #3d5afe;
      }
    }
  }
  .filterRow {
    padding: 16px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    .filterItem {
      flex: 1;
      display: flex;
      justify-content: center;
    }
    :global {
      .ant-dropdown-open .ant-space-item {
        color: #3d5afe;
      }
    }
  }
}

.scrollableDiv {
  height: calc(100% - 114px);
  overflow: auto;
  // padding: 0 16px;
}
.ApprovalContainer {
  width: 100%;
  overflow-x: hidden;
  :global {
    .ant-drawer-body,
    .ant-drawer-header {
      padding: 0;
    }
  }
}
.todoList {
  background: #fff;
  height: calc(100%-88px);
  :global {
    .ant-list-item-meta-title {
      margin-bottom: 0;
    }
  }
  .listContentItem {
    padding: 16px;
  }
  .leftContent {
    .webIcon {
      width: 50px;
      height: 50px;
    }
  }
  .titleLine {
    display: flex;
    align-items: baseline;
    .title {
      font-size: 17px;
      font-weight: 500;
      color: #292929;
      overflow: hidden; /* 超出部分隐藏 */
      display: -webkit-box; /* 使用 WebKit 的弹性盒模型 */
      -webkit-line-clamp: 2; /* 限制在一个块元素显示的文本的行数 */
      -webkit-box-orient: vertical;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
  .contentLine {
    display: flex;
    align-items: end;
  }

  .content {
    flex: 1;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
  }
  .unread {
    color: #fff;
    background: #e53935;
    border-radius: 50%;
    font-size: 13px;
    padding: 0 2px;
  }
  .cirleUnread {
    color: #fff;
    background: #e53935;
    border-radius: 50%;
    font-size: 13px;
    width: 20px;
    line-height: 20px;
    text-align: center;
  }
  .time {
    margin: -1px 0 0 10px;
    color: rgba(0, 0, 0, 0.25);
    .btnDefault {
      color: #3d5afe;
      border: 1px #3d5afe solid;
    }
  }
  .typeTitle {
    padding: 0px 4px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 12px;
  }
}
