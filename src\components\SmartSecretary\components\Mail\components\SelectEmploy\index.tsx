/**
 * 选择员工公共组件
 */

import Drawer from '@/components/Drawer';
import Header from '@/components/SmartSecretary/publicComponents/Header';
import Search from '@/components/SmartSecretary/publicComponents/Search';
import { Button } from 'antd';
import { FC, useState } from 'react';
import Footer from './Footer';
import styles from './index.module.less';
import List from './List';
interface Props {
  title?: string; // 头部标题
  open: boolean; // 开关标识
  onClose: () => void; // 关闭窗口
  onSubmit: (list: any[]) => void; // 提交表单
  mode: 'single' | 'multiple'; // 单选/多选
  type: 1 | 2 | 3; // 通讯录类型（1：联系人，2：群，3:人+群）
  dataSource: any[]; // 数据源（可能只是通讯录中的一部分人）
  disabledList?: any[]; // 禁用的联系人列表[{ username: '5802666698-43',... }]
  selectData: any[];
  sendMailType: number; //0 所有 1内部 2外部
}
const Index: FC<Props> = ({
  title = '通讯录',
  open,
  onClose,
  onSubmit,
  mode,
  type,
  dataSource,
  disabledList,
  selectData,
  sendMailType,
}) => {
  const [selectList, setSelectList] = useState<any[]>(selectData);

  const [renderList, setRenderList] = useState(
    dataSource.filter((item: any) => item.type !== type),
  ); // 用于控制渲染的列表
  const [keyWords, setKeyWords] = useState('');

  // 提交选中
  const handlderSubmit = () => {
    onSubmit(selectList);
  };

  // 渲染头部功能按钮
  const renderActionList = () => {
    const btnList = [
      <Button key="goBack" onClick={onClose}>
        返回
      </Button>,
    ];
    if (mode === 'multiple') {
      const choiceBtn =
        selectList.length === renderList.length ? (
          <Button key="cancelChoice" onClick={() => setSelectList([])}>
            取消选择
          </Button>
        ) : (
          <Button key="allChoice" onClick={() => setSelectList([...renderList])}>
            全部选择
          </Button>
        );
      btnList.unshift(choiceBtn);
    }
    return btnList;
  };

  return (
    <Drawer
      title={<Header title={title} onCancel={onClose} actionList={renderActionList()} />}
      onClose={onClose}
      open={open}
      footer={<Footer total={selectList.length} onSubmit={handlderSubmit} />}
    >
      <div className={styles.employeeListContainer}>
        <Search
          value={keyWords}
          onChange={(e) => setKeyWords(e.target.value)}
          onSearch={() => setKeyWords('')}
        />
        <List
          mode={mode}
          searchValue={keyWords}
          employees={renderList}
          selectList={selectList}
          setSelectList={setSelectList}
          disabledList={disabledList}
        />
      </div>
    </Drawer>
  );
};

export default Index;
