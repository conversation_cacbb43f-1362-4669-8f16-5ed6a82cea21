.modalSelectFiles {
  .title {
    display: flex;
    height: 32px;
    line-height: 32px;
    align-items: baseline;

    :global {
      span:last-child {
        color: initial;
        margin-left: 10px;
        font-size: 14px;
        b {
          color: var(--ant-color-error);
          font-weight: normal;
        }
      }
    }
  }
}

.filter {
  padding: 16px;

  :global(.ant-form-item) {
    margin-bottom: 0;
  }

  .rightBar {
    text-align: right;
  }
}

.filesList {
  margin: 0 8px;
  height: 320px;

  :global {
    .ant-table-tbody-virtual .ant-table-tbody-virtual-scrollbar-horizontal {
      display: none;
    }
    .ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:first-child {
      border-radius: 0px;
    }
    .ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:last-child {
      border-radius: 0px;
    }
    .ant-table {
      font-size: 14px !important;
      width: 100%;
      th {
        height: 36px;
        line-height: 36px;
        padding: 0 0 0 10px !important;
        font-weight: normal !important;
        background-color: #e8eaf6 !important;
        &::before {
          height: 0 !important;
        }
      }
      td {
        height: 40px;
        padding: 0 0 0 10px !important;
        font-weight: normal !important;
        .ant-table-cell:empty:after,
        .ant-table-cell span:empty:after {
          content: '--';
        }
      }
    }
  }

  .deactive {
    background: #8c9eff !important;
  }
}

.selectedFilesList {
  margin: 0 8px;
  height: 242px;

  .title {
    font-size: 16px;
    color: var(--ant-color-error);
    height: 46px;
    line-height: 46px;
  }

  :global {
    .ant-table-tbody-virtual .ant-table-tbody-virtual-scrollbar-horizontal {
      display: none;
    }
    .ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:first-child {
      border-radius: 0px;
    }
    .ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:last-child {
      border-radius: 0px;
    }
    .ant-table {
      font-size: 14px !important;
      width: 100%;
      th {
        height: 36px;
        line-height: 36px;
        padding: 0 0 0 10px !important;
        font-weight: normal !important;
        background-color: #e8eaf6 !important;
        &::before {
          height: 0 !important;
        }
      }
      td {
        height: 40px;
        padding: 0 0 0 10px !important;
        font-weight: normal !important;
        .ant-table-cell:empty:after,
        .ant-table-cell span:empty:after {
          content: '--';
        }
      }
    }
  }
}

.selectFiles {
  // border: 1px solid #efefef;

  .header {
    height: 62px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #efefef;

    .title {
      font-size: 20px;
      font-weight: bold;
      color: var(--ant-color-primary);
      display: flex;
      height: 32px;
      line-height: 32px;
      align-items: baseline;

      :global {
        span:last-child {
          color: initial;
          margin-left: 10px;
          font-size: 14px;
          b {
            color: var(--ant-color-error);
            font-weight: normal;
          }
        }
      }
    }
  }

  .footer {
    padding: 8px 0 24px;
    margin-top: 0;
    text-align: center;
  }
}
