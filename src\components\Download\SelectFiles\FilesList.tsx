import { searchConditionAll, searchConditionResult } from '@/api/library';
import { formatFileSizeFilterValue } from '@/components/Filters';
import type { FilePreviewAPI } from '@/components/FliePreview';
import FilePreview from '@/components/FliePreview';
import { Button, Space, Table } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import Context from '../Context';
import { getColumns } from './columns';
import styles from './index.module.less';
import { formatQueryData, formatTemporaryData } from './useCtxStore';

const Component = () => {
  const filePreviewRef = useRef<FilePreviewAPI>();
  const { useSelectFilesCtxStore, useMainPanelCtxStore, config } = useContext(Context);
  const [
    selectFilesOpen,
    setColumnsFilterData,
    queryData,
    list,
    setList,
    selectedList,
    setSelectedList,
    selectedMap,
    setSelectedMap,
    queryType,
    setQueryType,
    temporaryData,
  ] = useSelectFilesCtxStore!((state) => [
    state.selectFilesOpen,
    state.setColumnsFilterData,
    state.queryData,
    state.list,
    state.setList,
    state.selectedList,
    state.setSelectedList,
    state.selectedMap,
    state.setSelectedMap,
    state.queryType,
    state.setQueryType,
    state.temporaryData,
  ]);
  const [selectedFileMap] = useMainPanelCtxStore!((state) => [state.selectedFileMap]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageNumber, setPageNumber] = useState({ current: 1 });
  const [pageSize] = useState(1000);
  const [total, setTotal] = useState(0);
  const [loadedList, setLoadedList] = useState<any[]>([]);
  const didMountRef = useRef(false);
  const columns = useMemo(() => {
    return getColumns(true, config).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              {list.length === 0 && <span>操作</span>}
              {list.length > 0 && (
                <Button
                  type="primary"
                  size="small"
                  ghost={selectedList.length < list.length}
                  onClick={() => {
                    if (selectedList.length === list.length) {
                      const map = { ...selectedFileMap };
                      setSelectedMap(map);
                      setSelectedList(Object.values(map));
                    } else {
                      const map: any = {};
                      list.forEach((item: any) => {
                        map[item.filePath] = item;
                      });
                      setSelectedMap(map);
                      setSelectedList(Object.values(map));
                    }
                  }}
                >
                  全部选择
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          let element = null;
          if (selectedMap[row.id]) {
            element = (
              <Button
                type="primary"
                size="small"
                disabled={Boolean(selectedFileMap[row.filePath])}
                onClick={() => {
                  const map = { ...selectedMap };
                  delete map[row.id];
                  setSelectedMap(map);
                  setSelectedList(Object.values(map));
                }}
              >
                已选
              </Button>
            );
          } else {
            element = (
              <Button
                type="primary"
                size="small"
                ghost
                onClick={() => {
                  const map = { ...selectedMap };
                  map[row.id] = row;
                  setSelectedMap(map);
                  setSelectedList(Object.values(map));
                }}
              >
                多选
              </Button>
            );
          }
          return (
            <Space>
              {element}
              <Button
                style={{ paddingLeft: 0, paddingRight: 0 }}
                type="link"
                size="small"
                onClick={() => {
                  preview(row);
                }}
              >
                浏览
              </Button>
            </Space>
          );
        };
      }
      return item;
    });
  }, [list, selectedList, selectedMap, selectedFileMap]);
  const getList = () => {
    const api = queryData.length === 1 ? searchConditionAll : searchConditionResult;
    api(
      queryType.current === 'current'
        ? formatTemporaryData(queryData, temporaryData, pageNumber, pageSize, config)
        : formatQueryData(queryData, { pageNumber, pageSize, config }),
      { setLoading },
    ).then(({ data }: any) => {
      const { total, list } = data;
      const nextLoadedList = [...loadedList, ...list];
      setTotal(total);
      setLoadedList(nextLoadedList);
      setList([...nextLoadedList]);
    });
  };
  useEffect(() => {
    if (total > 0 && list.length === total) {
      setHasMore(false);
    }
  }, [total, list]);
  const change = (pagination: any, filters: any, sorter: any, extra: any) => {
    const fileSize: any = filters.fileSize ? filters.fileSize[0] : {};
    switch (extra.action) {
      case 'filter':
        setColumnsFilterData({
          fileFormatTypeList: filters.fileFormatType ?? [],
          sourceList: filters.source ?? [],
          ...formatFileSizeFilterValue(fileSize),
        });
        setQueryType({ current: 'current' });
        break;
    }
  };
  const preview = (row: any) => {
    filePreviewRef.current?.open({ ...row, fileSource: 13 });
  };
  useEffect(() => {
    if (selectFilesOpen) {
      const map = { ...selectedFileMap };
      setSelectedMap(map);
      setSelectedList(Object.values(map));
    }
  }, [selectFilesOpen, queryData, selectedFileMap]);
  useEffect(() => {
    if (!loadedList.length || loadedList.length < total) {
      getList();
    }
  }, [pageNumber]);
  useEffect(() => {
    if (didMountRef.current) {
      setTotal(0);
      setLoadedList([]);
      setList([]);
      setPageNumber({ current: 1 });
    } else {
      didMountRef.current = true;
    }
  }, [queryData, temporaryData]);
  const handleScroll = useCallback(
    debounce((e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, clientHeight, scrollHeight } = e.target as HTMLDivElement;
      if (scrollTop + clientHeight >= scrollHeight - 20 && hasMore && !loading) {
        setPageNumber((value) => ({ current: value.current + 1 }));
      }
    }, 500),
    [hasMore, loading],
  );
  const [forceUpdateKey, setForceUpdateKey] = useState(0);
  useEffect(() => {
    if (queryType.current !== 'current') {
      setForceUpdateKey((prevKey) => prevKey + 1);
    }
  }, [queryType]);
  return (
    <div className={styles.filesList}>
      <Table
        key={forceUpdateKey}
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={list}
        loading={loading}
        rowKey={'id'}
        scroll={{ y: 491 }}
        pagination={false}
        onChange={change}
        onScroll={handleScroll}
      />
      <FilePreview ref={filePreviewRef} />
    </div>
  );
};

export default Component;
