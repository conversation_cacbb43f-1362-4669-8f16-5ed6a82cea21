import MultiModal from '@/components/MultiModal';
import { Button } from 'antd';
import { useContext, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Context from '../Context';
import styles from './index.module.less';

interface Props {
  onCancel?: () => void;
}

const Component = ({ onCancel }: Props) => {
  const history = useNavigate();
  const location = useLocation();
  const { useMainPanelCtxStore, useSelectFilesCtxStore, config } = useContext(Context);
  const [loadingStatus] = useMainPanelCtxStore!((state) => [state.loadingStatus]);
  const btnEnabled = useMemo(() => {
    return loadingStatus === 'complete';
  }, [loadingStatus]);
  const close = () => {
    console.log(config);
    useMainPanelCtxStore!.reset();
    useSelectFilesCtxStore!.reset();
  };
  return (
    <MultiModal
      layoutClassName="normal"
      destroyOnClose={true}
      closable={false}
      title={null}
      open={btnEnabled}
      footer={null}
      top={120}
    >
      <div className={styles.SuccessModal}>
        <div className={styles.headerBox}>
          <span
            onClick={() => {
              if (onCancel) {
                onCancel();
              }
              close();
            }}
          >
            关闭
          </span>
        </div>
        <div className={styles.contentBox}>{config?.moduleText}一键上传完成</div>
        <div className={styles.footerBoxs}>
          <Button
            type="primary"
            ghost
            onClick={() => {
              if (onCancel) {
                onCancel();
              }
              close();
              if (config?.module === 'contextMenu' && location.pathname !== '/library') {
                history('/library');
              }
            }}
          >
            去
            {config?.module === 'contextMenu' || config?.module === 'library'
              ? '文库大全'
              : config?.moduleText}
          </Button>
          <Button type="primary" onClick={close}>
            继续上传
          </Button>
        </div>
      </div>
    </MultiModal>
  );
};

export default Component;
