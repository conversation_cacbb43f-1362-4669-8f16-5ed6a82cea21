import { Button, Input } from 'antd';
import { FC } from 'react';
import styles from './index.module.less';
interface Props {
  value: string;
  onChange: (e: any) => void;
  onSearch: () => void;
}
const Component: FC<Props> = ({ value, onChange, onSearch }) => {
  return (
    <div className={styles.searchBar}>
      <Input value={value} onChange={onChange} />
      <Button type="primary" onClick={onSearch}>
        全部查询
      </Button>
      <Button type="primary" onClick={onSearch}>
        结果查询
      </Button>
    </div>
  );
};

export default Component;
