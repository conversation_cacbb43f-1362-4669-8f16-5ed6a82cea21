import { Drawer } from 'antd';
import { DrawerClassNames } from 'antd/es/drawer/DrawerPanel';
import { FC, useEffect, useRef, useState } from 'react';
import Styles from './index.module.less';

export interface DataProps {
  label: string;
  value: string | number;
  checked: boolean;
}

export interface DataSelectProps {
  title: string;
  isOpenDrawer: boolean;
  data: DataProps[];
  onSelect: (selectItem: DataProps) => void;
}

const SelectComponent: FC<DataSelectProps> = ({ title, isOpenDrawer, data, onSelect }: DataSelectProps) => {
  const divRef = useRef<HTMLDivElement | null>(null);
  //根据内容动态计算高度 324
  const h: number = 80 + (data.length + 1) * 54;
  const [height] = useState(h > 458 ? 458 : h);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(!isVisible);
  }, [isOpenDrawer]);


  const handleClose = () => {
    setIsVisible(false);
  };

  if(!isVisible) return null;

  const classNames: DrawerClassNames = {
    body: Styles.body1,
    header: Styles.headerStyle,
    content: Styles.body1,
  };

  return (
    <>
      <Drawer
        placement="bottom"
        open={isVisible}
        getContainer={false}
        style={{ position: 'absolute' }}
        key="bottom"
        height={height}
        keyboard={false}
        maskClosable={false}
        classNames={classNames}
      >
        <div className={Styles.comSelectContainer} ref={divRef}>
          <div className={Styles.comSelectHeader}>
            <div className={Styles.titleBox}>{title}</div>
            <div className={Styles.closeButton} onClick={handleClose}>关闭</div>
          </div>
          <div className={Styles.comSelectList}>
            {data.map((dataItem) => (
              <div key={dataItem.value} className={Styles.listItem} onClick={() => {
                setIsVisible(false);
                dataItem.checked = true;
                onSelect(dataItem);
              }}>{dataItem.label}</div>
            ))}
          </div>
          <div className={Styles.listItem} style={{
            marginTop: '8px',
            color: 'rgba(0, 0, 0, 0.65)'
          }} onClick={handleClose}>取消</div>
        </div>
      </Drawer>
    </>
  );
};

export default SelectComponent;
