.edit-card {
  margin: 0 0 10px 5px;
  padding-top: 10px;
  .item-par {
    min-height: 36px;
    .edit-span {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.6);
      margin-right: 10px;
      .text-span {
        width: 70px;
        margin-right: 5px;
        display: inline-block;
        text-align-last: justify;
      }
    }
    .edit-box {
      width: 100%;
      font-size: 14px;
      word-break: break-word;
    }
    .font-color {
      color: #eb5323;
      :global {
        .ant-typography {
          color: #eb5323 !important;
        }
      }
    }
    .symbol-span {
      cursor: pointer;
      display: inline-block;
      height: 18px;
      line-height: 18px;
      text-align: center;
      width: 40px;
      height: 18px;
      border-radius: 4px;
      background: #d3d3d3;
      font-size: 12px;
      color: #4d4d4d;
    }
  }

  .item-pars {
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 11px;
    padding-left: 80px;
    padding-right: 20px;
    .content-span {
      display: inline-block;
      height: 32px;
      border-radius: 4px;
      align-items: center;
      padding: 5px 8px;
      background: #8092ff;
      font-size: 14px;
      color: #ffffff;
      cursor: pointer;
      text-wrap: nowrap;
    }
  }
  .item-parc {
    height: 40px;
    border-radius: 4px;
    padding: 6px 8px;
    margin: 0 20px 0 80px;
    background: #8092ff;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .icon {
      width: 16px;
      height: 16px;
    }
    .title {
      height: 20px;
      opacity: 1;
      font-size: 14px;
      font-weight: 500;
      color: #ffffff;
      z-index: 1;
    }
    .duration {
      height: 17px;
      font-size: 12px;
      font-weight: 500;
      color: #ffffff;
    }
    button {
      width: 60px;
      height: 28px;
      border-radius: 4px;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 4px 16px;
      gap: 8px;
      background: #ffffff;
      line-height: 20px;
      font-size: 14px;
    }
    .okButton {
      color: rgba(0, 0, 0, 0.85);
    }
    .deButton {
      color: #ff1744;
    }
  }
  .contentBox {
    position: relative;
    padding-bottom: 22px;
    .expanded-button {
      position: absolute;
      right: 10px;
      cursor: pointer;
      display: inline-block;
      height: 18px;
      line-height: 18px;
      text-align: center;
      width: 40px;
      height: 18px;
      border-radius: 4px;
      background: #d3d3d3;
      font-size: 12px;
      color: #4d4d4d;
    }
  }
}

.edit-box {
  :global {
    .ant-input {
      border: none;
      padding: 0 3px;
      margin-bottom: 5px;
    }
  }

  .imageBox {
    width: 100%;
    margin-bottom: 5px;
    .image {
      width: 49%;
      margin-bottom: 5px;
      img {
        cursor: pointer;
        height: 180px;
        width: 100%;
      }
    }
  }

  .fileBox {
    width: 100%;
    margin-bottom: 5px;
    .file {
      width: 95%;
    }
  }
  .delBtn {
    cursor: pointer;
    color: #eb5323;
    letter-spacing: 2px;
    border: 1px solid #eb5323;
    padding: 0 6px;
    border-radius: 4px;
    font-size: 12px;
    display: flex;
    align-items: center;
  }
  .delBtn:hover {
    background-color: #e98466;
    color: #ffffff;
  }
}

//正文类型
.contentPopup {
  :global {
    .ant-list {
      border: none;
    }
    .ant-list-item {
      padding: 0 !important;
      height: 60px;
      button {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        letter-spacing: 3px;
        width: 100%;
        height: 100%;
        border: none;
      }
    }
    .ant-list-footer {
      padding: 0 !important;
    }
    .ant-btn-default:disabled,
    .ant-btn-default.ant-btn-disabled {
      background-color: #ffffff;
    }
  }
}
.contentFooter {
  background-color: #ececec;
  padding-top: 10px;
  padding-bottom: 20px;
  .contentCancal {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 80px;
    cursor: pointer;
    background-color: #ffffff;
    border-radius: 0 0 12px 12px;
    letter-spacing: 3px;
  }
}
//发送按钮
.itemParSend {
  span {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #3d5afe;
    color: #ffffff;
    letter-spacing: 5px;
    font-size: 16px;
    border-radius: 4px;
    cursor: pointer;
  }
}
