import {
  getSourceFilterProps,
  getFileSizeFilterProps,
  getFileFormatTypeFilterProps,
} from '@/components/Filters';
import useFromModuleStore from '@/store/useFromModuleStore';
import { formatFileSize } from '@/utils/common';

export interface FileRecord {
  id: string;
  shareRealName: string;
  title: string;
  source: string;
  sourceName: string;
  fileSize: number;
  fileFormatType: string;
  fileFormatTypeName: string;
  [prop: string]: any;
}

export type ColumnField =
  | 'index'
  | 'shareRealName'
  | 'title'
  | 'source'
  | 'fileSize'
  | 'fileFormatType'
  | 'actions';

export const getColumns = (
  hasFilters: boolean,
  columnFields: ColumnField[] = [
    'index',
    'shareRealName',
    'title',
    'source',
    'fileSize',
    'fileFormatType',
    'actions',
  ],
) => {
  const { fromModule, fromModuleQuery } = useFromModuleStore.getState();
  const columns: any[] = [];
  columnFields.forEach((field) => {
    switch (field) {
      case 'index':
        columns.push({
          title: '序号',
          dataIndex: 'index',
          key: 'index',
          width: 45,
          render: (value: any, row: any, index: number) => index + 1,
        });
        break;
      case 'shareRealName':
        columns.push({
          title: '姓名',
          dataIndex: 'shareRealName',
          key: 'shareRealName',
          width: 75,
          ellipsis: true,
        });
        break;
      case 'title':
        columns.push({
          title: '文件名称',
          dataIndex: 'title',
          key: 'title',
          width: 120,
          ellipsis: true,
        });
        break;
      case 'source':
        columns.push({
          title: '文件来源',
          dataIndex: 'source',
          key: 'source',
          width: 100,
          ...(hasFilters ? getSourceFilterProps() : {}),
          render: (value: any, row: any) => {
            return row.sourceName;
          },
        });
        break;
      case 'fileSize':
        columns.push({
          title: '文件大小',
          dataIndex: 'fileSize',
          key: 'fileSize',
          width: 100,
          ...(hasFilters ? getFileSizeFilterProps() : {}),
          render: (value: any, row: any) => {
            return formatFileSize(row.fileSize);
          },
        });
        break;
      case 'fileFormatType':
        columns.push({
          title: '文件格式',
          dataIndex: 'fileFormatType',
          key: 'fileFormatType',
          width: 100,
          ellipsis: true,
          ...(hasFilters&&!(fromModule&&fromModuleQuery.currentModuleFlag) ? getFileFormatTypeFilterProps() : {}),
          render: (value: any, row: any) => {
            return row.fileFormatTypeName;
          },
        });
        break;
      case 'actions':
        columns.push({
          title: '操作',
          dataIndex: 'actions',
          key: 'actions',
          width: 110,
        });
        break;
    }
  });

  return columns;
};
