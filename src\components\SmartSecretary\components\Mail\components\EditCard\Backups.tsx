import { MailBookReq, MailChatAttachVOList, options } from '@/api/mail/chat/mailModels';
import ediVoice from '@/assets/images/mail/ediVoice.png';
import FliePreview, { FilePreviewAPI } from '@/components/FliePreview';
import useUserStore from '@/store/useUserStore';
import { parserHtmlToString } from '@/utils/parser';
import { Button, Flex, Popconfirm, Typography } from 'antd';
import { FC, useEffect, useRef, useState } from 'react';
import Collapse from '../Collapse/Collapse';
import CollapseFiles from '../Collapse/CollapseFiles';
import styles from './EditCard.module.less';
type EditProps = {
  item: MailBookReq;
  addContent: (contentType: number) => void;
  deleteRecord: (data: any) => void;
  isShowEditContent: boolean;
  mailChatContentVOList: MailChatAttachVOList[];
};

const EditCard: FC<EditProps> = ({
  item,
  addContent,
  deleteRecord,
  isShowEditContent,
  mailChatContentVOList,
}) => {
  const { Paragraph } = Typography;
  const [expanded, setExpanded] = useState(false);
  const baomidengji = options.find((o) => +o.value == item.confidentialityStatus)?.label;
  const containerRef = useRef<HTMLDivElement>(null);
  const [contentType, setContentType] = useState<number>(0);
  const [isShow, setIsShow] = useState<boolean>(false);
  const userInfo = useUserStore((state) => state.userInfo);
  const chaoSong = () => {
    const a = item.copyAddressList?.map((i) => i.addressee) || [];
    const b =
      item.copyAddressGroupList?.map((i) => `${i.groupName}(${i.contactPersonVOS.length})`) || [];
    return [...a, ...b];
  };
  const filePreviewRef = useRef<FilePreviewAPI>();

  const faSong = () => {
    const a = item.addressList?.map((i) => i.addressee) || [];
    const b =
      item.addressGroupList?.map((i) => `${i.groupName}(${i.contactPersonVOS.length})`) || [];
    return [...a, ...b];
  };

  const button = (
    <button className={styles.expandedButton} onClick={() => setExpanded(false)}>
      折叠
    </button>
  );

  useEffect(() => {
    resolveContent();
  }, [expanded]);

  useEffect(() => {
    if (isShowEditContent) {
      setTimeout(() => {
        setIsShow(true);
      }, 1000);
    }
  }, [isShowEditContent]);

  const preview = (index: number) => {
    filePreviewRef.current?.open({
      id: mailChatContentVOList[index].attachmentId || '302470223',
      title: mailChatContentVOList[index].attachmentName,
      fileSize: mailChatContentVOList[index].attachmentSize,
      visitPath: mailChatContentVOList[index].attachmentUrl,
      fileType: mailChatContentVOList[index].fileType,
      fileFormatType: mailChatContentVOList[index].fileFormatType,
      safeLevel: item.confidentialityStatus,
      filePath: mailChatContentVOList[index].attachmentUrl,
      userId: userInfo?.id,
      source: 0,
    });
  };

  const resolveContent = () => {
    if (item.mailContent == '') {
      return <span>请选择正文输入方式：</span>;
    }
    if (expanded) {
      return (
        <div className={styles.contentBox}>
          <div ref={containerRef} dangerouslySetInnerHTML={{ __html: item.mailContent }}></div>
          {button}
        </div>
      );
    } else {
      return (
        <Paragraph
          ellipsis={{
            rows: 3,
            expandable: 'collapsible',
            expanded: expanded,
            onExpand: (_, info) => setExpanded(info.expanded),
            symbol: <span className={styles.symbolSpan}>展开</span>,
          }}
        >
          {parserHtmlToString(item.mailContent)}
        </Paragraph>
      );
    }
  };

  useEffect(() => {
    console.log('item', item);

    if (item.mailContent == '') {
      setContentType(0);
    } else {
      setContentType(1);
    }
  }, [item]);

  const chooseContentType = (type: number) => {
    addContent(type);
  };

  return (
    <div className={styles.editCard} id={`div-${item.id}`}>
      <Flex vertical gap={5}>
        <Flex className={styles.itemPar}>
          <span className={styles.editSpan}>
            <span className={styles.textSpan}>收件人</span>:
          </span>
          <div className={styles.editBox}>
            <Collapse items={faSong()}></Collapse>
          </div>
        </Flex>
        <Flex className={styles.itemPar}>
          <span className={styles.editSpan}>
            <span className={styles.textSpan}>标题</span>:
          </span>
          <div className={styles.editBox}>
            <span>{item.mailTitle}</span>
          </div>
        </Flex>
        <Flex className={styles.itemPar}>
          <span className={styles.editSpan}>
            <span className={styles.textSpan}>正文</span>:
          </span>
          <div className={styles.editBox}>{resolveContent()}</div>
        </Flex>
        {mailChatContentVOList.length > 0 &&
          mailChatContentVOList.map((item, index) => {
            return (
              <Flex className={styles.itemParc} key={item.attachmentId}>
                <Flex align={'center'} gap={10}>
                  <img src={ediVoice} className={styles.icon}></img>
                  <span className={styles.title}>{item.attachmentName}</span>
                </Flex>
                <Flex align={'center'} gap={10}>
                  <span className={styles.duration}>
                    {secondsToHMS(item.mediaDuration as number)}
                  </span>
                  <Button className={styles.okButton} onClick={() => preview(index)}>
                    播放
                  </Button>
                  <Popconfirm
                    title={null}
                    icon={null}
                    description="确认删除本条记录么？"
                    placement="topRight"
                    onConfirm={() => deleteRecord(item)}
                    okText="确认"
                    cancelText="取消"
                  >
                    <Button className={styles.deButton}>删除</Button>
                  </Popconfirm>
                </Flex>
              </Flex>
            );
          })}
        {isShow && (
          <Flex className={styles.itemPars}>
            {contentType !== 2 && (
              <span className={styles.contentSpan} onClick={() => chooseContentType(0)}>
                普通正文
              </span>
            )}
            {contentType !== 2 && (
              <span className={styles.contentSpan} onClick={() => chooseContentType(1)}>
                音频正文
              </span>
            )}
            {contentType !== 2 && (
              <span className={styles.contentSpan} onClick={() => chooseContentType(2)}>
                视频正文
              </span>
            )}
            {contentType !== 1 && (
              <span className={styles.contentSpan} onClick={() => chooseContentType(3)}>
                音频通话正文
              </span>
            )}
            {contentType !== 1 && (
              <span className={styles.contentSpan} onClick={() => chooseContentType(4)}>
                视频通话正文
              </span>
            )}
          </Flex>
        )}
        <Flex className={styles.itemPar}>
          <span className={styles.editSpan}>
            <span className={styles.textSpan}>附件</span>:
          </span>
          <div className={styles.editBox}>
            <CollapseFiles items={item.mailChatAttachVOList} type={'edit'}></CollapseFiles>
          </div>
        </Flex>
        <Flex className={styles.itemPar}>
          <span className={styles.editSpan}>
            <span className={styles.textSpan}>抄送</span>:
          </span>
          <div className={styles.editBox}>
            <Collapse items={chaoSong()}></Collapse>
          </div>
        </Flex>
        <Flex className={styles.itemPar}>
          <span className={styles.editSpan}>
            <span className={styles.textSpan}>密送</span>:
          </span>
          <div className={styles.editBox}>
            <Collapse items={item.secretAddressList?.map((i) => i.addressee)}></Collapse>
          </div>
        </Flex>
        <Flex className={styles.itemPar}>
          <span className={styles.editSpan}>
            <span className={styles.textSpan}>保密等级</span>:
          </span>
          <div className={`${styles.editBox} ${styles.fontColor}`}>
            {`${baomidengji}` + ' ' + `${item.timedTime}`}
          </div>
        </Flex>
      </Flex>
      <FliePreview ref={filePreviewRef} zIndex={1000} />
    </div>
  );
};

export default EditCard;

export const secondsToHMS = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return [
    hours ? hours.toString().padStart(2, '0') : '00',
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0'),
  ].join(':');
};
