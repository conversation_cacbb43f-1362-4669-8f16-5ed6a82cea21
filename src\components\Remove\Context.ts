import { createContext } from 'react';
export { default as createUseMainPanelCtxStore } from './MainPanel/useCtxStore';
export { default as createUseSelectFilesCtxStore } from './SelectFiles/useCtxStore';
import type { UseStore } from '@/store/middlewares';
import type {
  State as MainPanelState,
  SetState as MainPanelSetState,
} from './MainPanel/useCtxStore';
import type {
  State as SelectFilesState,
  SetState as SelectFilesSetState,
} from './SelectFiles/useCtxStore';

export type RemoveType =
  | 'dataBank-select-remove'
  | 'dataBank-all-remove'
  | 'dataBank-range-remove'
  | 'recycle-select-remove'
  | 'recycle-all-remove'
  | 'recycle-range-remove'
  | 'recycle-select-cancel'
  | 'recycle-all-cancel'
  | 'recycle-range-cancel';
export type ButtonText =
  | '选择删除'
  | '全部删除'
  | '按时间删除'
  | '选择永久删除'
  | '全部永久删除'
  | '按时间永久删除'
  | '选择永久取消'
  | '全部永久取消'
  | '按时间永久取消'
  | '选择取消删除'
  | '全部取消删除'
  | '按时间取消删除';
export interface Config {
  module: 'library' | 'audioPlay' | 'videoPlay';
  removeType: RemoveType;
  buttonText: ButtonText;
}

interface Value {
  useMainPanelCtxStore: null | UseStore<MainPanelState, MainPanelSetState>;
  useSelectFilesCtxStore?: null | UseStore<SelectFilesState, SelectFilesSetState>;
  config: null | Config;
}

export default createContext<Value>({
  useMainPanelCtxStore: null,
  useSelectFilesCtxStore: null,
  config: null,
});
