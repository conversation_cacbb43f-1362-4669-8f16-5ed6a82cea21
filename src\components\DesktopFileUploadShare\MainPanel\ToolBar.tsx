import { useContext, useMemo } from 'react';
import { Space, Button } from 'antd';
import Context from '../Context';
import styles from './index.module.less';

interface Props {
  onClick?: (type: string) => void;
}

const Component = ({ onClick }: Props) => {
  const { useMainPanelCtxStore } = useContext(Context);
  const [loadingStatus] = useMainPanelCtxStore!((state) => [state.loadingStatus]);
  const btnEnabled = useMemo(() => {
    return loadingStatus === 'init';
  }, [loadingStatus]);

  return (
    <div className={styles.toolBar}>
      <Space>
        <span className={styles.text}>已选文件</span>
      </Space>
    </div>
  );
};

export default Component;
