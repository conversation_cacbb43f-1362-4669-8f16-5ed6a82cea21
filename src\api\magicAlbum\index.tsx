import request from '../index';
const base = '/web-api/album';

/**
 * 查询相册模版列表
 * @returns
 */
export const getTemplateViewOder = () => {
  return request.get({
    url: `${base}/template/views/web/getTemplateViewOder`,
  });
};

/**
 * 获得模版数据分页
 * @param pageNo
 * @param pageSize
 * @returns
 */
export const getTemplatePage = (params: queryPageParamsType) => {
  return request.get({
    url: `${base}/template/views/web/getTemplateViewOderPage`,
    params,
  });
};

/**
 * 获得模版相册详情
 * @param pageNo
 * @param pageSize
 * @returns
 */
export const getTemplateDetail = (params: getTemplateParamsType) => {
  return request.get({
    url: `${base}/template/views/web/getTemplate`,
    params,
  });
};

/**
 * 首页相册列表分页，不足8条数据用模板数据补全
 * @param userId
 * @param pageNo
 * @param pageSize
 * @returns
 */
export const getAlbumPage = (params: queryPageParamsType) => {
  return request.get({
    url: `${base}/query/page`,
    params,
  });
};

/**
 * 创建相册
 * @param albumsTemplateId
 * @param albumsName
 * @param photosReqVOList
 * @returns
 */
export const createAlbum = (data: createAlbumParamsType) => {
  return request.post({
    url: `${base}/create`,
    data,
  });
};

/**
 * 编辑相册
 * @param albumsId
 * @param albumsTemplateId
 * @param albumsName
 * @param photosReqVOList
 * @returns
 */
export const updateAlbum = (data: createAlbumParamsType) => {
  return request.post({
    url: `${base}/update`,
    data,
  });
};

/**
 * 查询相册详情
 * @param albumId
 * @param tag 选填
 * @returns
 */
export const getAlbumDetail = (params: getAlbumDetailParamsType) => {
  return request.get({
    url: `${base}/query/queryAlbumDetail`,
    params,
  });
};

/**
 * 添加相册背景文字
 * @param albumsId
 * @param content
 * @param voiceType 声音类型
 * @param speed 1
 * @returns
 */
export const addBackgroundText = (data: backgroundTextParamsType) => {
  return request.post({
    url: `${base}/addBackgroundText`,
    data,
  });
};

/**
 * 用户删除照片
 * @param albumsId
 * @param photoId
 * @param userId
 * @param userName
 */
export const photosdelete = (data: photosdeleteParamsType) => {
  return request.post({
    url: `${base}/photosBak/create`,
    data,
  });
};

/**
 * 批量保存图片信息
 * @param photosReqVOList
 * @returns
 */
export const createPhotoBatch = (data: any) => {
  return request.post({
    url: `${base}/photo/createBatch`,
    data,
  });
};
/**
 * 用户删除照片-批量
 */
export const photosdeleteBatch = (data: photosdeleteBatchType) => {
  return request.post({
    url: `${base}/photosBak/createBatch`,
    data,
  });
};

/**
 * 获取用户删除照片
 * @param ...
 * @param pageNo
 * @param pageSize
 */
export const getPhotosBakPage = (data: any) => {
  return request.post({
    url: `${base}/photosBak/getPhotosBakPage`,
    data,
  });
};

/**
 * 获取用户所有删除照片记录
 * @param id
 */
export const getPhotosBakAll = (params: any) => {
  return request.get({
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    url: `${base}/photosBak/get`,
    params,
  });
};

/**
 * 批量修改相册名称
 * @param albumNameUpdateReqVOList {albumsId,albumsName}
 */
export const updateAlbumNameBatch = (data: any) => {
  return request.post({
    url: `${base}/updateAlbumNameBatch`,
    data,
  });
};

/**
 * 创建相册模板选定记录
 * @param albumsTemplateId
 */
export const choseAlbumTempLog = (data: any) => {
  return request.post({
    url: `${base}/template/views/web/create`,
    data,
  });
};

/**
 * 人工智慧文字转语音
 * @param voice
 * @param text
 * @param speed 1
 * @param language  zh-cn
 */
export const useApitts = (data: any) => {
  return request.post({
    url: '/apitts',
    headers: { 'Content-Type': 'multipart/form-data' },
    data,
  });
};

// 网络世界模拟接口
import {
  backgroundTextParamsType,
  createAlbumParamsType,
  getAlbumDetailParamsType,
  getTemplateParamsType,
  photosdeleteBatchType,
  photosdeleteParamsType,
  queryPageParamsType,
  SharePageParams,
  ShareParams,
} from './interface';

//共享天地查询群里面的相册
export const queryAlbumByPage = (data: any) => {
  return request.post({
    url: `${base}/share/getGroupShareAlbumPage`,
    data,
  });
};

/**
 * 获取相册分享列表接口
 * @param albumsIds
 * @returns
 */
export const getAlbumsPage = (data: SharePageParams) => {
  return request.post({
    url: `${base}/share/getAlbumsPage`,
    data,
  });
};

/**
 * 分页查询不同类型相册列表
 * @param albumsCategory
 * @param pageNo
 * @param pageSize
 * @returns
 */
export const queryPageCategoryAlbum = (data: any) => {
  return request.post({
    url: `${base}/query/queryPageCategoryAlbum`,
    data,
  });
};

/**
 * 分享相册
 * @param data ShareParams
 */
export const createSharePhotos = (data: ShareParams) => {
  return request.post({
    url: `${base}/share/createSharePhotos`,
    data,
  });
};

/**
 * 数据银行删除相册
 * @param albumsIds []
 * @param deleteStatus 传1，也可以不传
 * @param shareUserReq [
    {
      "shareId": 0,
      "recipientUserId": 0,
      "recipientGroupId": 0,
      "albumId": 0
    }
  ]
  * @param shareGroupReq": [
    {
      "shareId": 0,
      "recipientUserId": 0,
      "recipientGroupId": 0,
      "albumId": 0
    }
  ]
 * @returns
 */
export const deleteDataBankAlbumPhotos = (data: any) => {
  return request.post({
    url: `${base}/deleteDataBankAlbumPhotos`,
    data,
  });
};

/**
 * 回收站彻底删除或恢复相册
 * @param albumsIds []
 * @param deleteStatus 恢复-0 永久删除-2
 * @returns
 */
export const deleteRecycleAlbumPhotos = (data: any) => {
  return request.post({
    url: `${base}/deleteRecycleAlbumPhotos`,
    data,
  });
};
/**
 * 数据银行删除所有相册
 * @returns
 */
export const deleteDataBankAllAlbumPhotos = (data: any) => {
  return request.post({
    url: `${base}/deleteDataBankAllAlbumPhotos`,
    data,
  });
};
/**
 * 回收站彻底删除或恢复所有相册
 * @returns
 */
export const deleteRecyclebinAllAlbumPhotos = (data: any) => {
  return request.post({
    url: `${base}/deleteRecyclebinAllAlbumPhotos`,
    data,
  });
};

/** 查询当前用户所有相册照片列表 */
export const queryUserPhotosList = (params: any) => {
  return request.get({
    url: `${base}/query/queryUserPhotosList`,
    params,
  });
};

/** 根据照片ID查询所在相册信息列表 */
export const queryAlbumListByPhotoId = (params: any) => {
  return request.get({
    url: `${base}/query/queryAlbumListByPhotoId`,
    params,
  });
};
