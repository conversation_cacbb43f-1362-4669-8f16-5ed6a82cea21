import request from '../index';

// 数据银行验证
export const validateDataBankAuth = (data: DataBankAuth) => {
  return request.post({
    url: '/web-api/account/auth/dataBankAuth',
    data,
  });
};

// 企业数据银行验证
export const validateDataBankAuthEE = (data: DataBankAuth) => {
  return request.post({
    url: '/web-api/account/auth/enterprise/dataBankAuth',
    data,
  });
};

// 全部删除
export const deleteAllData = (data: any) => {
  return request.delete({
    url: '/web-api/library/dataBank/deleteAllData',
    data,
  });
};
//共享天地-选择删除
export const deleteByIds = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/deleteByIds',
    data,
  });
};
//共享天地-全部删除
export const deleteALL = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/deleteAll',
    data,
  });
};
//共享天地-选择所有数据
export const restoreAll = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/restoreAll',
    data,
  });
};
//共享天地-选择恢复数据
export const restoreByIds = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/restoreByIds',
    data,
  });
};
// 备忘祝福预览
export const getPreviewFile = (url: string) => {
  return request.get({
    url,
  });
};
