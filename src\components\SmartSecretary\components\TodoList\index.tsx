/**
 * 待办事项
 */

import { getTodoList } from '@/api/todoList';
import { TodoListResProps } from '@/api/todoList/module';
import useAppStore from '@/store/useAppStore';
import useIntelligentSecretaryStore from '@/store/useIntelligentSecretaryStore';
import useJavaWebSocketStore from '@/store/useJavaWebSocketStore';
import { getWidth } from '@/utils/common';
import { List } from 'antd';
import { FC, useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import CommonItem from './components/CommonItem';
import styles from './index.module.less';

const Index: FC = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({ total: 0 });
  const [list, setList] = useState<TodoListResProps[]>([]);
  const [response] = useJavaWebSocketStore((state) => [state.response]);
  const { channel } = useAppStore((state: any) => state);
  const [hasMore, setHasMore] = useState(true);
  const [pageNo, setPageNo] = useState(1);
  const [text, queryData, queryType] = useIntelligentSecretaryStore((state) => [
    state.text,
    state.queryData,
    state.queryType,
  ]);
  const fetchList = async (page: number) => {
    try {
      const params: any = { pageNo: page, pageSize: 20 };
      if (queryType.current === 'current') {
        params.keyWords = [text];
      } else if (queryType.current === 'result') {
        params.keyWords = queryData;
      }
      const { data } = (await getTodoList(params)) as { data: any };
      console.log('data', data);
      const nextItems = data.list;
      if (nextItems.length < 20) setHasMore(false); // 如果返回的数据少于一页，说明没有更多数据了
      setData(data);
      setList((prev) => (page === 1 ? nextItems : [...prev, ...nextItems]));
      setPageNo(page);
    } catch (error) {
      //
    }
  };

  useEffect(() => {
    fetchList(1);
  }, [queryType]);

  useEffect(() => {
    // 收到推送的待办
    if (response.noticeType === 'TODO_ITEMS') {
      setTimeout(() => {
        fetchList(1);
      }, 1000);
    }
  }, [response]);

  return (
    <div className={styles.todoListContainer}>
      <div className={styles.todoList}>
        <div
          id="scrollableTodoDiv"
          className={styles.scrollableDiv}
          style={{ height: window.innerHeight - getWidth(channel === 'web' ? 428 : 360) }}
        >
          <InfiniteScroll
            dataLength={list.length} // 已加载的数据长度
            next={() => fetchList(pageNo + 1)} // 加载更多数据的函数
            hasMore={hasMore} // 是否还有更多数据
            loader={false}
            // loader={<div>加载中...</div>} // 加载中的提示信息
            // endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
            scrollableTarget="scrollableTodoDiv"
          >
            <List
              className={styles.todoList}
              // loading={initLoading}
              itemLayout="horizontal"
              // loadMore={loadMore}
              dataSource={list}
              renderItem={(item, index) => (
                // item.todoType === 4 ? (
                //   <MailItem data={item} />
                // ) : item.todoType === 105 ? (
                //   <CommonItem data={item} />
                // ) : (
                //   <ApprovalItem data={item} />
                // )
                <CommonItem key={index} data={item} onInit={() => fetchList(1)} />
              )}
            />
          </InfiniteScroll>
        </div>
      </div>
    </div>
  );
};

export default Index;
