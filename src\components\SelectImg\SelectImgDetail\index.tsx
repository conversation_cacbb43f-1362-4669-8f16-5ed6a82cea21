import { searchConditionAll, searchConditionResult } from '@/api/library';
import { getAlbumDetail, getPhotosBakPage, getTemplateDetail } from '@/api/magicAlbum';
import default_img from '@/assets/images/magicAlbum/default_img.png';
import YbDatePicker from '@/components/YbDatePicker';
import { TempListType } from '@/pages/MagicAlbum/album.interface';
import { getImg } from '@/utils/common';
import { Button, Flex, Form, Input, message } from 'antd';
import classNames from 'classnames';
import { forwardRef, MouseEvent, useEffect, useRef, useState } from 'react';
import HTMLFlipBook from 'react-pageflip';
import { useImmer } from 'use-immer';
import styles from './index.module.less';

const flipConfig = {
  width: (404 * window.innerWidth) / 1920,
  height: (570 * window.innerWidth) / 1920,
  flippingTime: 600,
  maxShadowOpacity: 0.7,
};

const pxToVw = (px: any) => {
  return `${(px / 1920) * 100}vw`;
};
let imgData: any = {
  list: { 1: [], 2: [], 3: [] },
  total: { 1: 0, 2: 0, 3: 0 },
  index: { 1: 0, 2: 0, 3: 0 },
  pager: { 1: 0, 2: 0, 3: 0 },
  query: { realName: [], startTime: [], endTime: [] },
  queryType: 'all',
};
let filterTime = { startTime: '', endTime: '' };
let albumsTemplateDataStr = '';
const SelectImgDetail = forwardRef((props: any, ref) => {
  const [messageApi, contextHolder] = message.useMessage();
  const {
    onAdd,
    closeModal,
    closeSelectImg,
    albumsId,
    albumsTemplateId,
    albumsCategorySingle,
    multiple,
    selectTag,
    openSource,
    isShowInsertBtn = true,
    deleteCache = [], // 数据就是相册图片数据格式, 有删除缓存说明是新增相册进来的
    isShare,
  } = props;
  const albumSelectRef: any = useRef(null);
  const [selectedList, setSelectedList] = useImmer<string[]>([]);
  const [filterDate, setFilterDate] = useState('');
  const [almbumList, setAlmbumList] = useImmer<TempListType[]>([]);
  const [current, setCurrent] = useState(2);
  const [realNameInput, setRealNameInput] = useState('');
  const [albumsTemplateCategory, setAlbumsTemplateCategory] = useState('');

  const pxToVw = (px: any) => {
    return `${(Number(px / 1920) * 100).toFixed(2)}vw`;
  };

  const nextPage = () => {
    albumSelectRef.current.pageFlip().flipNext();
  };

  const prevPage = () => {
    albumSelectRef.current.pageFlip().flipPrev();
  };

  const onFlip = (e: any) => {
    setCurrent(e.data + 2);
    if (
      albumsTemplateId === 1 &&
      albumSelectRef.current.pageFlip().getCurrentPageIndex() > 1 &&
      albumSelectRef.current.pageFlip().getCurrentPageIndex() + 2 >=
        albumSelectRef.current.pageFlip().getPageCount()
    ) {
      Promise.all([getConditionImg(1), getConditionImg(2)]).then((promiseRes) => {
        insetImg(true);
      });
    }
  };
  /**
   * 选定照片
   * @param e 鼠标事件
   * @param imgkey 图片对应位置,后续index换成图片id和url
   * @param type 1-单选 2-多选
   */
  const confirmImg = (e: MouseEvent, imgkey: string, type: number) => {
    e.stopPropagation();
    if (selectedList.includes(imgkey)) {
      setSelectedList((draft) => draft.filter((item) => item != imgkey));
    } else if (type === 1) {
      setSelectedList([imgkey]);
    } else {
      setSelectedList((draft) => {
        draft.push(imgkey);
      });
    }
  };
  /**
   *  确认添加图片，后续将key替换为图片id和url
   */
  const confirmSelect = () => {
    let resList: any = [];
    selectedList.map((v) => {
      const arr: Array<string> = v.split('-');
      resList.push(almbumList[+arr[0]].list[+arr[1]].imgInfo);
    });
    //文库大全的图片
    if (!albumsId) {
      resList = resList.map((data: any) => {
        return {
          albumId: '',
          photoId: data.id,
          userId: data.userId,
          path: data.filePath,
          fileName: data.title,
          width: data.imgWidth || 0,
          height: data.imgHeight || 0,
          size: data.fileSize,
          mimeType: data.fileType,
          tag: data.imgIsStandard,
          uploadDate: data.createTime,
          createTime: data.createTime,
          visitPath: data.visitPath,
          description: '',
          secretKey: data.secretKey,
          photoSource: data.source,
        };
      });
    }
    onAdd(resList, isShare);
    if (openSource !== 'group') closeModal();
  };
  /** 获取相册数据 */
  const getAlbumDetailFn = () => {
    if (albumsId) {
      // 已生成相册情况进入
      getAlbumDetail({ albumId: albumsId, albumsCategorySingle }).then((res: any) => {
        const { photosRespVOs, songsRespVO } = res.data;
        albumsTemplateDataStr = songsRespVO.albumsTemplateData;
        const list = JSON.parse(albumsTemplateDataStr);
        let index = 0;
        list.map((page: TempListType) => {
          page.list.map((item) => {
            item.x = item.x ? (+item.x * 0.86).toFixed(2) : '';
            item.y = item.y ? (+item.y * 0.86).toFixed(2) : '';
            item.w = item.w ? (+item.w * 0.86).toFixed(2) : '';
            item.h = item.h ? (+item.h * 0.86).toFixed(2) : '';
            if (!(item.itemType === 1 && index < photosRespVOs.length)) {
              return;
            }
            if (item.tag !== photosRespVOs[index].tag) {
              return;
            }
            page.hasTrueImg = true;
            item.img = photosRespVOs[index].visitPath;
            item.imgInfo = photosRespVOs[index];
            index++;
          });
        });
        setAlmbumList(list.filter((v: TempListType) => v.hasTrueImg));
      });
    } else if (albumsTemplateId) {
      // 模板相册进入
      if (albumsTemplateId === 1) {
        searchAllOrResult('all');
      } else {
        getTemplateDetail({ albumsTemplateId }).then((res: any) => {
          albumsTemplateDataStr = res.data.albumsTemplateData;
          const list = JSON.parse(albumsTemplateDataStr);
          list.map((page: TempListType) => {
            page.list.map((item) => {
              item.x = item.x ? (+item.x * 0.86).toFixed(2) : '';
              item.y = item.y ? (+item.y * 0.86).toFixed(2) : '';
              item.w = item.w ? (+item.w * 0.86).toFixed(2) : '';
              item.h = item.h ? (+item.h * 0.86).toFixed(2) : '';
            });
          });
          setAlmbumList(list);
          setAlbumsTemplateCategory(res.data?.albumsTemplateCategory);
        });
      }
    } else {
      console.error('没有相册id模版id');
    }
  };
  const getConditionImg = (tag: number) => {
    const searchCondition: any =
      imgData.queryType === 'all' ? searchConditionAll : searchConditionResult;

    return new Promise((resolve) => {
      if (imgData.total[tag] === 0 || imgData.list[tag].length < imgData.total[tag]) {
        imgData.pager[tag]++;
        // 携带请求参数
        const queryParams = Object.assign(
          {
            fileFormatType: ['library_file_type_pic'],
            // imgIsStandard: tag,
            imgIsStandardList: tag === 1 ? [1, 3] : [2], //tag, 目前上传的图片没有宽高也就没有横竖tag
            pageNo: imgData.pager[tag],
            pageSize: 156,
          },
          imgData.query,
        );
        if (imgData.queryType === 'delete') {
          getPhotosBakPage({ tag, pageNo: imgData.pager[tag], pageSize: 156 }).then((res: any) => {
            imgData.list[tag] = [...imgData.list[tag], ...res.data.list];
            imgData.total[tag] = res.data.total;
            resolve(true);
          });
        } else {
          searchCondition(queryParams).then((res: any) => {
            imgData.list[tag] = [...imgData.list[tag], ...res.data.list];
            imgData.total[tag] = res.data.total;
            resolve(true);
          });
        }
      } else {
        resolve(true);
      }
    });
  };

  const searchAllOrResult = (searchType: string) => {
    imgData = {
      list: { 1: [], 2: [], 3: [] },
      total: { 1: 0, 2: 0, 3: 0 },
      index: { 1: 0, 2: 0, 3: 0 },
      pager: { 1: 0, 2: 0, 3: 0 },
      query: searchType === 'all' ? { realName: [], startTime: [], endTime: [] } : imgData.query,
      queryType: searchType,
    };
    realNameInput && imgData.query.realName.push(realNameInput);
    filterTime.startTime && imgData.query.startTime.push(filterTime.startTime);
    filterTime.endTime && imgData.query.endTime.push(filterTime.endTime);
    Promise.all([getConditionImg(1), getConditionImg(2)]).then(async (promiseRes) => {
      if (albumsTemplateDataStr.length) {
        setAlmbumList([]);
        insetImg();
        return;
      }
      getTemplateDetail({ albumsTemplateId }).then((res: any) => {
        albumsTemplateDataStr = res.data.albumsTemplateData;
        setAlbumsTemplateCategory(res.data?.albumsTemplateCategory);
        insetImg(true);
      });
    });
  };

  const insetImg = (isAddPage?: boolean) => {
    const list = JSON.parse(albumsTemplateDataStr);
    let hasSetImg = false;
    list.map((page: TempListType) => {
      page.list.map((item) => {
        item.x = item.x ? (+item.x * 0.86).toFixed(2) : '';
        item.y = item.y ? (+item.y * 0.86).toFixed(2) : '';
        item.w = item.w ? (+item.w * 0.86).toFixed(2) : '';
        item.h = item.h ? (+item.h * 0.86).toFixed(2) : '';
        if (!(item.itemType === 1 && imgData.index[item.tag] < imgData.list[item.tag].length)) {
          return;
        }
        item.imgInfo = imgData.list[item.tag][imgData.index[item.tag]];
        item.imgInfo.imgIsStandard = item.tag;
        item.img =
          imgData.list[item.tag][imgData.index[item.tag]].visitPath ||
          imgData.list[item.tag][imgData.index[item.tag]].path;
        imgData.index[item.tag] += 1;
        hasSetImg = true;
      });
    });
    if (hasSetImg) {
      isAddPage ? setAlmbumList([...almbumList, ...list]) : setAlmbumList(list);
    } else {
      console.log('文库图片全部加载完');
    }
  };
  const getAllDelPhotos = async () => {
    resetData();
    imgData.queryType = 'delete';
    if (deleteCache.length) {
      deleteCache.map((v: any) => {
        imgData.list[v.tag].push(v);
      });
      imgData.total[1] = imgData.list[1].length;
      imgData.total[2] = imgData.list[2].length;
      imgData.total[3] = imgData.list[3].length;
      const list = JSON.parse(albumsTemplateDataStr);
      list.map((page: TempListType) => {
        page.list.map((item) => {
          item.x = item.x ? (+item.x * 0.86).toFixed(2) : '';
          item.y = item.y ? (+item.y * 0.86).toFixed(2) : '';
          item.w = item.w ? (+item.w * 0.86).toFixed(2) : '';
          item.h = item.h ? (+item.h * 0.86).toFixed(2) : '';
          if (!(item.itemType === 1 && imgData.index[item.tag] < imgData.list[item.tag].length)) {
            return;
          }
          item.imgInfo = imgData.list[item.tag][imgData.index[item.tag]];
          item.img = imgData.list[item.tag][imgData.index[item.tag]].path;
          imgData.index[item.tag] += 1;
        });
      });
      setAlmbumList(list);
    } else {
      // TODO 分别获取tag=1,2的删除图片
      Promise.all([getConditionImg(1), getConditionImg(2)]).then((promiseRes) => {
        insetImg();
      });
    }
  };

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRealNameInput(e.target.value);
  };
  const getFilterDate = (datestr: string) => {
    const arr = datestr.split('~');
    if (!arr[0] || !arr[1]) {
      setFilterDate(datestr.replace('~', ''));
    } else {
      setFilterDate(datestr);
    }
    filterTime = {
      startTime: arr[0] || '',
      endTime: arr[1] || '',
    };
  };
  const showImg = (temp: any) => {
    if (temp.itemType === 1) {
      return isShare ? getImg(temp.imgInfo?.hashPath) : getImg(temp.img);
    } else {
      return getImg(temp.img);
    }
  };
  const resetData = () => {
    imgData = {
      list: { 1: [], 2: [], 3: [] },
      total: { 1: 0, 2: 0, 3: 0 },
      index: { 1: 0, 2: 0, 3: 0 },
      pager: { 1: 0, 2: 0, 3: 0 },
      query: { realName: [], startTime: [], endTime: [] },
      queryType: 'all',
    };
    filterTime = {
      startTime: '',
      endTime: '',
    };
  };
  useEffect(() => {
    getAlbumDetailFn();
    return () => {
      resetData();
      albumsTemplateDataStr = '';
    };
  }, []);
  return (
    <div
      className={classNames(
        styles.albumContainOut,
        albumsTemplateCategory === '1' ? styles.albumCategory1 : undefined,
        albumsTemplateCategory === '2' ? styles.albumCategory2 : undefined,
      )}
    >
      {contextHolder}
      <Form layout="inline" className="top-5 w-full px-10">
        <Flex justify="space-between" className="w-full">
          <Flex>
            <Button onClick={closeSelectImg} className="mr-5">
              返回
            </Button>
            {openSource === 'group' ? (
              <Button
                type="primary"
                className="ml-4"
                onClick={confirmSelect}
                disabled={!selectedList.length}
                style={{ position: 'absolute', right: 60 }}
              >
                选择
              </Button>
            ) : (
              <>
                <Form.Item label="姓名：">
                  <Input
                    style={{ width: 160 }}
                    onChange={handleInput}
                    placeholder="请输入姓名查询照片"
                  />
                </Form.Item>
                <Form.Item label="时间查询：">
                  <YbDatePicker
                    width={filterDate.includes('~') ? 290 : 160}
                    dateValue={filterDate}
                    setDateValue={getFilterDate}
                  />
                </Form.Item>
                <Button
                  type="primary"
                  onClick={() => {
                    searchAllOrResult('all');
                  }}
                >
                  全部查询
                </Button>
                <Button
                  type="primary"
                  className="ml-4"
                  onClick={() => {
                    searchAllOrResult('result');
                  }}
                >
                  结果查询
                </Button>
                {isShowInsertBtn ? (
                  <>
                    <Button className="ml-4" type="primary" onClick={confirmSelect}>
                      确定添加
                    </Button>
                    <Button className="ml-4" onClick={getAllDelPhotos}>
                      从已删照片插入
                    </Button>
                  </>
                ) : (
                  <Button className="ml-4" onClick={confirmSelect}>
                    确定选择
                  </Button>
                )}
              </>
            )}
          </Flex>
        </Flex>
      </Form>
      <div className={styles.flexbox}>
        <div className={styles.albumContain}>
          <HTMLFlipBook
            onFlip={onFlip}
            width={flipConfig.width}
            height={flipConfig.height}
            maxShadowOpacity={flipConfig.maxShadowOpacity}
            flippingTime={flipConfig.flippingTime}
            className={styles.magazine}
            ref={albumSelectRef}
            disableFlipByClick={true}
            startZIndex={0}
            // 以下为默认参数不管
            style={{}}
            startPage={0}
            size={'fixed'} // 默认fixed，组件是否在父元素下拉伸
            minWidth={0}
            maxWidth={0}
            minHeight={0}
            maxHeight={0}
            drawShadow={true} // 默认true， 绘制阴影
            usePortrait={false} // 切换到纵向模式
            autoSize={true} // 导致上一页失效
            showCover={false} // 默认false，是否显示封面 第一页和最后一页会被标记为硬页
            mobileScrollSupport={true} // 默认true，移动设备触摸组件时禁止内容滚动
            clickEventForward={false} // 默认true，将点击事件转发给页面子html元素
            useMouseEvents={false} // 默认true， 使用鼠标和触摸事件翻页
            swipeDistance={30} // 默认30，检测滑动的最小距离
            showPageCorners={true}
            // renderOnlyPageLengthChange // 如果为true，则仅当页数发生变化时才会更新重绘组件
          >
            {almbumList.map((item, index) => (
              <div className={styles.pageBox} key={index}>
                <div className={styles.page}>
                  {item.list.map((temp: any, i: any) => (
                    <div key={index.toString() + i}>
                      <img
                        src={showImg(temp)}
                        className={`${!multiple && selectedList.includes(`${index.toString()}-${i}`) ? styles.selected : ''} ${temp.tag !== selectTag && selectTag !== 0 ? styles.disabled : ''}`}
                        alt="图片"
                        loading="lazy"
                        style={{
                          width: temp.w && pxToVw(temp.w),
                          height: temp.h && pxToVw(temp.h),
                          left: pxToVw(temp.x),
                          top: pxToVw(temp.y),
                          display: `${albumsId && !temp.img ? 'none' : ''}`,
                        }}
                        onClick={(e) => {
                          if (!albumsId && !temp.imgInfo?.id) {
                            messageApi.error('图片异常，禁止选择');
                            return;
                          }
                          if (temp.img && (temp.tag === selectTag || selectTag === 0)) {
                            confirmImg(e, `${index.toString()}-${i}`, multiple ? 2 : 1);
                          }
                        }}
                        onError={(e: any) => {
                          e.target.src = default_img;
                        }}
                      />
                      {multiple &&
                        !(temp.tag !== selectTag && selectTag !== 0) &&
                        temp.itemType === 1 && (
                          <div
                            className={styles.btnBoxLeft}
                            style={{
                              left: pxToVw(temp.x),
                              top: pxToVw(+temp.h + +temp.y - 24),
                            }}
                          >
                            <span
                              className={`${selectedList.includes(`${index.toString()}-${i}`) ? styles.active : ''}`}
                              onClick={(e) => {
                                if (!albumsId && !temp.imgInfo?.id) {
                                  messageApi.error('图片异常，禁止选择');
                                  return;
                                }
                                confirmImg(e, `${index.toString()}-${i}`, 2);
                              }}
                            >
                              多选
                            </span>
                          </div>
                        )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </HTMLFlipBook>
        </div>
      </div>
      <div className={styles.pager}>
        <Button disabled={current <= 2} onClick={prevPage}>
          上一页
        </Button>
        <div>
          共<span className={styles.blueFont}>{almbumList.length}</span> 页 当前第{' '}
          <span className={styles.blueFont}>
            {current - 1}-{current}
          </span>{' '}
          页
        </div>
        <Button disabled={current >= almbumList.length} onClick={nextPage}>
          下一页
        </Button>
      </div>
    </div>
  );
});

export default SelectImgDetail;
