.modalSelectFiles {
  :global {
    .ant-modal-close {
      z-index: var(--ant-z-index-popup-base);
    }
  }
  .title {
    width: 100%;
    display: flex;
    height: 32px;
    line-height: 32px;
    align-items: baseline;
    :global {
      span:last-child {
        color: initial;
        margin-left: 10px;
        font-size: 14px;
        b {
          color: var(--ant-color-error);
          font-weight: normal;
        }
      }
    }
  }
}

.filter {
  padding: 16px;

  :global(.ant-form-item) {
    margin-bottom: 0;
  }

  .rightBar {
    text-align: right;
  }
}

.filesList {
  margin: 0 8px;

  :global {
    .ant-table-tbody-virtual .ant-table-tbody-virtual-scrollbar-horizontal {
      display: none;
    }
  }
}

.selectedFilesList {
  margin: 0 8px;

  .title {
    font-size: 16px;
    color: var(--ant-color-error);
    height: 46px;
    line-height: 46px;
  }
}

.selectFiles {
  margin: 20px;
  border: 1px solid #efefef;

  .header {
    height: 62px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #efefef;

    .title {
      font-size: 20px;
      font-weight: bold;
      color: var(--ant-color-primary);
      display: flex;
      height: 32px;
      line-height: 32px;
      align-items: baseline;

      :global {
        span:last-child {
          color: initial;
          margin-left: 10px;
          font-size: 14px;
          b {
            color: var(--ant-color-error);
            font-weight: normal;
          }
        }
      }
    }
  }

  .footer {
    border-top: 1px solid #efefef;
    padding: 8px 0 24px;
    margin-top: 0;
    text-align: center;
  }
}
