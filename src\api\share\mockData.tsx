import App16 from '@/assets/images/home/<USER>/bless.png';
import App17 from '@/assets/images/home/<USER>/file.png';
import App3 from '@/assets/images/home/<USER>/msxc.png';
import App5 from '@/assets/images/home/<USER>/spbfq.png';
import App8 from '@/assets/images/home/<USER>/txl.png';
import App6 from '@/assets/images/home/<USER>/wdrj.png';
import App1 from '@/assets/images/home/<USER>/wkdq.png';
import App15 from '@/assets/images/home/<USER>/wlsj.png';
import App14 from '@/assets/images/home/<USER>/ybbj.png';
import App2 from '@/assets/images/home/<USER>/ybyx.png';
import App4 from '@/assets/images/home/<USER>/ypbfq.png';
import App12 from '@/assets/images/home/<USER>/zmrj.png';
import jpgIcon1 from '@/assets/images/share/icon1.jpg';
import svgIcon1 from '@/assets/images/share/icon1.svg';
import svgIcon10 from '@/assets/images/share/icon10.svg';
import svgIcon11 from '@/assets/images/share/icon11.svg';
import svgIcon12 from '@/assets/images/share/icon12.svg';
import svgIcon13 from '@/assets/images/share/icon13.svg';
import svgIcon14 from '@/assets/images/share/icon14.svg';
import jpgIcon2 from '@/assets/images/share/icon2.jpg';
import svgIcon2 from '@/assets/images/share/icon2.svg';
import jpgIcon3 from '@/assets/images/share/icon3.jpg';
import svgIcon3 from '@/assets/images/share/icon3.svg';
import pngIcon30 from '@/assets/images/share/icon30.png';
import pngIcon31 from '@/assets/images/share/icon31.png';
import pngIcon32 from '@/assets/images/share/icon32.png';
import pngIcon33 from '@/assets/images/share/icon33.png';
import jpgIcon4 from '@/assets/images/share/icon4.jpg';
import svgIcon4 from '@/assets/images/share/icon4.svg';
import pngIcon40 from '@/assets/images/share/icon40.png';
import pngIcon41 from '@/assets/images/share/icon41.png';
import jpgIcon5 from '@/assets/images/share/icon5.jpg';
import svgIcon5 from '@/assets/images/share/icon5.svg';
import pngIcon50 from '@/assets/images/share/icon50.png';
import pngIcon51 from '@/assets/images/share/icon51.png';
import pngIcon52 from '@/assets/images/share/icon52.png';
import pngIcon53 from '@/assets/images/share/icon53.png';
import pngIcon54 from '@/assets/images/share/icon54.png';
import svgIcon6 from '@/assets/images/share/icon6.svg';
import svgIcon7 from '@/assets/images/share/icon7.svg';
import svgIcon8 from '@/assets/images/share/icon8.svg';
import svgIcon9 from '@/assets/images/share/icon9.svg';

export const myCreation = [
  {
    id: 1,
    name: '高中同学',
    quantity: 45,
    messages: 99,
    icon: svgIcon1,
    url: '/share/category',
  },
  {
    id: 2,
    name: '大学同学',
    quantity: 15,
    messages: 9,
    icon: svgIcon2,
    url: '/share/category',
  },
  {
    id: 3,
    name: '游泳爱好',
    quantity: 5,
    messages: 99,
    icon: svgIcon3,
    url: '/share/category',
  },
  {
    id: 4,
    name: '228班',
    quantity: 35,
    messages: 99,
    icon: svgIcon4,
    url: '/share/category',
  },
  {
    id: 5,
    name: '钓鱼爱好',
    quantity: 15,
    messages: 999,
    icon: svgIcon5,
    url: '/share/category',
  },
  {
    id: 6,
    name: '自驾旅游',
    quantity: 11,
    messages: 9,
    icon: svgIcon6,
    url: '/share/category',
  },
  {
    id: 7,
    name: '唱歌爱好',
    quantity: 15,
    messages: 999,
    icon: svgIcon7,
    url: '/share/category',
  },
  {
    id: 8,
    name: '一起吃饭',
    quantity: 5,
    messages: 99,
    icon: svgIcon8,
    url: '/share/category',
  },
  {
    id: 9,
    name: '加班群',
    quantity: 4,
    messages: 9,
    icon: svgIcon9,
    url: '/share/category',
  },
  {
    id: 10,
    name: '高中同学',
    quantity: 45,
    messages: 99,
    icon: svgIcon1,
    url: '/share/category',
  },
  {
    id: 11,
    name: '大学同学',
    quantity: 15,
    messages: 9,
    icon: svgIcon2,
    url: '/share/category',
  },
  {
    id: 12,
    name: '游泳爱好',
    quantity: 5,
    messages: 99,
    icon: svgIcon3,
    url: '/share/category',
  },
  {
    id: 13,
    name: '228班',
    quantity: 35,
    messages: 99,
    icon: svgIcon4,
    url: '/share/category',
  },
  {
    id: 14,
    name: '钓鱼爱好',
    quantity: 15,
    messages: 999,
    icon: svgIcon5,
    url: '/share/category',
  },
  {
    id: 15,
    name: '自驾旅游',
    quantity: 11,
    messages: 9,
    icon: svgIcon6,
    url: '/share/category',
  },
  {
    id: 16,
    name: '唱歌爱好',
    quantity: 15,
    messages: 999,
    icon: svgIcon7,
    url: '/share/category',
  },
  {
    id: 17,
    name: '一起吃饭',
    quantity: 5,
    messages: 99,
    icon: svgIcon8,
    url: '/share/category',
  },
  {
    id: 18,
    name: '加班群',
    quantity: 4,
    messages: 9,
    icon: svgIcon9,
    url: '/share/category',
  },
];

export const friendCreation = [
  {
    id: 1,
    name: 'UI设计',
    quantity: 6,
    messages: 9,
    icon: svgIcon10,
    url: '/share/category',
  },
  {
    id: 2,
    name: '我的家庭',
    quantity: 20,
    messages: 99,
    icon: svgIcon11,
    url: '/share/category',
  },
  {
    id: 3,
    name: '一起拼车',
    quantity: 5,
    messages: 99,
    icon: svgIcon12,
    url: '/share/category',
  },
  {
    id: 4,
    name: '初中同学',
    quantity: 40,
    messages: 999,
    icon: svgIcon13,
    url: '/share/category',
  },
  {
    id: 5,
    name: '户外运动',
    quantity: 11,
    messages: 99,
    icon: svgIcon14,
    url: '/share/category',
  },
  {
    id: 6,
    name: 'UI设计',
    quantity: 6,
    messages: 9,
    icon: svgIcon10,
    url: '/share/category',
  },
  {
    id: 7,
    name: '我的家庭',
    quantity: 20,
    messages: 99,
    icon: svgIcon11,
    url: '/share/category',
  },
  {
    id: 8,
    name: '一起拼车',
    quantity: 5,
    messages: 99,
    icon: svgIcon12,
    url: '/share/category',
  },
  {
    id: 9,
    name: '初中同学',
    quantity: 40,
    messages: 999,
    icon: svgIcon13,
    url: '/share/category',
  },
  {
    id: 10,
    name: '户外运动',
    quantity: 11,
    messages: 99,
    icon: svgIcon14,
    url: '/share/category',
  },
  {
    id: 11,
    name: 'UI设计',
    quantity: 6,
    messages: 9,
    icon: svgIcon10,
    url: '/share/category',
  },
  {
    id: 12,
    name: '我的家庭',
    quantity: 20,
    messages: 99,
    icon: svgIcon11,
    url: '/share/category',
  },
  {
    id: 13,
    name: '一起拼车',
    quantity: 5,
    messages: 99,
    icon: svgIcon12,
    url: '/share/category',
  },
  {
    id: 14,
    name: '初中同学',
    quantity: 40,
    messages: 999,
    icon: svgIcon13,
    url: '/share/category',
  },
  {
    id: 15,
    name: '户外运动',
    quantity: 11,
    messages: 99,
    icon: svgIcon14,
    url: '/share/category',
  },
  {
    id: 16,
    name: 'UI设计',
    quantity: 6,
    messages: 9,
    icon: svgIcon10,
    url: '/share/category',
  },
  {
    id: 17,
    name: '我的家庭',
    quantity: 20,
    messages: 99,
    icon: svgIcon11,
    url: '/share/category',
  },
  {
    id: 18,
    name: '一起拼车',
    quantity: 5,
    messages: 99,
    icon: svgIcon12,
    url: '/share/category',
  },
  {
    id: 19,
    name: '初中同学',
    quantity: 40,
    messages: 999,
    icon: svgIcon13,
    url: '/share/category',
  },
  {
    id: 20,
    name: '户外运动',
    quantity: 11,
    messages: 99,
    icon: svgIcon14,
    url: '/share/category',
  },
];

export const share = [
  {
    id: 1,
    name: '文库大全',
    messages: 99,
    icon: App1,
    shareName: 'library',
  },
  {
    id: 2,
    name: '裕邦邮箱',
    messages: 9,
    icon: App2,
    shareName: 'mailHomePage',
  },
  {
    id: 3,
    name: '魔术相册',
    messages: 9,
    icon: App3,
    shareName: 'magicAlbum',
  },
  {
    id: 4,
    name: '音频播放器',
    messages: 9,
    icon: App4,
    shareName: 'audioPlay',
  },
  {
    id: 5,
    name: '视频播放器',
    messages: 9,
    icon: App5,
    shareName: 'videoPlay',
  },
  {
    id: 6,
    name: '备忘祝福',
    messages: 9,
    icon: App16,
    shareName: 'memoBless',
  },
  {
    id: 7,
    name: '我的日记',
    messages: 9,
    icon: App6,
    shareName: 'diary',
  },
  {
    id: 8,
    name: '通讯录',
    messages: 9,
    icon: App8,
    shareName: 'addressBook',
  },
  {
    id: 9,
    name: '桌面软件',
    messages: 9,
    icon: App12,
    shareName: 'softWare',
  },
  {
    id: 10,
    name: '裕邦编辑',
    messages: 9,
    icon: App14,
    shareName: 'editor',
  },
  {
    id: 11,
    name: '网络世界',
    messages: 59,
    icon: App15,
    shareName: 'networkWorld',
  },
  {
    id: 12,
    name: '桌面文件',
    messages: 59,
    icon: App17,
    shareName: 'desktopFile',
  },
];

export const videos = [
  {
    id: 1,
    name: '裕邦智慧办公平台介绍视频',
    uploadDate: '2024.07.12 08:59',
    icon: jpgIcon2,
  },
  {
    id: 2,
    name: '裕邦智慧办公平台介绍视频',
    uploadDate: '2024.07.12 08:59',
    icon: jpgIcon2,
  },
  {
    id: 3,
    name: '裕邦智慧办公平台介绍视频',
    uploadDate: '2024.07.12 08:59',
    icon: jpgIcon1,
  },
];

export const games = [
  {
    id: 1,
    name: 'DNF - 格斗类竞技',
    describe: '一款超热门的游戏',
    icon: jpgIcon3,
  },
  {
    id: 2,
    name: '逆水寒 - 仙侠网游',
    describe: '会呼吸的江湖',
    icon: jpgIcon5,
  },
  {
    id: 3,
    name: '英雄联盟 - MOBA竞技',
    describe: '一款超热门的游戏',
    icon: jpgIcon4,
  },
];

export const albums = [
  {
    id: 1,
    icon: pngIcon30,
  },
  {
    id: 2,
    icon: pngIcon31,
  },
  {
    id: 3,
    icon: pngIcon32,
  },
  {
    id: 4,
    icon: pngIcon33,
  },
  {
    id: 5,
    icon: pngIcon31,
  },
  {
    id: 6,
    icon: pngIcon32,
  },
];

export const audios = [
  {
    id: 1,
    name: '明天你是否依然爱我',
    author: '谭曼',
    duration: '03:56',
    icon: pngIcon41,
  },
  {
    id: 2,
    name: '前天你是否依然爱我',
    author: '谭曼',
    duration: '03:56',
    icon: pngIcon41,
  },
  {
    id: 3,
    name: '后天你是否依然爱我',
    author: '谭曼',
    duration: '03:56',
    icon: pngIcon41,
  },
  {
    id: 4,
    name: '男人海洋',
    author: '周传雄',
    duration: '03:56',
    icon: pngIcon40,
  },
];

export const sites = [
  {
    id: 1,
    name: '百度',
    url: '/',
    icon: pngIcon50,
  },
  {
    id: 2,
    name: '凤凰网',
    url: '/',
    icon: pngIcon51,
  },
  {
    id: 3,
    name: '新华网',
    url: '/',
    icon: pngIcon52,
  },
  {
    id: 4,
    name: '今日头条',
    url: '/',
    icon: pngIcon53,
  },
  {
    id: 5,
    name: '携程网',
    url: '/',
    icon: pngIcon54,
  },
];
