import useUserStore from '@/store/useUserStore';
import http from '../index';
interface Response {
  code: number;
  data: number;
  msg: string;
}
// 创建会议
export function createMeeting(data: {
  initiatorDeviceType?: string;
  micStatus?: string;
  cameraStatus?: string;
  initiatorDeviceId?: string;
  meetingTitle: string;
  userIds?: string[];
  groupIds?: string[],
  sourceType?:string
}) {
  // "micStatus": "OFF",
  // "cameraStatus": "OFF",
  const clientType = useUserStore.getState().clientType;
  const deviceId = useUserStore.getState().deviceId;
  data.initiatorDeviceId = deviceId;
  data.initiatorDeviceType = clientType;
  data.cameraStatus = 'ON';
  data.micStatus = 'ON';
  return http.post<Response>({
    url: `/web-api/media/meeting`,
    data,
  });
}

// 结束会议
export function endMeeting(data: { id: string }) {
  return http.post<Response>({
    url: `/web-api/media/meeting/end`,
    data,
  });
}
// 结束会议
export function exitMeeting(data: { id: string }) {
  return http.post<Response>({
    url: `/web-api/media/meeting/exit`,
    data,
  });
}
// 同意-拒绝加入视频会议邀请
export function dealInvite(data: {
  meetingId: string;
  userId?: string;
  flg: boolean;
  deviceType?: string;
  deviceId?: string;
  sourceType?: number;
}) {
  const userId = useUserStore.getState().userInfo?.id;
  const clientType = useUserStore.getState().clientType;
  const deviceId = useUserStore.getState().deviceId;
  data.deviceId = deviceId;
  data.deviceType = clientType;
  data.userId = userId;
  return http.post<Response>({
    url: `/web-api/media/meeting/user/invite`,
    data,
  });
}
/**获取会议申请列表 */
export function getMeetingApplyList(meetingId: string) {
  return http.get<Response>({
    url: `/web-api/media/meeting/user/list?meetingId=${meetingId}`,
  });
}
/**获取加入会议列表 */
export function getJoinedMeetingList(params: { meetingId: string; role?: string }) {
  return http.get<Response>({
    url: `/web-api/media/meeting/join/list`,
    params,
  });
}
/**申请画面 */
export function applyScreen(meetingId: string) {
  return http.get<Response>({
    url: `/web-api/media/meeting/applyScreen/${meetingId}`,
  });
}
/**申请画面审核列表 */
export function applyList(meetingId: string) {
  return http.get<Response>({
    url: `/web-api/media/meeting/applies?id=${meetingId}`,
  });
}
//申请画面审核
export function applyScreenApprove(data: {
  meetingId: string;
  applyUserId: string;
  agree: boolean;
}) {
  return http.post<Response>({
    url: `/web-api/media/meeting/applyScreenApprove`,
    data,
  });
}
// 发布 发送信令
export function callpublish(data: { meetingId: string; sdp: string }) {
  return http.post<Response>({
    url: `/web-api/media/meeting/publish `,
    data,
  });
}
// 播放 发送信令
export function callplay(data: { meetingId: string; sdp: string; userId: string }) {
  return http.post<Response>({
    url: `/web-api/media/meeting/play`,
    data,
  });
}
// 开关麦克风
export function switchVoiceOnOff(data: { meetingId: string; command: string; userId?: string }) {
  const userId = useUserStore.getState().userInfo?.id;
  data.userId = userId;
  return http.post<Response>({
    url: `/web-api/media/meeting/voiceOnOff`,
    data,
  });
}
// 开关麦克风
export function ad(data: { meetingId: string; command: string; userId?: string }) {
  return http.post<Response>({
    url: `/web-api/media/meeting/voiceOnOff`,
    data,
  });
}
/**申请画面审核列表 */
export function MeetingDetail(meetingId: string) {
  return http.get<Response>({
    url: `/web-api/media/meeting?id=${meetingId}`,
  });
}
// 添加用户
export function addUser(data: { meetingId: string; userIds: string[] }) {
  return http.post<Response>({
    url: `/web-api/media/meeting/user`,
    data,
  });
}
// 添加用户
export function kickUser(data: { meetingId: string; userId: string }) {
  return http.post<Response>({
    url: `/web-api/media/meeting/kick`,
    data,
  });
}
// 关闭画面
export function offScreen(data: { meetingId: string; userId: string }) {
  return http.post<Response>({
    url: `/web-api/media/meeting/offScreen`,
    data,
  });
}
// 添加画面
export function onScreen(data: { meetingId: string; userId: string }) {
  return http.post<Response>({
    url: `/web-api/media/meeting/onScreen`,
    data,
  });
}
// 同意添加画面
export function agreeShowScreen(meetingId: string) {
  return http.get<Response>({
    url: `/web-api/media/meeting/agreeShowScreen/${meetingId}`,
  });
}
// 不同意添加画面
export function disagreeShowScreen(meetingId: string) {
  return http.get<Response>({
    url: `/web-api/media/meeting/disagreeShowScreen/${meetingId}`,
  });
}
