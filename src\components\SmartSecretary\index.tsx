/**
 * 智慧办公小秘书
 */

import { getWidth, searchBD } from '@/utils/common';
import { SearchOutlined } from '@ant-design/icons';
import { Col, Flex, Input, message, Row } from 'antd';
import classNames from 'classnames';
import { FC, useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.module.less';

import { deepType } from '@/api/mail/chat/mailModels.ts';
import editIcon from '@/assets/images/home/<USER>/edit.png';
import mailIcon from '@/assets/images/home/<USER>/mail.png';
import moreIcon from '@/assets/images/home/<USER>/more.png';
import vidiconIcon from '@/assets/images/home/<USER>/vidicon.png';
import wechatIcon from '@/assets/images/home/<USER>/wechat.png';
import ChatDetail from '@/components/Chat/chatDetail';
import { TenantType, WorkMode } from '@/const';
import { setBrowseLog } from '@/pages/LoginReview/setRecord';
import useIntelligentSecretaryStore from '@/store/useIntelligentSecretaryStore';
import usesmartSecretaryActiveTab from '@/store/usesmartSecretaryActiveTab.ts';
import useUserStore from '@/store/useUserStore';
import { smartSecretaryEmitter } from '@/utils/emitters.ts';
import { debounce } from 'lodash';
import { useLocation, useNavigate } from 'react-router-dom';
import Chat from '../Chat';
import AddressBook from './components/AddressBook';
import Approval from './components/Approval';
import ArticleDetails from './components/ArticleDetails/index.tsx';
import FavoriteWebPage from './components/FavoriteWebPage';
import FavoriteWebsit from './components/FavoriteWebsit';
import FileList from './components/FileList';
import SecretaryLibrary from './components/Library';
import Mail from './components/Mail';
import MoreList from './components/More';
import NotificationFile from './components/NotificationFile/index.tsx';
import TodayNews from './components/TodayNews/index.tsx';
import TodoList from './components/TodoList';
import { Context } from './context';

interface SmartSecretaryPropsType {
  insetHome: boolean;
  mailType?: deepType;
  query?: any;
}
const headTab = {
  default: [
    { label: '待办事项', key: 'todo' },
    { label: '文库大全', key: 'library' },
    { label: '裕邦聊天', key: 'im' },
    { label: '裕邦邮箱', key: 'mail' },
  ],
  shareGroup: [
    { label: '文库大全', key: 'library' },
    { label: '裕邦聊天', key: 'im' },
    { label: '裕邦邮箱', key: 'mail' },
    { label: '通讯录', key: 'addressBook' },
  ],
};
const footerTab = [
  { label: '发起聊天', icon: wechatIcon, key: 'im' },
  { label: '在线编辑', icon: editIcon, key: 'edit' },
  { label: '音视频通话', icon: vidiconIcon, key: 'call' },
  { label: '发邮件', icon: mailIcon, key: 'mail' },
  { label: '更多', icon: moreIcon, key: 'more' },
];
const SmartSecretary: FC<SmartSecretaryPropsType> = (props) => {
  const { insetHome, mailType, query } = props;
  const [tabRowList, setTabRowList] = useState(headTab.default);
  const [inputText, setInputText] = useState('');
  const [activeTab, setActiveTab] = useState('todo');
  const [extraData, setExtraData] = useState({}); // 传递给其他组件的额外属性
  const [openChat, setOpenChat] = useState(false); // 控制聊天窗口显隐
  const inputRef: any = useRef<HTMLInputElement>(null);
  const { userInfo } = useUserStore((state) => state);
  const [currentTab, setCurrentTab] = usesmartSecretaryActiveTab((state) => [
    state.currentTab,
    state.setCurrentTab,
  ]);
  const bdDisabled =
    userInfo?.workMode === WorkMode.FIXED_CLOSED ||
    (userInfo?.workMode === WorkMode.CLOSED && userInfo.tenantType === TenantType.ENTERPRISE);
  const [showMore, setShowMore] = useState(false);
  const [text, setQueryType, setText, queryType, queryData, temporaryData, shareGroupId] =
    useIntelligentSecretaryStore((state) => [
      state.text,
      state.setQueryType,
      state.setText,
      state.queryType,
      state.queryData,
      state.temporaryData,
      state.shareGroupId,
    ]);
  const [articleId, setArticleId] = useState('');
  const [pageType, setPageType] = useState('');
  const location = useLocation();
  const history = useNavigate();
  // 监听带参数跳转来的
  useEffect(() => {
    if (location.state && location.state.activeTab) {
      setCurrentTab({
        txt: '审批事项',
        key: location.state.activeTab,
      });
    }
  }, [location]);

  useEffect(() => {
    if (!mailType) {
      return;
    } else {
      changeActiveTab(headTab.default[3].key);
    }
  }, [mailType]);
  useEffect(() => {
    console.log('queryData', queryData);
    console.log('temporaryData', temporaryData);
  }, [queryType]);
  const searchBaidu = () => {
    if (bdDisabled) {
      message.warning('全封闭模式下不能百度上网');
      return;
    }
    const value = (inputRef.current?.input?.value || '').trim();
    setBrowseLog({
      fileName: `百度上网搜索${value}`,
      fileType: 'other',
      fileSource: 17,
    });
    searchBD(value);
  };

  const changeActiveTab = (key: string, tabItem?: any, articleId?: string, pageType?: string) => {
    setShowMore(false);
    if (tabItem && tabItem.status !== 'fixed') {
      // 非固定栏功能，点击替换掉第四个tab
      setTabRowList([...tabRowList.slice(0, 3), { label: tabItem.txt, key: tabItem.key }]);
    }
    console.log('key', key);
    if (articleId) {
      setArticleId(articleId);
    }
    if (pageType) {
      setPageType(pageType);
    }
    setActiveTab(key);
  };
  const autoSearchConditionResult = useCallback(
    debounce((word?: any) => {
      setText(word);
      setQueryType({ current: 'current' });
    }, 500),
    [],
  );
  const renderCenter = () => {
    switch (activeTab) {
      case 'todo':
        return <TodoList />;
      case 'library':
        return <SecretaryLibrary />;
      case 'im':
        return <Chat showDetail={false} />; // Boolean(shareGroupId)
      case 'mail':
        return <Mail />;
      case 'addressBook':
        return <AddressBook />;
      case 'favoriteWebsit':
        return <FavoriteWebsit />;
      case 'favoriteWebPage':
        return <FavoriteWebPage />;
      case 'screenShot':
        return <FileList source="20" />; //一键截图
      case 'picturesFavorite':
        return <FileList source="22" />; //图片收藏
      case 'webEditDocument':
        return <FileList source="8" />; //网页编辑
      case 'editor':
        return <FileList source="7" />; //裕邦编辑
      case 'audio':
      case 'audioCall':
      case 'videoMeeting':
        return <FileList source="3" />; //音频
      case 'video':
        return <FileList source="4" />; //视频
      case 'diary':
        return <FileList source="5" />; //日记
      case 'magicAlbum':
        return <FileList fileFormatType="library_file_type_pic" />; //相册
      case 'approval':
        return <Approval />; // 审批
      case 'todayNews':
        return <TodayNews changeActiveTab={changeActiveTab} />; //今日新闻
      case 'notificationFile':
        return <NotificationFile changeActiveTab={changeActiveTab} />; //通知文件
      case 'articleDetails':
        return (
          <ArticleDetails
            articleId={articleId}
            pageType={pageType}
            changeActiveTab={changeActiveTab}
          />
        ); //文章详情
      case 'chatWindow':
        return <ChatDetail onClose={() => setOpenChat(false)} targetUser={extraData} />;

      default:
        return <></>;
    }
  };

  const bottomBtnClick = (key: string) => {
    switch (key) {
      case 'im':
        setCurrentTab({
          txt: '通讯录',
          key: 'addressBook',
        });
        break;
      case 'edit':
        history('/editor');
        break;
      case 'call':
        setCurrentTab({
          txt: '通讯录',
          key: 'addressBook',
        });
        break;
      case 'mail':
        setCurrentTab({
          txt: '通讯录',
          key: 'addressBook',
        });
        break;
      case 'more':
        message.warning('更多功能开发中');
        break;
      default:
        break;
    }
  };
  useEffect(() => {
    if (mailType) {
      return;
    }
    if (shareGroupId) {
      setTabRowList(headTab.shareGroup);
      changeActiveTab(headTab.shareGroup[0].key);
    } else {
      setTabRowList(headTab.default);
      changeActiveTab(headTab.default[0].key);
    }
  }, [shareGroupId]);
  useEffect(() => {
    if (currentTab.key) {
      changeActiveTab(currentTab.key, currentTab);
      setTimeout(() => {
        usesmartSecretaryActiveTab.reset();
      });
    }
  }, [currentTab]);
  useEffect(() => {
    // Emitter 提前通知导致消息丢失 废除
    // smartSecretaryEmitter.on('smartSecretaryChangeActiveTab', (tabItem: any) => {
    //   changeActiveTab(tabItem.key, tabItem);
    // });
    smartSecretaryEmitter.on('changePage', (data: any) => {
      setActiveTab(data.pageName);
      setExtraData(data.data);
    });
    return () => {
      useIntelligentSecretaryStore.reset();
    };
  }, []);
  return (
    <Context.Provider
      value={{
        shareGroupId,
        changeActiveTab,
        rootDom: document.querySelector(`.${styles.smartSecretaryContainer}`),
        query: query || null,
        mailType: mailType || { index: 1000 },
      }}
    >
      <div
        id="smartSecretaryRoot"
        className={classNames(styles.smartSecretaryContainer, insetHome ? styles.insetHome : '')}
      >
        <div className={styles.headerBar}></div>
        <div className={styles.searchBar}>
          <Input
            ref={inputRef}
            placeholder="搜索"
            variant="borderless"
            value={inputText}
            allowClear
            prefix={<SearchOutlined />}
            style={{
              background: 'rgb(242,244,248)',
              borderRadius: `${getWidth(46)}px`,
              width: `86%`,
              margin: `0 ${getWidth(14)}px`,
            }}
            onChange={(e) => {
              setInputText(e.target.value);
              autoSearchConditionResult(e.target.value);
            }}
            onPressEnter={() => {
              setInputText('');
              setQueryType({ current: 'all' });
            }}
          />
          <Row className={styles.searchBarBtn}>
            <Col
              span={8}
              onClick={() => {
                setInputText('');
                setQueryType({ current: 'all' });
              }}
            >
              <div className={styles.btn}>全部查询</div>
            </Col>
            <Col
              span={8}
              onClick={() => {
                setQueryType({ current: 'results' });
              }}
            >
              <div className={styles.btn}>结果查询</div>
            </Col>
            <Col span={8}>
              <div className={styles.btn} onClick={searchBaidu}>
                网络搜索
              </div>
            </Col>
          </Row>
        </div>
        <Row className={styles.tabBar}>
          {tabRowList.map((item, index) => (
            <Col span={5} key={index}>
              <div
                className={classNames(styles.tabItem, activeTab === item.key ? styles.active : '')}
                onClick={() => {
                  changeActiveTab(item.key);
                }}
              >
                {item.label}
              </div>
            </Col>
          ))}

          <Col span={4}>
            <div
              className={classNames(styles.tabItem, showMore ? styles.moreBtnActive : undefined)}
              onClick={() => {
                setShowMore(!showMore);
              }}
            >
              更多
            </div>
          </Col>
        </Row>

        {/* 中间区域 */}
        <div className={styles.contentWrap} id="SmartSecretaryInnerContent">
          {renderCenter()}
          {showMore ? (
            <MoreList
              close={() => {
                setShowMore(false);
              }}
            />
          ) : undefined}
        </div>

        {/* 底部tab */}
        <Flex className={styles.footerBar}>
          {footerTab.map((item) => (
            <div
              key={item.key}
              className={styles.footerBtn}
              onClick={() => {
                bottomBtnClick(item.key);
              }}
            >
              <img src={item.icon} alt="" />
              <span>{item.label}</span>
            </div>
          ))}
        </Flex>
      </div>
    </Context.Provider>
  );
};

export default SmartSecretary;
