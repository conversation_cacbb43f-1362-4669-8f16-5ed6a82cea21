import request from '../index';
// 获取备忘祝福简单分页列表
export const getSimplePage = (data: any) => {
  return request.post({
    url: '/admin-api/note/getSimplePage',
    data,
  });
};

// 根据id集合更新阅读状态
export const updateReadStatusByIds = (data: any) => {
  return request.post({
    url: '/web-api/note/blessing/updateReadStatusByIds',
    data,
  });
};

// 获取备忘祝福数据含关键字查询功能
export const getNotePage = (data: any) => {
  return request.post({
    url: '/web-api/note/getNotePage',
    data,
  });
};
