import { addRxPlugin, createRxDatabase, RxCollection } from 'rxdb';
import { RxDBQueryBuilderPlugin } from 'rxdb/plugins/query-builder';
import { getRxStorageDexie } from 'rxdb/plugins/storage-dexie';
import { RxDBUpdatePlugin } from 'rxdb/plugins/update';
import { getSchema } from '../schemas/im-chat-single.schema';

// 添加查询构建器插件
addRxPlugin(RxDBQueryBuilderPlugin);
addRxPlugin(RxDBUpdatePlugin); // 添加更新插件

export interface ImChatSingleData {
  id: string;
  content: string;
  msgType: number;
  extra: string;
  sendUserId: number;
  sendTime: number;
  receiveUserId: number;
  status?: string; // 可选字段，默认为'sending'
}

export class ImChatSingle implements ImChatSingleData {
  id: string;
  content: string;
  msgType: number;
  extra: string;
  sendUserId: number;
  sendTime: number;
  receiveUserId: number;
  status: string;

  constructor(data: Partial<ImChatSingleData>) {
    console.log('ImChatSingle', data);

    this.id = data.id || '-1'; // 或者一个合理的默认值
    this.content = data.content || '';
    this.msgType = data.msgType || 0;
    this.extra = data.extra || '';
    this.sendUserId = data.sendUserId || -1;
    this.sendTime = data.sendTime || Date.now();
    this.receiveUserId = data.receiveUserId || -1;
    this.status = data.status || 'sending';

    // 检查必填字段是否存在，并抛出错误如果缺少
    if (
      this.id === '-1' ||
      this.content === '' ||
      this.msgType === 0 ||
      this.sendUserId === -1 ||
      // this.sendTime === Date.now() ||
      this.receiveUserId === -1
    ) {
      throw new Error('Missing required properties');
    }
  }

  static fromProto(protoMessage: ImChatSingleData): ImChatSingle {
    return new ImChatSingle(protoMessage);
  }

  toProto(): ImChatSingleData {
    return {
      id: this.id,
      content: this.content,
      msgType: this.msgType,
      extra: this.extra,
      sendUserId: this.sendUserId,
      sendTime: this.sendTime,
      receiveUserId: this.receiveUserId,
      status: this.status,
    };
  }
}

export async function initImChatModel(): Promise<{
  chat_messages: RxCollection<ImChatSingleData>;
}> {
  const db = await createRxDatabase({
    name: 'chat_db',
    // adapter: 'idb', // 使用IndexedDB作为适配器
    multiInstance: true,
    storage: getRxStorageDexie(),
  });

  await db.addCollections({
    chat_messages: {
      schema: getSchema(),
    },
  });

  return {
    chat_messages: db.chat_messages,
  };
}
