import { BackendError } from '@/const/error';
import useUserStore from '@/store/useUserStore';
import { message as Message } from 'antd';
import axios, { AxiosError, AxiosRequestConfig, CancelTokenSource } from 'axios';

const axiosInstance = axios.create({
  baseURL: '/',
  headers: { 'Content-Type': 'application/json;charset=utf-8' },
});

// 存储最近请求的时间戳和请求源
const requestCache: Record<string, number> = {};
const requestSource: Record<string, CancelTokenSource> = {};
const REPEAT_ERR = '请求太频繁，请稍后重试';

axiosInstance.interceptors.request.use(
  (config) => {
    const { accessToken, clientType, deviceId, deviceType, appVersion, tenantId } =
      useUserStore.getState();
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    config.headers['Tenant-Id'] = tenantId; // 租户id
    // if (clientType) {
    config.headers['Client-Id'] = clientType || 'win_desktop'; // 终端编号  win_desktop、win_notebook
    config.headers['Device-Id'] = deviceId || 'yb168'; // 设备编号
    config.headers['Device-Type'] = deviceType ?? 'win_desktop'; // 登录设备类型(电脑型号、笔记本)
    config.headers['App-Version'] = encodeURIComponent(appVersion || '浏览器网页版'); // 版本信息
    // }
    if (!config.url?.includes('restapi')) {
      config.url = '/api' + config.url;
    }

    if (!config.headers.ignoreDebounce) {
      const requestKey = `${config.method}:${config.url}:${JSON.stringify(config.method === 'get' ? config.params : config.data)}`;
      // 检查请求时间
      const lastRequestTime = requestCache[requestKey];
      const currentTime = Date.now();

      if (lastRequestTime && currentTime - lastRequestTime < 1500) {
        if (requestSource[requestKey]) {
          requestSource[requestKey].cancel('新请求被发送，取消旧请求');
        }
        delete requestCache[requestKey];
        delete requestSource[requestKey];
      }
      // 更新请求时间和取消源
      requestCache[requestKey] = currentTime;
      requestSource[requestKey] = axios.CancelToken.source();
      config.cancelToken = requestSource[requestKey].token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

axiosInstance.interceptors.response.use(
  (res) => {
    if (!res.data) {
      throw new Error('sys.api.apiRequestFailed');
    }
    if (res.status === 200 && (res.data instanceof Blob || typeof res.data === 'string')) {
      return res.data;
    }
    if (res.data.code === 0) {
      return res.data;
    }
    if (res.data.code === 401) {
      window.location.href = '/#/login';
    }
    return Promise.reject(res.data);
  },
  (error) => {
    return Promise.reject(error);
  },
);

export interface Extra {
  errorHandling?: 'manual';
  setLoading?: (value: boolean) => void;
  ignoreDebounce?: boolean; // 忽略请求防抖
}

class APIClient {
  get<T>(config: AxiosRequestConfig, extra?: Extra) {
    return this.request<T>({ ...config, method: 'GET' }, extra);
  }

  post<T>(config: AxiosRequestConfig, extra?: Extra) {
    return this.request<T>({ ...config, method: 'POST' }, extra);
  }

  put<T>(config: AxiosRequestConfig, extra?: Extra) {
    return this.request<T>({ ...config, method: 'PUT' }, extra);
  }

  delete<T>(config: AxiosRequestConfig, extra?: Extra) {
    return this.request<T>({ ...config, method: 'DELETE' }, extra);
  }

  request<T>(config: AxiosRequestConfig, extra?: Extra) {
    return new Promise<T>((resolve, reject) => {
      const { ignoreDebounce, errorHandling, setLoading } = extra || {};
      if (setLoading) {
        setLoading(true);
      }
      // 忽略请求防抖
      if (ignoreDebounce) {
        config.headers = config.headers || {};
        config.headers.ignoreDebounce = ignoreDebounce;
      }
      axiosInstance
        .request<T>(config)
        .then((res) => {
          resolve(res as T);
        })
        .catch((error: Error | AxiosError | any) => {
          if (errorHandling !== 'manual') {
            const { response, message, msg } = error;
            const errMsg = response?.data?.message || message || msg || 'sys.api.errorMessage';
            const ignoreCode = [1002000000, BackendError.REPEAT, AxiosError.ERR_CANCELED];
            // 部分报错不需要重复提示，如登录报错
            // 配置Nomsg，有单独的错误提示，不重复
            if (
              !ignoreCode.includes(error.code) &&
              !config?.headers?.Notip &&
              errMsg !== REPEAT_ERR
            ) {
              Message.error(errMsg);
            }
            if (error.code === 401) {
              window.location.href = '/#/login';
            }
          }
          reject(error);
        })
        .finally(() => {
          if (setLoading) {
            setLoading(false);
          }
        });
    });
  }
}

export default new APIClient();
