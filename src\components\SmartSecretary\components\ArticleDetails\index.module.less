.antDrawerHeader {
  padding: 0 !important;
  margin: 0 !important;
}

.antDrawer<PERSON>ooter {
  padding: 8px 0 !important;
  margin: 0 !important;
  background: linear-gradient(to bottom, #ECECEC, white) !important;
}

.headerContainer {
  height: 100%;
  width: 100%;
  background: #FFFFFF;
  .headerBox {
    height: 57px;
    width: 100%;
    font-size: 20px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0;
    color: rgba(0, 0, 0, 0.95);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    margin-top: 23px;

    .title {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 317px;
    }

    .button {
      border: 1px solid #3D5AFE;
      width: 36px;
      height: 33px;
      font-size: 12px;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0;
      color: #3D5AFE;
    }
  }

  .content {
    overflow: auto;
    box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.16);
    background: #FFFFFF;
    padding: 0 12px;
  }

  .footerBox {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: PingFang SC, serif;
    font-size: 16px;
    font-weight: normal;
    line-height: 24px;
    text-align: center;
    letter-spacing: 0;
    font-optical-sizing: auto;

    .footerButtonBox {
      width: 215.5px;
      height: 44px;
      gap: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #FFFFFF;
      box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.16);
      border-radius: 4px;
      cursor: pointer;
      color: rgba(0, 0, 0, 0.2);
    }

    .footerButtonBox:hover {
      //color: rgba(0, 0, 0, 0.85);
    }
  }
}