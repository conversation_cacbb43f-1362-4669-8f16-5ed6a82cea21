import { checkWebpage, getWebpageData } from '@/api/networld';
import useAppStore from '@/store/useAppStore';
import useJavaWebSocketStore from '@/store/useJavaWebSocketStore';
import { getPublicImg, getWidth } from '@/utils/common';
import { Button, Image, List } from 'antd';
import { FC, useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import styles from './index.module.less';
const FavoriteWebPage: FC<any> = ({ urlType, dataList }) => {
  const { channel } = useAppStore((state: any) => state);
  const [hasMore, setHasMore] = useState(true);
  const [pageNo, setPageNo] = useState(1);
  const [renderList, setRenderList] = useState([]);
  const listRef = useRef<HTMLDivElement>(null);
  const [response] = useJavaWebSocketStore((state) => [state.response]);

  const handleClick = (item: any) => {
    let url = '';
    if (urlType === 'websiteUrl') {
      url = item?.websiteUrl.trim() || '';
    } else {
      url = item?.webpageUrl.trim() || '';
    }
    if (!url) return;
    window.open(url.includes('//') ? url : '//' + url);
    const param = {
      webpageId: item.id || '',
    };
    checkWebpage(param).then((res: any) => {});
  };
  const fetchList = async (page: number) => {
    if (dataList) {
      return;
    }
    getWebpageData({
      pageNo: pageNo,
      pageSize: 1000,
    }).then((res: any) => {
      setRenderList(res.data.list);
      scrollToTop();
      // setTotal(res.data.total);
    });
  };
  useEffect(() => {
    if (dataList) {
      setRenderList(dataList);
      scrollToTop();
    }
  }, [dataList]);

  useEffect(() => {
    fetchList(pageNo);
  }, [pageNo]);

  useEffect(() => {
    // 收到推送
    if (response.noticeType === 'NET_WORLD') {
      setPageNo(1);
      fetchList(1).then(() => {});
    }
  }, [response]);

  const scrollToTop = () => {
    if (listRef.current) {
      listRef.current.scrollTop = 0;
    }
  };

  return (
    <div className={styles.todoListContainer}>
      <div className={styles.todoList}>
        <div
          id="scrollWebDiv"
          className={styles.scrollableDiv}
          style={{ height: window.innerHeight - getWidth(channel === 'web' ? 405 : 340) }}
          ref={listRef}
        >
          <InfiniteScroll
            dataLength={renderList.length} // 已加载的数据长度
            next={() => fetchList(pageNo + 1)} // 加载更多数据的函数
            hasMore={hasMore} // 是否还有更多数据
            loader={false}
            // loader={<div>加载中...</div>} // 加载中的提示信息
            // endMessage={<Divider plain>It is all, nothing more 🤐</Divider>}
            scrollableTarget="scrollWebDiv"
          >
            <List
              className={styles.todoList}
              itemLayout="horizontal"
              dataSource={renderList}
              // loadMore={loadMore}
              renderItem={(item: any) => (
                <List.Item className={styles.listContentItem}>
                  <List.Item.Meta
                    avatar={
                      <div className={styles.leftContent}>
                        <Image
                          src={getPublicImg(item.iconUrl)}
                          preview={false}
                          className={styles.webIcon}
                        ></Image>
                      </div>
                    }
                    title={
                      <div className={styles.titleLine}>
                        <div className={styles.title}>{item.title || item.contentTitle}</div>
                        <div className={styles.time}>
                          <Button
                            className={styles.btnPrimary}
                            onClick={() => {
                              handleClick(item);
                            }}
                            type="primary"
                            size="small"
                          >
                            浏览
                          </Button>
                        </div>
                      </div>
                    }
                    description={
                      <div className={styles.contentLine}>
                        <div className={styles.content}>
                          <div className={styles.contentText}>
                            {item.websiteUrl || item.content}
                          </div>
                        </div>
                        <div>{item.createTime}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </InfiniteScroll>
        </div>
      </div>
    </div>
  );
};
export default FavoriteWebPage;
