import MultiModal from '@/components/MultiModal';
import { Button, Space } from 'antd';
import { forwardRef, useContext, useImperativeHandle, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Context from './Context';
import styles from './index.module.less';

interface Props {
  onCancel?: () => void;
  onOk?: (next: () => void) => void;
}

const Component = forwardRef(({ onCancel, onOk }: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (value?: any) => {
      setOpen(true);
      if (value) {
        setRangeValue({ ...value });
      }
    },
  }));
  const navigate = useNavigate();
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [selectedFileList] = useMainPanelCtxStore!((state) => [state.selectedFileList]);
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [rangeValue, setRangeValue] = useState({
    startTime: '',
    endTime: '',
  });
  const moduleText = useMemo(() => {
    if (config?.module === 'library') {
      return '文库大全';
    }
    if (config?.module === 'audioPlay') {
      return '音频播放器';
    }
    if (config?.module === 'videoPlay') {
      return '视频播放器';
    }
  }, [config]);
  const rangeValueText = useMemo(() => {
    const { startTime, endTime } = rangeValue;
    if (startTime && endTime) {
      return `${startTime}到${endTime}之间的`;
    } else if (startTime) {
      return `${startTime}之后的`;
    } else if (endTime) {
      return `${endTime}之前的`;
    }
  }, [rangeValue]);
  const goRecycle = () => {
    navigate('/recycle');
  };
  const cancel = () => {
    setOpen(false);
    if (onCancel) {
      onCancel();
    }
  };
  const ok = () => {
    if (onOk) {
      setLoading(true);
      onOk(() => {
        setLoading(false);
        setOpen(false);
      });
    } else {
      setOpen(false);
    }
  };
  const content: any = useMemo(() => {
    switch (config!.removeType) {
      case 'dataBank-select-remove':
        return (
          <div className={styles.removeModal}>
            确认后，已选择的 <strong>{selectedFileList.length}</strong> 个文件将放入{' '}
            <span onClick={goRecycle}>回收站</span>
            ！你可以在回收站 <strong>取消删除</strong> 或者 <strong>永久删除</strong>。
          </div>
        );
      case 'dataBank-all-remove':
        return (
          <div className={styles.removeModal}>
            确认后，{moduleText}的 <strong>全部数据</strong> 将放入{' '}
            <span onClick={goRecycle}>回收站</span>！你可以在回收站 <strong>取消删除</strong> 或者{' '}
            <strong>永久删除</strong>。
          </div>
        );
      case 'dataBank-range-remove':
        return (
          <div className={styles.removeModal}>
            确认后，{moduleText}的 <strong>{rangeValueText}全部数据</strong> 将放入{' '}
            <span onClick={goRecycle}>回收站</span>！你可以在回收站 <strong>取消删除</strong> 或者{' '}
            <strong>永久删除</strong>。
          </div>
        );
      case 'recycle-select-remove':
        return (
          <div className={styles.removeModal}>
            确认后，已选择的 <strong>{selectedFileList.length}</strong> 个文件将
            <strong>永久删除</strong>！请谨慎选择！
          </div>
        );
      case 'recycle-all-remove':
        return (
          <div className={styles.removeModal}>
            确认后，<strong>全部文件</strong>将<strong>永久删除</strong>！请谨慎选择！
          </div>
        );
      case 'recycle-range-remove':
        return (
          <div className={styles.removeModal}>
            确认后，<strong>{rangeValueText}全部文件</strong>将<strong>永久删除</strong>
            ！请谨慎选择！
          </div>
        );
      case 'recycle-select-cancel':
        return (
          <div className={styles.removeModal}>
            确认后，已选择的 <strong>{selectedFileList.length}</strong> 个文件将移回原来的位置。
          </div>
        );
      case 'recycle-all-cancel':
        return (
          <div className={styles.removeModal}>
            确认后，<strong>全部文件</strong>将全部移回原来的位置。
          </div>
        );
      case 'recycle-range-cancel':
        return (
          <div className={styles.removeModal}>
            确认后，<strong>{rangeValueText}全部文件</strong>将全部移回原来的位置。
          </div>
        );
      default:
        return null;
    }
  }, [config, selectedFileList, rangeValueText]);

  return (
    <MultiModal
      layoutGroup={`remove_${config?.module}_${config?.removeType}`}
      layoutClassName="normal"
      destroyOnClose={true}
      top={185}
      width={580}
      title="确认删除吗？"
      open={open}
      onCancel={cancel}
      footer={
        <div className={styles.selectLocationModalFooter}>
          <Space>
            <Button type="primary" loading={loading} onClick={ok}>
              {config!.buttonText.replace('选择', '确认')}
            </Button>
            <Button type="primary" ghost onClick={cancel}>
              取消
            </Button>
          </Space>
        </div>
      }
    >
      {content}
    </MultiModal>
  );
});

export default Component;
