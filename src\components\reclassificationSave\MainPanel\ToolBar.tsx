import { Button, Space } from 'antd';
import { useContext, useMemo } from 'react';
import Context from '../Context';
import styles from './index.module.less';

interface Props {
  onClick?: (type: string) => void;
}

const Component = ({ onClick }: Props) => {
  const { useMainPanelCtxStore } = useContext(Context);
  const [loadingStatus] = useMainPanelCtxStore!((state) => [state.loadingStatus]);
  const btnEnabled = useMemo(() => {
    return loadingStatus === 'init';
  }, [loadingStatus]);

  return (
    <div className={styles.toolBar}>
      <Space>
        <Button type="primary" disabled={!btnEnabled} onClick={() => onClick!('selectFiles')}>
          选择重新分类保存文件
        </Button>
        <span className={styles.text}></span>
      </Space>
    </div>
  );
};

export default Component;
