import { sendSingleMsg } from '@/api/chat';
import { RxCollection } from 'rxdb';
import { ImChatSingle, initImChatModel } from '../model/im-chat-single.model';

import { ImChatSingleData } from '../types';

class ChatDataManager {
  private dbPromise: Promise<{ chat_messages: RxCollection<ImChatSingleData> }>;
  private messageChangeHandler?: (messages: ImChatSingle[]) => void;

  constructor() {
    this.dbPromise = initImChatModel();
    this.setupMessageChangeListener();
  }

  private async setupMessageChangeListener() {
    const { chat_messages } = await this.dbPromise;
    1;
    // 设置实时查询以监听所有消息的变化
    chat_messages.find().$.subscribe((docs: any) => {
      const messages = docs.map((doc: any) => new ImChatSingle(doc));
      if (this.messageChangeHandler) {
        this.messageChangeHandler(messages);
      }
    });
  }

  public setMessageChangeHandler(handler: (messages: ImChatSingle[]) => void) {
    this.messageChangeHandler = handler;
  }

  async sendMessage(msgData: {
    content: string;
    msgType: number;
    extra: string;
    sendUserId: number;
    receiveUserId: number;
  }): Promise<void> {
    const { chat_messages } = await this.dbPromise;
    const now = Date.now();

    // 创建一个新的消息实例，并设置初始状态为“发送中”
    const messageData: Partial<ImChatSingleData> = {
      id: generateId(),
      ...msgData,
      sendTime: now,
      status: 'sending', // 设置初始状态
    };

    const message = new ImChatSingle(messageData);

    // 将新消息插入到本地数据库中
    await chat_messages.insert(message.toProto());

    try {
      // 发送HTTP请求给后端
      console.log('发送HTTP请求给后端-message', message);

      const result = await sendSingleMsg(message);

      // 更新消息状态为“发送成功”
      await chat_messages.findOne({ selector: { id: message.id } }).update({
        $set: { status: 'sent', time: result.data },
      });
    } catch (error) {
      console.error('发送失败:', error);
      // 更新消息状态为“发送失败”
      await chat_messages.findOne({ selector: { id: message.id } }).update({
        $set: { status: 'failed' },
      });
    }
  }
}

function generateId(): string {
  // 实现一个生成唯一ID的方法，这里简单地使用时间戳加随机数
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

export default new ChatDataManager();
