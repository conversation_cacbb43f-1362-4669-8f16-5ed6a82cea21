import employee from '@/assets/images/home/<USER>/employee.png';
import sjyh from '@/assets/images/home/<USER>/sjyh.png';
import spbfq from '@/assets/images/home/<USER>/spbfq.png';
import wkdq from '@/assets/images/home/<USER>/wkdq.png';
import ypbfq from '@/assets/images/home/<USER>/ypbfq.png';
import zmwj from '@/assets/images/home/<USER>/zmwj.png';
import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create } from '@/store';
import { cppEmitter } from '@/store/useCppBridgeStore';

export interface LoadingItem {
  dataBankFlag: string;
  module:
    | ''
    | 'library'
    | 'audioPlay'
    | 'videoPlay'
    | 'desktopFile'
    | 'dataBank'
    | 'contextMenu'
    | 'employee'
    | 'desktopSoftware'
    | 'preview';
  type:
    | ''
    | 'upload'
    | 'shareUpload'
    | 'download'
    | 'shareDownload'
    | 'downloadAll'
    | 'DesktopFileReclassification'
    | 'desktopSoftwareDownloadInstall'
    | 'DesktopFileUploadShare';
  moduleText?: string;
  typeText?: string;
  icon?: string;
  status?: string;
  itemVisible?: boolean;
  modalVisible?: boolean;
  removed?: boolean;
  fileList?: any[];
}
interface State {
  currentLoadingItem: LoadingItem;
  loadingList: LoadingItem[];
  removedLoadingItem: LoadingItem;
}
interface SetState {
  setCurrentLoadingItem: Setter;
  setLoadingList: Setter;
  setRemovedLoadingItem: Setter;
}

const getTypeText = (type: string) => {
  switch (type) {
    case 'upload':
      return '一键上传';
    case 'shareUpload':
      return '共享群一键上传';
    case 'download':
      return '一键下载';
    case 'shareDownload':
      return '共享群一键下载';
    case 'downloadAll':
      return '一键全部下载';
    case 'DesktopFileUploadShare':
      return '桌面文件分享';
    case 'desktopSoftwareDownloadInstall':
      return '桌面软件下载安装';
    default:
      return '';
  }
};

const useCtxStore = autoUseShallow<State, SetState>(
  create(
    autoCreateSetters<State>((set, get, api): any => {
      return {
        currentLoadingItem: {
          module: '',
          type: '',
        },
        loadingList: [],
        removedLoadingItem: {
          module: '',
          type: '',
        },
        setRemovedLoadingItem: (removedConifg: LoadingItem) => {
          const { loadingList, setLoadingList } = get();
          let removedItem = null;
          const list = loadingList.map((item: any) => {
            if (item.module === removedConifg.module && item.type === removedConifg.type) {
              removedItem = item;
              return {
                ...item,
                modalVisible: false,
                removed: true,
              };
            } else {
              return {
                ...item,
              };
            }
          });
          if (removedItem === null) {
            set({
              removedLoadingItem: { ...removedConifg },
            });
          } else {
            setLoadingList(list);
            setTimeout(() => {
              setLoadingList((prev: any) => {
                return [
                  ...prev.filter((item: any) => {
                    if (item.module === removedConifg.module && item.type === removedConifg.type) {
                      set({
                        removedLoadingItem: { ...item },
                      });
                      return false;
                    } else {
                      return true;
                    }
                  }),
                ];
              });
            }, 600);
          }
        },
      };
    }),
  ),
);

useCtxStore.subscribe((state, prev) => {
  if (state.currentLoadingItem !== prev.currentLoadingItem) {
    let has = false;
    const list = state.loadingList.map((item) => {
      if (
        item.module === state.currentLoadingItem.module &&
        item.type === state.currentLoadingItem.type
      ) {
        has = true;
        return {
          ...item,
          ...state.currentLoadingItem,
        };
      } else {
        return {
          ...item,
          modalVisible: false,
        };
      }
    });
    if (has) {
      state.setLoadingList([...list]);
    } else {
      const item = { ...state.currentLoadingItem };
      item.typeText = getTypeText(item.type);
      item.status = '...';
      switch (item.module) {
        case 'library':
          item.icon = wkdq;
          item.moduleText = '文库大全';
          break;
        case 'audioPlay':
          item.icon = ypbfq;
          item.moduleText = '音频播放器';
          break;
        case 'videoPlay':
          item.icon = spbfq;
          item.moduleText = '视频播放器';
          break;
        case 'desktopFile':
          item.icon = zmwj;
          item.moduleText = '桌面文件';
          break;
        case 'dataBank':
          item.icon = sjyh;
          item.moduleText = '数据银行';
          break;
        case 'contextMenu':
          item.icon = wkdq;
          item.moduleText = '右键菜单';
          break;
        case 'preview':
          item.icon = wkdq;
          item.moduleText = '文件预览';
          break;
        case 'employee':
          item.icon = employee;
          item.moduleText = '人事管理';
          break;
        default:
          item.icon = '';
          item.moduleText = '';
      }
      state.setLoadingList([item, ...list]);
    }
  }
});

cppEmitter.on('oneclickupload', () => {
  const { setCurrentLoadingItem } = useCtxStore.getState();
  setCurrentLoadingItem({
    module: 'contextMenu',
    type: 'upload',
    modalVisible: true,
    itemVisible: false,
  });
});

cppEmitter.on('oneclickdownload', () => {
  const { setCurrentLoadingItem } = useCtxStore.getState();
  setCurrentLoadingItem({
    module: 'contextMenu',
    type: 'download',
    modalVisible: true,
    itemVisible: false,
  });
});

export default useCtxStore;
