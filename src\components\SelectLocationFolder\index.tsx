import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import CreateFolder from './CreateFolder';
import SelectLocation from './SelectLocation';
import useCtxStore from './useCtxStore';

interface Props {
  onClose?: () => void;
  onMinimize?: () => void;
  onConfirm: (path: string) => void;
  title: string;
  config?: any;
  isCustom?: boolean;
}
const Component = forwardRef(({ title, config, onConfirm, onClose, isCustom }: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setLocationOpen(true);
    },
  }));
  const [location] = useCtxStore((state) => [state.location]);
  const selectLocationRef = useRef<{ getCreatedFolder: () => void }>();
  const [locationOpen, setLocationOpen] = useState(isCustom);
  const [folderOpen, setFolderOpen] = useState(false);
  useEffect(() => {
    return () => {
      useCtxStore.reset();
    };
  }, []);

  const createFolderOk = () => {
    setFolderOpen(false);
    selectLocationRef.current?.getCreatedFolder();
  };
  const Confirm = (isSave?: boolean) => {
    onConfirm(isSave ? location : '');
    if (!isCustom) {
      setLocationOpen(false);
    }
  };
  return (
    <>
      {locationOpen && (
        <SelectLocation
          title={title}
          ref={selectLocationRef}
          config={config}
          onCancel={() => {
            if (onClose) {
              onClose();
            }
            setLocationOpen(false);
          }}
          isCustom={isCustom}
          onOk={Confirm}
          onCreateFolder={() => setFolderOpen(true)}
        />
      )}
      {folderOpen && <CreateFolder onCancel={() => setFolderOpen(false)} onOk={createFolderOk} />}
    </>
  );
});

export default Component;
