.detail-container {
  position: relative;
  top: 0;
  height: calc(100vh - 400px);
  overflow-y: auto;
  .detailContent {
    position: relative;
    .content-right {
      height: 100%;
      .right-item {
        position: relative;
        height: 130px;
        display: flex;
        padding-top: 10px;
        border-width: 1px 0px 1px 0px;
        border-style: solid;
        border-color: rgba(0, 0, 0, 0.05);
        box-sizing: content-box;
        .item-avatar {
          width: 67px;
          height: 67px;
          margin: 0px 16px auto 20px;
          // img {
          //   width: 52px;
          //   height: 52px;
          //   border-radius: 8px;
          // }
        }
        .item-info {
          .options {
            width: 100%;
            button {
              width: 62px;
            }
          }
          .info-phone {
            position: absolute;
            right: 16px;
            top: 12px;
            background: #f0f5ff;
            border-radius: 4px;
            padding: 0px 5px;
            color: #3d5afe !important;
          }
          .info-name {
            color: #000;
            font-weight: 600;
            display: inline-block;
            width: 160px;
            height: 24px;
            font-size: 18px;
            // margin-bottom: 1px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            .name {
              max-width: 105px;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .infoGroupName {
            width: 250px;
            display: flex;
          }
          .blackTag {
            display: inline-block;
            position: relative;
            top: -12px;
            margin-left: 6px;
            font-size: 12px;
            height: 17px;
            padding: 0 4px;
            color: #e57373;
            background: rgba(255, 138, 128, 0.4);
            border-radius: 4px;
          }
          .outside {
            display: inline-block;
            position: relative;
            top: -12px;
            margin-left: 6px;
            font-size: 12px;
            height: 17px;
            padding: 0 4px;
            color: #fff;
            background: #66bb6a;
            border-radius: 4px;
          }
          .info-desc {
            font-family: DingTalk JinBuTi;
            font-size: 14px;
            letter-spacing: 0px;
            font-variation-seetings: 'opsz' auto;
            font-feature-seetings: 'kern' on;
            color: rgba(0, 0, 0, 0.65);
            .emailArea {
              display: inline-block;
              width: 230px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              text-align: left;
            }
          }
          // .operateArea {
          //   display: flex;
          //   Button {
          //     width: 100%;
          //     border: 1px solid #3d5afe;
          //     color: #3d5afe;
          //   }
          // }
        }
        .myBtn {
          color: #3d5afe;
          border: 1px solid #3d5afe;
          border-radius: 5px;
          padding: 4px 12px;
        }
      }
      .right-item:first-child {
        margin-top: 0;
      }
      .noDataWrap {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        font-size: 12px;
        line-height: 18px;
        .noDataText {
          color: rgba(0, 0, 0, 0.65);
        }
        .tip {
          color: rgba(0, 0, 0, 0.2);
        }
      }
    }
  }
  .detail-footer {
    position: absolute;
    bottom: 16px;
    left: 200px;
    line-height: 40px;
    .pre-page {
      background: url('@/assets/images/diary/pre_page.png') no-repeat;
      background-size: 100% auto;
      width: 100px;
      height: 40px;
      text-align: center;
      color: #0447bd;
    }
    .pre-num {
      width: 14px;
      margin-left: 120px;
      margin-right: 190px;
      color: #91acc7;
      font-size: 12px;
    }
    .next-num {
      width: 14px;
      margin-right: 153px;
      margin-left: 190px;
      color: #91acc7;
      font-size: 12px;
    }
    .next-page {
      background: url('@/assets/images/diary/next_page.png') no-repeat;
      background-size: 100% auto;
      width: 100px;
      height: 40px;
      text-align: center;
      color: #0447bd;
    }
  }
  .pen {
    width: 536px;
    height: 373px;
    position: absolute;
    background: url('@/assets/images/diary/pen.png') no-repeat;
    background-size: cover;
    bottom: -70px;
    right: -370px;
  }
}
