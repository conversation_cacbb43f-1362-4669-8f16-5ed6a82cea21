import type { Extra } from '../index';
import request from '../index';
import { ShareGroupParams, ShareUserParams } from './typing';

// 创建通讯录联系人
export const createApi = (data: any, noTip?: boolean) => {
  return request.post({
    url: '/web-api/contacts/contact/person/create',
    headers: {
      Notip: noTip,
    },
    data,
  });
};
// 删除通讯录联系人(废)
export const deleteApi = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/deleteByIds',
    data,
  });
};
// 删除所有数据(废)
export const deleteAll = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/deleteAll',
    data,
  });
};
// 删除-取消所有数据
export const deleteOrCancelAll = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/deleteOrCancelAll',
    data,
  });
};
// 根据主键id集合操作删除-取消
export const deleteOrCancelByIds = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/deleteOrCancelByIds',
    data,
  });
};

// 获得通讯录联系人分页
export const getDataByPage = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/person/page',
    data,
  });
};
// 获取分享列表
export const getSharePage = (data: any) => {
  return request.get({
    url: '/web-api/contacts/contact/person/pageShare',
    data,
  });
};
//黑名单总人数
export const selectBlackCount = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/person/blackCount',
    data,
  });
};
// 填写通讯录时的联想查询
export const getDataBySearch = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/person/search',
    data,
  });
};
// 通讯录联系人结果查询分页
export const getDataByResult = (data: any, extra?: Extra) => {
  return request.get(
    {
      url: '/web-api/contacts/contact/person/pageResult',
      params: data,
    },
    { ...extra, ignoreDebounce: true },
  );
};
// 获得通讯录联系人-分享联系人
export const shareApi = (data: any) => {
  return request.get({
    url: '/web-api/contacts/contact/person/share',
    params: data,
  });
};
// 更新通讯录联系人
export const updateInfo = (data: any, noTip?: boolean) => {
  return request.put({
    url: '/web-api/contacts/contact/person/update',
    headers: {
      Notip: noTip,
    },
    data,
  });
};

// 创建通讯录联系人群
export const createGroupApi = (data: any, noTip?: boolean) => {
  return request.post({
    url: '/web-api/contacts/contact/group/create',
    headers: {
      Notip: noTip,
    },
    data,
  });
};
// 获得通讯录联系人群分页
export const queryGroupByPage = (data: any, extra?: Extra) => {
  return request.post(
    {
      url: '/web-api/contacts/contact/group/page',
      data,
    },
    { ...extra, ignoreDebounce: true },
  );
};
/**
 * 获得分享联系人群的列表
 * @param data
 * @returns
 */
export const queryShareGroupByPage = (params: any) => {
  return request.get({
    url: '/web-api/contacts/contact/group/pageShare',
    params,
  });
};

// 更新通讯录联系人群
export const updateGroupInfo = (data: any, noTip?: boolean) => {
  return request.put({
    url: '/web-api/contacts/contact/group/update',
    headers: {
      Notip: noTip,
    },
    data,
  });
};
// 退出群
export const exitGroup = (data: any) => {
  return request.put({
    url: '/web-api/contacts/contact/group/exit',
    data,
  });
};

// 通讯录列表黑名单管理
export const setBlackList = (data: any) => {
  return request.put({
    url: '/web-api/contacts/contact/person/blackAction',
    data,
  });
};
// 获得通讯录群成员分页
export const getGroupMember = (data: any) => {
  return request.get({
    url: '/web-api/contacts/contact/groupMember/page',
    params: data,
  });
};
// 根据群id获得通讯录群成员信息
export const listByGroupId = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/groupMember/listByGroupId',
    data,
  });
};

/**
 * 分享群
 * @param data ShareGroupParams
 * @returns
 */
export const shareGroup = (data: ShareGroupParams) => {
  return request.post({
    url: '/web-api/contacts/contact/group/share',
    data,
  });
};

/**
 * 分享用户
 * @param data ShareUserParams
 * @returns
 */
export const shareUser = (data: ShareUserParams) => {
  return request.post({
    url: '/web-api/contacts/contact/person/share',
    data,
    headers: {
      Notip: true,
    },
  });
};
//查询分享群记录
export const selectShareGroupRecord = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/group/shareGroupRecord',
    data,
  });
};

// 通讯录信息列表分页
export const queryAllData = (data: any, extra?: Extra) => {
  return request.post(
    {
      url: '/web-api/contacts/contact/info/heatRankPage',
      // url: '/web-api/contacts/contact/info/page',
      data,
    },
    extra,
  );
};
