.smartSecretaryContainer {
  width: 439px;
  height: 100%;
  background-color: #fff;
  position: relative;
  overflow-x: hidden;
  &.insetHome {
    margin-top: 20px;
    height: calc(100% - 20px);
  }
  .headerBar {
    width: 100%;
    height: 46px;
    background-image: url('@/assets/images/home/<USER>/header.png');
    background-repeat: no-repeat;
    background-size: cover;
  }
  .searchBar {
    background: rgb(5, 48, 117);
    display: flex;
    align-items: center;
    height: 60px;
    .searchBarBtn {
      color: #fff;
      font-size: 14px;
      .btn {
        cursor: pointer;
      }
    }
  }
  .tabBar {
    height: 60px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    .tabItem {
      text-align: center;
      color: rgba(0, 0, 0, 0.65);
      line-height: 60px;
      cursor: pointer;
      user-select: none;
      &.active {
        color: #3d5afe;
        &::after {
          content: ' ';
          position: absolute;
          bottom: 0;
          width: 24px;
          height: 3.5px;
          border-radius: 3.5px;
          background: #3d5afe;
          left: calc(50% - 12px);
        }
      }
      &.moreBtnActive {
        color: #3d5afe;
        font-weight: 600;
      }
    }
  }
  .contentWrap {
    //
  }
  .footerBar {
    position: absolute;
    z-index: 10;
    bottom: 0;
    width: 100%;
    height: 70px;
    align-items: center;
    background: #ffffff;
    box-shadow: inset 0px 1.17px 0px 0px rgba(0, 0, 0, 0.05);
    .footerBtn {
      flex: 1;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      img {
        width: 28px;
        height: 28px;
      }
    }
  }
}
