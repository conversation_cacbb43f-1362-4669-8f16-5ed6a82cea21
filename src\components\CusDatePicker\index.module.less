.headerStyle {
  display: none !important;
}

.body {
  border-radius: 12px 12px 0 0;

  .customHeaderBox {
    width: 100%;
    height: 64px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;

    .closeButton {
      width: 34px;
      height: 32px;
      font-size: 12px;
      font-weight: 500;
      line-height: normal;
      color: rgba(0, 0, 0, 0.85);;
      background: #F2F2F2;
    }

    .titleBox {
      font-size: 17px;
      font-weight: 500;
      line-height: 21px;
      text-align: center;
      letter-spacing: 0;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .customContainer {
    display: flex;
    justify-content: center;
    .customBody {
      display: flex;
      justify-content: center;
      width: fit-content;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.85);
      border: 1px solid #3D5AFE;
      border-bottom: none;
      border-right: none;
      margin-top: 20px;

      .itemBox {
        width: 125px;
        border-right: 1px solid #3D5AFE;

        .itemTitle {
          padding-left: 10px;
        }

        .scrollBox {
          overflow: auto;
          height: 192px;

          .item {
            height: 32px;
            display: flex;
            align-items: center;
            border-top: 1px solid rgba(0, 0, 0, 0.15);
            padding-left: 10px;
            cursor: pointer;
          }

          .item:hover {
            background: #F2F2F2;
          }
        }
      }
    }
  }
}