import { searchConditionAll } from '@/api/library';
import type { LoadingItem } from '@/components/LoadingList';
import { MinimizeButton, useLoadingListStore } from '@/components/LoadingList';
import MultiModal from '@/components/MultiModal';
import { Button } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import Context, { createUseMainPanelCtxStore } from './Context';
import CreateFolder from './MainPanel/CreateFolder';
import Progress from './MainPanel/Progress';
import SelectFiles from './MainPanel/SelectFiles';
import SelectLocation from './MainPanel/SelectLocation';
import styles from './MainPanel/index.module.less';
import { formatQueryData } from './MainPanel/useCtxStore';
import SelectLocationFolder from '../SelectLocationFolder';

interface Props {
  config: LoadingItem;
}

const Component = ({ config }: Props) => {
  const progressRef = useRef<{
    download: (path:string) => void;
    pause: () => void;
    resume: () => void;
    abort: () => void;
  }>();
  const selectLocationRef = useRef<{ open: () => void }>();
  const [useMainPanelCtxStore] = useState(() => createUseMainPanelCtxStore({ ...config }));
  const [setCurrentLoadingItem, setRemovedLoadingItem] = useLoadingListStore((state) => [
    state.setCurrentLoadingItem,
    state.setRemovedLoadingItem,
  ]);
  const [
    mainPanelOpen,
    setMainPanelOpen,
    selectedFileList,
    setSelectedFileList,
    setSelectedDataBankUrl,
    selectedFileMap,
    setSelectedFileMap,
    loadingStatus,
  ] = useMainPanelCtxStore((state) => [
    state.mainPanelOpen,
    state.setMainPanelOpen,
    state.selectedFileList,
    state.setSelectedFileList,
    state.setSelectedDataBankUrl,
    state.selectedFileMap,
    state.setSelectedFileMap,
    state.loadingStatus,
  ]);
  const [pageNumber, setPageNumber] = useState({ current: 1 });
  const [pageSize] = useState(1000);
  const [locationOpen, setLocationOpen] = useState(false);
  const [folderOpen, setFolderOpen] = useState(false);
  const isPreview = useMemo(() => {
    return config.module === 'preview';
  }, [config]);
  const okEnabled = useMemo(() => {
    return selectedFileList.length > 0 && loadingStatus === 'init';
  }, [selectedFileList, loadingStatus]);
  const pauseEnabled = useMemo(() => {
    return loadingStatus === 'loading';
  }, [loadingStatus]);
  const resumeEnabled = useMemo(() => {
    return loadingStatus === 'pause';
  }, [loadingStatus]);
  const abortEnabled = useMemo(() => {
    return loadingStatus === 'loading' || loadingStatus === 'pause';
  }, [loadingStatus]);
  const minimizeEnabled = useMemo(() => {
    return loadingStatus === 'loading' || loadingStatus === 'pause';
  }, [loadingStatus]);
  const closeEnabled = useMemo(() => {
    return loadingStatus === 'init' || loadingStatus === 'complete';
  }, [loadingStatus]);
  useEffect(() => {
    return () => {
      cancel();
      abort();
    };
  }, []);
  const getList = () => {
    let promise = null;
    if (config.module === 'preview') {
      promise = new Promise((resolve) => {
        resolve({
          data: {
            total: 1,
            list: config.fileList,
          },
        });
      });
    } else {
      promise = searchConditionAll(formatQueryData([{}], { pageNumber, pageSize, config }));
    }
    promise.then(({ data }: any) => {
      const { total, list } = data;
      const map: any = { ...selectedFileMap };
      list.forEach((item: any) => {
        item.status = 'init';
        map[item.filePath] = item;
      });
      setSelectedFileMap(map);
      setSelectedFileList(Object.values(map));
      if (config.module == 'dataBank') {
        setSelectedDataBankUrl(data.downloadUrl.map((itm: any) => `/api${itm.url}`));
      }
      if (total > pageSize * pageNumber.current) {
        setPageNumber((value) => ({ current: value.current + 1 }));
      }
    });
  };
  const minimize = () => {
    setMainPanelOpen(false);
    setCurrentLoadingItem({
      module: config.module,
      type: config.type,
      modalVisible: false,
      itemVisible: true,
    });
  };
  const cancel = () => {
    setMainPanelOpen(false);
    setRemovedLoadingItem({ ...config });
    useMainPanelCtxStore.reset();
  };
  const download = (path:string) => {
    setLocationOpen(false);
    progressRef.current?.download(path);
  };
  const pause = () => {
    progressRef.current?.pause();
  };
  const resume = () => {
    progressRef.current?.resume();
  };
  const abort = () => {
    progressRef.current?.abort();
  };
  useEffect(() => {
    if (mainPanelOpen && loadingStatus === 'init') {
      getList();
    }
  }, [mainPanelOpen, loadingStatus, pageNumber]);
  useEffect(() => {
    if (config.modalVisible) {
      setMainPanelOpen(true);
    } else {
      setMainPanelOpen(false);
    }
  }, [config]);
  const title = (
    <div className={styles.title}>
      <div className={styles.left}>
        {isPreview ? <span>下载文件</span> : <span>一键全部下载</span>}
        {isPreview ? (
          <span></span>
        ) : (
          <span>
            已选择 <b>{selectedFileList.length}</b> 个
          </span>
        )}
      </div>
      <div className={styles.right}>
        <MinimizeButton disabled={!minimizeEnabled} onClick={minimize} />
        <Button type="primary" ghost disabled={!closeEnabled} onClick={cancel}>
          关闭
        </Button>
      </div>
    </div>
  );

  return (
    <Context.Provider value={{ useMainPanelCtxStore, config }}>
      <MultiModal
        layoutGroup={`download_${config.module}_${config.type}`}
        layoutClassName="normal"
        mask={true}
        destroyOnClose={true}
        closable={false}
        title={title}
        open={mainPanelOpen}
        zIndex={isPreview ? 1800 : undefined}
        footer={[
          <Button
            key="download"
            type="primary"
            loading={loadingStatus === 'waiting'}
            disabled={!okEnabled}
            onClick={() => selectLocationRef.current?.open()}
          >
            确认下载
          </Button>,
          <Button key="pause" type="primary" ghost disabled={!pauseEnabled} onClick={pause}>
            暂停下载
          </Button>,
          <Button key="resume" type="primary" ghost disabled={!resumeEnabled} onClick={resume}>
            恢复下载
          </Button>,
          <Button key="abort" type="primary" ghost danger disabled={!abortEnabled} onClick={abort}>
            终止下载
          </Button>,
        ]}
      >
        <SelectFiles />
        <Progress ref={progressRef} />
        <SelectLocationFolder ref={selectLocationRef} title='选择一键下载位置' onConfirm={download} config={config}/>
      </MultiModal>
    </Context.Provider>
  );
};

export default Component;
