import useUserStore from '@/store/useUserStore';
import http from '../index';
interface Response {
  code: number;
  data: number;
  msg: string;
}
// 一对一音视频信令
// 音视频通话发起
export function sendcall(data: {
  fromDeviceType?: string;
  fromDeviceId?: string;
  toUserId: string;
  mediaType: number;
  remark?: string;
  sourceType?: number;
}) {
  const clientType = useUserStore.getState().clientType;
  const deviceId = useUserStore.getState().deviceId;
  data.fromDeviceId = deviceId;
  data.fromDeviceType = clientType;
  return http.post<Response>({
    url: `/web-api/media/signaling/call`,
    data,
  });
}
//音视频通话 回拨
export function callback(data: {
  fromDeviceType?: string;
  fromDeviceId?: string;
  remark?: string;
  sourceType: number;
  callRoomId: string;
  bizMsgId: string;
  todoCode?: number;
}) {
  const clientType = useUserStore.getState().clientType;
  const deviceId = useUserStore.getState().deviceId;
  data.fromDeviceId = deviceId;
  data.fromDeviceType = clientType;
  return http.post<Response>({
    url: `/web-api/media/signaling/callback`,
    data,
  });
}
// 取消音视频通话
export function cancelCall(data: { roomId: string; deviceType?: string; deviceId?: string }) {
  const clientType = useUserStore.getState().clientType;
  const deviceId = useUserStore.getState().deviceId;
  data.deviceId = deviceId;
  data.deviceType = clientType;
  return http.post<Response>({
    url: '/web-api/media/signaling/cancel',
    data,
  });
}
// 接受音视频通话
export function acceptCall(data: {
  roomId: string;
  deviceType?: string;
  deviceId?: string;
  remark?: string;
}) {
  const clientType = useUserStore.getState().clientType;
  const deviceId = useUserStore.getState().deviceId;
  data.deviceId = deviceId;
  data.deviceType = clientType;
  return http.post<Response>({
    url: '/web-api/media/signaling/accept',
    data,
  });
}
// 发布 发送信令
export function callpublish(data: { roomId: string; sdp: string }) {
  return http.post<Response>({
    url: `/web-api/media/signaling/publish`,
    data,
  });
}
// 播放 发送信令
export function callplay(data: { roomId: string; sdp: string }) {
  return http.post<Response>({
    url: `/web-api/media/signaling/play`,
    data,
  });
}
// 拒绝通话请求
export function refuse(data: { roomId: string; mediaType: number }) {
  return http.post<Response>({
    url: `/web-api/media/signaling/refuse`,
    data,
  });
}
// 结束通话
export function ringOff(data: { systemSource?: string; roomId: string; mediaType: number }) {
  return http.post<Response>({
    url: `/web-api/media/signaling/stop`,
    data,
  });
}
// 开关摄像头麦克风
export function turnOnOff(data: { input?: string; roomId: string; status: string }) {
  return http.post<Response>({
    url: `/web-api/media/signaling/turnOnOff`,
    data,
  });
}
