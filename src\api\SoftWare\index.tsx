import request from '../index';
// 桌面软件列表分页
export const querySoftWareByPage = (data: any) => {
  return request.post({
    url: '/web-api/desktop/softwareStore/desktopSoftwarePage',
    data,
  });
};
//获取当前设备已安装软件总数
export const selectInstallTotal = (params: any) => {
  return request.get({
    url: '/web-api/desktop/softwareStore/installTotal',
    params,
  });
};
//分享的软件取消操作
export const cancelShare = (params: any) => {
  return request.delete({
    url: '/web-api/desktop/software-share/cancelShare',
    params,
  });
};
//群分享
export const shareSoft = (data: any) => {
  return request.post({
    url: '/web-api/desktop/software-share/shareData',
    data,
  });
};
