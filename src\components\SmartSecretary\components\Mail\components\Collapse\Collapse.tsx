import { FC, useEffect, useState } from 'react';
import styles from './Collapse.module.less';
interface CollapseProps {
  items: string[];
  expand?: boolean;
}

const Collapse: FC<CollapseProps> = ({ items = [], expand = false }) => {
  const [expanded, setExpanded] = useState<boolean>(false);

  useEffect(() => {
    setExpanded(expand);
  }, [expand]);

  const rederItems = () => {
    if (items.length <= 1) {
      return items.map((item, index) => (
        <div key={index} className={styles.consDiv}>
          <span>{item}</span>
          {index < items.length - 1 ? <br /> : null}
        </div>
      ));
    }
    if (expanded) {
      return items.map((item, index) => (
        <div key={index} className={styles.consDiv}>
          <span>{item + `${index < items.length - 1 ? '；' : ''}`}</span>
          {index < items.length - 1 ? <br /> : renderButtons()}
        </div>
      ));
    }
    return (
      <div key={0} className={styles.consDiv}>
        <span>{items[0]}</span>
        {renderButtons()}
      </div>
    );
  };

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  const renderButtons = () => {
    return (
      <>
        {expanded ? (
          <span className={styles.cosSpan} onClick={toggleExpanded}>
            折叠
          </span>
        ) : (
          <span className={styles.cosSpan} onClick={toggleExpanded}>
            展开{items.length - 1}
          </span>
        )}
      </>
    );
  };

  return <>{rederItems()}</>;
};

export default Collapse;
