.title {
  display: flex;
  height: 32px;
  line-height: 32px;
  align-items: baseline;

  :global {
    span:last-child {
      color: initial;
      margin-left: 10px;
      font-size: 14px;
      b {
        color: var(--ant-color-error);
        font-weight: normal;
      }
    }
  }
}
.toolBar {
  padding: 16px;
}
.list {
  margin: 0 8px;
}
.selectLocalRoot {
  padding: 32px 16px;
  display: flex;
  justify-content: space-between;

  .item {
    display: flex;
    align-items: center;
  }
}
.createFolder {
  padding: 32px 16px;

  .item {
    display: flex;
    justify-content: center;
    align-items: center;

    &:first-child {
      margin-bottom: 24px;
    }
  }
}
.selectLocationModalFooter {
  display: flex;
  justify-content: space-between;
  padding: 0 16px;
}
.selectLocation {
  margin: 8px;
  // border: 1px solid var(--ant-color-primary);
  height: 380px;
  overflow: auto;
}
.success {
  color: var(--ant-color-success);
}
.uploading {
  color: var(--ant-color-primary);
}
.waiting {
}
.error {
  color: var(--ant-color-error);
}
