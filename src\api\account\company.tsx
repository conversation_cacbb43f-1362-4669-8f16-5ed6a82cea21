import request from '../index';

// 获得用户和角色关联分页
export const getUserRoleRef = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/userRoleRef/page',
    data,
  });
};

// 修改用户手机号和密码
export const updateUserRoleRef = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/userRoleRef/update',
    data,
  });
};

// 配置账户管理员
export const bindUserRoleRef = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/userRoleRef/bind',
    data,
  });
};

// 获得部门分页
export const getDeptPage = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/dept/page',
    data,
  });
};

// 自定义部门联想查询列表分页
export const queryDeptByPage = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/dept/associateQueryByPage',
    data,
  });
};

// 创建部门
export const createDept = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/dept/create',
    data,
  });
};

// 删除部门
export const deleteDept = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/dept/delete',
    data,
  });
};

// 更新部门
export const updateDept = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/dept/update',
    data,
  });
};

// 部门|岗位|工作内容修改历史记录分页
export const configLogPage = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/config/log/page',
    data,
  });
};

// 获得人事信息分页(账号管理页面角色绑定用)
export const getStaffList = (data: any) => {
  return request.post({
    url: '/web-api/hr/hr/pageBind',
    data,
  });
};

// 获得岗位列表
export const getPostList = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/post/page',
    data,
  });
};

// 自定义岗位联想查询列表分页
export const queryPostByPage = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/post/associateQueryByPage',
    data,
  });
};

// 创建岗位
export const createPost = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/post/create',
    data,
  });
};

// 删除岗位
export const deletePost = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/post/delete',
    data,
  });
};

// 更新部门
export const updatePost = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/post/update',
    data,
  });
};

// --------------------------------------------

// 获得工作内容列表
export const getJobList = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/jobContent/page',
    data,
  });
};

// 创建工作内容
export const createJobContent = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/jobContent/create',
    data,
  });
};

// 删除工作内容
export const deleteJobContent = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/jobContent/delete',
    data,
  });
};

// 更新工作内容
export const updateJobContent = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/jobContent/update',
    data,
  });
};

// 获取企业信息
export const getCompanyInfo = (data: any) => {
  return request.get({
    url: '/web-api/ee/account/manage/enterprise/info/get',
    params: data,
  });
};

// PC端获取跳转凭证二维码信息
export const getGenerate = (data: any) => {
  return request.post({
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    url: '/web-api/account/auth/scanRedirect/redirectToken/generate',
    data,
  });
};
