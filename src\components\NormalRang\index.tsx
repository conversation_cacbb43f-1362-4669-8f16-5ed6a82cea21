import { FC, useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styles from './NormalRang.module.less';
interface RangelProps {
  strokeColor?: string;
  trailColor?: string;
  placeholder?: string;
  borderRadius?: string;
  value: number;
  total: number;
}
const strokeColor: string = '#333'; //已完成的分段的颜色
const trailColor: string = '#fff'; //未完成的分段的颜色
const NormalRang = (props: RangelProps) => {
  const getStrokeWidth = () => {
    return ((props.value / props.total) * 100).toFixed(2) + '%';
  };
  const getTrailWidth = () => {
    return ((1-(props.value / props.total)) * 100).toFixed(2) + '%';
  };
  useEffect(() => {}, []);
  return (
    <>
      <div
        className={styles.NormalRang}
        style={{ borderRadius: `${props.borderRadius ? props.borderRadius : '10px'}`,backgroundColor: `${props.trailColor ? props.trailColor : trailColor}` }}
      >
        <div
          style={{
            flexBasis: `${getStrokeWidth()}`,
            backgroundColor: `${props.strokeColor ? props.strokeColor : strokeColor}`,
            borderRadius: `${props.borderRadius ? props.borderRadius : '10px'}`,
          }}
        />
        <div style={{flexBasis: `${getTrailWidth()}`, }} />
      <div className={styles.placeholderBox}>{props.placeholder||''}{getStrokeWidth()}</div>
      </div>
    </>
  );
};

export default NormalRang;
