.multiModalWrapper {
  &:global(.modalLeft) {
    width: 0 !important;
    height: 0 !important;
    overflow: visible !important;

    :global {
      .ant-modal {
        left: calc(100vw / 2 - 722px);
      }
    }
  }

  &:global(.modalRight) {
    :global {
      .ant-modal {
        left: calc(100vw / 2 + 2px);
      }
    }
  }

  &:global(.normal) {
    :global {
      .ant-modal {
        margin: 0 auto;
        left: inherit;
      }
    }
  }

  :global {
    .ant-modal {
      margin: inherit;
      left: calc((100vw - 720px) / 2);
      transition: left 0.6s;
    }
    .ant-modal-content {
      padding: 0;
    }
    .ant-modal-close {
      height: 32px;
      width: 60px;
      border-radius: 4px;
      text-align: center;
      top: 16px;
      right: 16px;
      border: 1px solid var(--ant-color-primary);
      color: var(--ant-color-primary);
      background-color: #fff;
      font-weight: 500;
      &:hover {
        border: 1px solid var(--ant-color-primary-hover);
        color: var(--ant-color-primary-hover);
        background-color: #fff;
      }
    }
    .ant-modal-close-x {
      line-height: 30px;
      width: 60px;
      font-size: 14px;
    }
    .ant-modal-header {
      border-bottom: 1px solid #efefef;
      padding: 16px;
      margin-bottom: 0;
    }
    .ant-modal-title {
      font-size: 20px;
      color: var(--ant-color-primary);
      line-height: 32px;
      font-weight: 500;
    }
    .ant-modal-footer {
      position: relative;
      top: -1px;
      border-top: 1px solid #efefef;
      padding: 8px 0 24px;
      margin-top: 0;
      text-align: center;
    }
    /* 
    .ant-table-tbody-virtual-scrollbar-vertical {
      top: -40px !important;
      margin-top: 20px !important;
      margin-bottom: 20px !important;
      width: 20px !important;
      border-left: 1px solid #ddd;
      border-right: 1px solid #ddd;
      background-color: #fff !important;

      &::before {
        position: absolute;
        top: -20px;
        left: -1px;
        content: ' ';
        display: inline-block;
        width: 20px;
        height: 20px;
        background: url('./images/up.svg') center no-repeat;
        border: 1px solid #ddd;
        border-bottom-color: #fff;
        background-color: #fff;
      }
      &::after {
        position: absolute;
        bottom: -20px;
        left: -1px;
        content: ' ';
        display: inline-block;
        width: 20px;
        height: 20px;
        background: url('./images/down.svg') center no-repeat;
        border: 1px solid #ddd;
        border-top-color: #fff;
        background-color: #fff;
      }
    }
    .ant-table-tbody-virtual-scrollbar-thumb {
      border-radius: 0 !important;
      border: 1px solid #fff;
      background-color: #7986cb !important;
    } 
    */
  }
}
