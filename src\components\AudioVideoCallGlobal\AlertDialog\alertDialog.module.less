.AlertModalContent {
  width: 336px;
  height: 168px;
  border-radius: 10px;
  opacity: 1;
  background-color: #fff;
  box-sizing: border-box;
  border: 1px solid #ffffff;

  padding: 32px 24px 24px 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  .Mark {
    width: 100%;
    position: absolute;
    height: 50px;
    background: linear-gradient(78deg, #0302f2 16%, #2c68f6 97%);
    left: 0;
    top: 0;
    border-radius: 10px;
  }
  .MarkBg {
    width: 100%;
    height: 100%;
    position: absolute;
    border-radius: 10px;
    left: 0;
    top: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
  }
  .AlertModalContentTitle {
    font-size: 16px;
    color: #3d3d3d;
    font-weight: 600;
    position: relative;
    z-index: 3;
  }
  .confirmBut {
    text-align: right;
  }
}
