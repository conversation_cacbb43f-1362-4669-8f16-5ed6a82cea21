import MultiModal from '@/components/MultiModal';
import AddGroup from '@/pages/addressBook/components/addGroup';
import AddressModal from '@/pages/addressBook/components/addressModal';
import { getWidth } from '@/utils/common';

interface AddFriendProps {
  open: boolean;
  onCancel: () => void;
  success?: () => void;
  layoutClassName?: 'modalRight' | 'modalLeft' | 'normal';
}
/**
 * 添加通讯录好友模态框
 */
const AddFriendModal = ({
  open,
  onCancel,
  success,
  layoutClassName = 'modalRight',
}: AddFriendProps) => {
  const curType = 'addGroup';

  return (
    <>
      <MultiModal
        title=""
        open={open}
        footer={null}
        closable={false}
        layoutClassName={layoutClassName}
        width={getWidth(380)}
        mask={true}
        top={180}
        destroyOnClose={true}
      >
        <AddGroup cb={onCancel} curType={curType} success={success} isListOpen showType="small" />
      </MultiModal>
      <AddressModal cb={onCancel} curType={curType} />
    </>
  );
};
export default AddFriendModal;
