import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create, createJSONStorage, devtools } from '@/store';
interface State {
  isBusyline: boolean;
  callTypeTitle: '邮箱音频通话中'|'邮箱视频通话中'|'音频通话中'|'视频通话中'|'视频会议通话中'|'';
}
interface SetState {
  setIsBusyline: Setter;
  setCallTypeTitle: Setter;
}
const useAudioVideoCallGlobalStore = autoUseShallow<State, SetState>(
  create(
    devtools(
      autoCreateSetters<State>({
        isBusyline: false,
        callTypeTitle: '',
      }),
      { name: 'AudioVideoCallGlobal', storage: createJSONStorage(() => sessionStorage) },
    ),
  ),
);

export default useAudioVideoCallGlobalStore;
