import { Button, Table, Space } from 'antd';
import { useContext, useMemo } from 'react';
import styles from './index.module.less';
import { getColumns } from '../Users/<USER>';
import Context from '../Context';

const Component = () => {
  const { useMainPanelCtxStore } = useContext(Context);
  const [userList, setUserList, userMap, setUserMap] = useMainPanelCtxStore!((state) => [
    state.userList,
    state.setUserList,
    state.userMap,
    state.setUserMap,
  ]);
  const columns = useMemo(() => {
    return getColumns().map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              {userList.length === 0 && <span>操作</span>}
              {userList.length > 0 && (
                <Button
                  type="primary"
                  size="small"
                  ghost
                  onClick={() => {
                    setUserMap({});
                    setUserList([]);
                  }}
                >
                  全部取消
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          return (
            <Space>
              <Button
                ghost
                type="primary"
                size="small"
                onClick={() => {
                  const map = { ...userMap };
                  delete map[row.id];
                  setUserMap(map);
                  setUserList(Object.values(map));
                }}
              >
                取消
              </Button>
            </Space>
          );
        };
      }
      return item;
    });
  }, [userList, userMap]);

  return (
    <div className={styles.list}>
      <div className={styles.title}>已选分享用户({userList.length})</div>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={userList}
        rowKey={'id'}
        scroll={{ y: 163 }}
        pagination={false}
      />
    </div>
  );
};

export default Component;
