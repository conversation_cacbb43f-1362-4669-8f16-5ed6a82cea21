import { libraryShareSaveFile } from '@/api/library';
import MultiModal from '@/components/MultiModal';
import { Button, message } from 'antd';
import { useContext, useMemo, useState } from 'react';
import Context from '../Context';
import SelectFiles from './SelectFiles';
import ShareFolders from './ShareFolders';
import ToolBar from './ToolBar';
import Users from './Users';

interface Props {
  onCancel?: () => void;
  onToolBarClick?: (type: string) => void;
}

const Component = ({ onCancel, onToolBarClick }: Props) => {
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [
    mainPanelOpen,
    userList,
    sharedFolderList,
    selectedFileList,
    safeLevel,
    enableFinalTime,
    finalTime,
  ] = useMainPanelCtxStore!((state) => [
    state.mainPanelOpen,
    state.userList,
    state.sharedFolderList,
    state.selectedFileList,
    state.safeLevel,
    state.enableFinalTime,
    state.finalTime,
  ]);
  const [loading, setLoading] = useState(false);
  const disabled = useMemo(() => {
    return (
      !selectedFileList.length ||
      (!userList.length && !sharedFolderList.length) ||
      (enableFinalTime && !finalTime)
    );
  }, [userList, sharedFolderList, selectedFileList, enableFinalTime, finalTime]);
  const cancel = () => {
    useMainPanelCtxStore!.reset();
    if (onCancel) {
      onCancel();
    }
  };
  const ok = () => {
    libraryShareSaveFile(
      {
        userList: userList.map((item: any) => {
          return {
            userId: item.userId,
            userName: item.username,
            realName: item.contactName,
            tenantId: item.tenantId,
          };
        }),
        groupIdList: sharedFolderList.map((item: any) => item.id),
        safeLevel: safeLevel,
        fileList: selectedFileList.map((item: any) => {
          return {
            esId: item.id,
            title: item.title,
            content: item.content,
            source: item.source,
            remark: item.remark,
            fileType: item.fileType,
            fileFormatType: item.fileFormatType,
            fileSize: item.fileSize,
            filePath: item.filePath,
            imgIsStandard: item.imgIsStandard,
            imgWidth: item.imgWidth,
            imgHeight: item.imgHeight,
            thumbnailOne: item.thumbnailOne,
            thumbnailTwo: item.thumbnailTwo,
            bizId: item.bizId,
            version: item.version,
          };
        }),
        timingFinalTime: finalTime,
        isSystemType:
          config?.module === 'audioPlay' ? 3 : config?.module === 'videoPlay' ? 4 : undefined,
      },
      { setLoading },
    ).then(() => {
      message.success('分享成功').then(() => {
        cancel();
      });
    });
  };

  return (
    <MultiModal
      layoutGroup={`share_${config?.module}`}
      layoutClassName="modalRight"
      destroyOnClose={true}
      title={`分享${config?.typeName || '文件'}`}
      open={mainPanelOpen}
      onCancel={cancel}
      footer={[
        <Button key="submit" type="primary" loading={loading} disabled={disabled} onClick={ok}>
          确认分享
        </Button>,
      ]}
    >
      <ToolBar onClick={onToolBarClick} />
      <Users />
      <ShareFolders />
      <SelectFiles />
    </MultiModal>
  );
};

export default Component;
