.card {
  margin-bottom: 10px;
  font-size: 14px;

  &.send {
    .card-header {
      background: #f6faff;
      border: 1px solid #78b0f4;
      border-bottom: none;
      .itemPar {
        .titleSpan {
          color: #7097f6;
        }
      }
      .detailSpan {
        background: #6799ff;
      }
    }
    .card-content {
      border: 1px solid #78b0f4;
      border-top: none;
    }
  }
  .card-header {
    background: #fcf6f3;
    padding: 16px 16px 5px 16px;
    border: 1px solid #ffc6a2;
    border-radius: 10px 10px 0 0;
    border-bottom: none;

    span {
      display: inline-block;
      line-height: 24px;
      height: 24px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.6);
    }

    .itemPar {
      padding-bottom: 10px;
      .edit-span {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        position: relative;
        min-width: 56px;
        .text-span {
          text-align-last: justify;
          min-width: 50px;
        }
      }
      .edit-span.marked::before {
        content: '';
        width: 8px;
        height: 8px;
        background-color: #ef5350;
        border-radius: 50%;
        position: absolute;
        left: -10px;
        top: 50%;
        transform: translateY(-50%);
      }
      .titleSpan {
        font-size: 18px;
        font-weight: 600;
        text-align: right;
        color: #f0955c;
        min-width: 100px;
      }
    }

    .headerContent {
      color: rgba(0, 0, 0, 0.6);
    }

    .detailSpan {
      min-width: 40px;
      border-radius: 4px;
      font-size: 12px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      color: #ffffff;
      background: #ff8a65;
      cursor: pointer;
    }
  }

  .card-content {
    border-radius: 0 0 10px 10px;
    border: 1px solid #ffc6a2;
    border-top: none;
    padding: 16px 16px 5px 16px;

    .imgStyles {
      width: 40px;
      height: 22px;
      margin-right: 16px;
    }
    .itemPar {
      padding-bottom: 10px;
    }

    .item-parc {
      height: 40px;
      border-radius: 4px;
      padding: 6px 8px;
      margin: 0px 5px 10px 55px;
      background: #8092ff;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .icon {
        width: 16px;
        height: 16px;
      }
      .title {
        height: 20px;
        opacity: 1;
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        z-index: 1;
      }
      .duration {
        height: 17px;
        font-size: 12px;
        font-weight: 500;
        color: #ffffff;
      }
      button {
        width: 60px;
        height: 28px;
        border-radius: 4px;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 4px 16px;
        gap: 8px;
        background: #ffffff;
        line-height: 20px;
        font-size: 14px;
      }
      .okButton {
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .titleBox {
      width: 100%;
      text-align: center;
      font-size: 18px;
      font-weight: 600;
      color: #252525;
    }
    .contentBox {
      width: 486px;
      .contentHtml {
        word-break: break-word;
      }
      .symbolSpan {
        display: inline-block;
        width: 40px;
        height: 18px;
        border-radius: 4px;
        background: #d3d3d3;
        cursor: pointer;
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        color: #4d4d4d;
        margin-left: 3px;
      }
      .paragraph {
        font-size: 14px !important;
      }
    }
    .annexBox {
      width: 100%;
    }

    .annexSpan {
      span {
        display: flex;
        min-width: 80px;
        height: 32px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      .redeSpan {
        min-width: 60px;
        background: rgba(0, 0, 0, 0.05);
        color: #3d5afe;
        margin-right: 10px;
      }
      .huifu {
        background: linear-gradient(180deg, #ff9863 -3%, #f16e42 100%);
      }
      .xufa {
        background: #3d5afe;
      }
    }
  }
}
