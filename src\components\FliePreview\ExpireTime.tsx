import { useEffect, useMemo, useState } from 'react';
import styles from './FilePreview.module.less';

interface Props {
  expireTime: number;
}

const Component = ({ expireTime }: Props) => {
  const [time, setTime] = useState(expireTime);
  const expire = useMemo(() => {
    let last = time;
    const days = Math.floor(last / (60 * 60 * 24));
    last = time % (60 * 60 * 24);
    const hours = Math.floor(last / (60 * 60));
    last = time % (60 * 60);
    const minutes = Math.floor(last / 60);
    return {
      days,
      hours,
      minutes,
    };
  }, [time]);
  useEffect(() => {
    const interval = setInterval(() => {
      setTime((prev) => prev - 60);
    }, 1000 * 60);
    return () => {
      clearInterval(interval);
    };
  }, []);

  return (
    <span className={styles.expireTime}>
      <b>{expire.days}</b>天<b>{expire.hours}:{expire.minutes}</b> 后撤回
    </span>
  );
};

export default Component;
