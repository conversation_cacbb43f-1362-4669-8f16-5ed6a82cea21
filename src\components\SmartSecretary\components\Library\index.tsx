import { searchConditionAll } from '@/api/library';
import useIntelligentSecretaryStore from '@/store/useIntelligentSecretaryStore';
import useLibraryStore from '@/store/useLibraryStore';
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Flex, MenuProps, message, Space } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import styles from './index.module.less';
import SecretaryLibraryList from './list';

const SecretaryLibrary = () => {
  const timeOptions: MenuProps['items'] = [
    { key: '1', label: '一天以内' },
    { key: '7', label: '一周以内' },
    { key: '30', label: '一月以内' },
  ];
  const fileSizeOptions: MenuProps['items'] = [
    { key: '1', label: '1M以上' },
    { key: '0.1~1', label: '100kb~1M' },
    { key: '0.1', label: '100kb内' },
  ];
  const fileTypeOptions: MenuProps['items'] = useLibraryStore((state) => state.fileFormatTypeList);
  const [libraryData, setLibraryData] = useState<any>([]);
  const [hasMore, setHasMore] = useState(true);
  const [pageNo, setPageNo] = useState(1);
  const pageSize = 10;
  const [fileTypeSelected, setFileTypeSelected] = useState<Array<string>>([]);
  const [fileSizeSelected, setFileSizeSelected] = useState('');
  const [fileTimeSelected, setFileTimeSelected] = useState('');
  const [text, queryData, queryType, shareGroupId] = useIntelligentSecretaryStore((state) => [
    state.text,
    state.queryData,
    state.queryType,
    state.shareGroupId,
  ]);

  const fetchList = () => {
    console.log('queryType', queryType);
    console.log('text', text);
    console.log('queryData', queryData);
    const params: any = {
      pageNo,
      pageSize,
      fileFormatType: [],
      querySizeList: [],
      startTime: [],
      groupId: shareGroupId,
    };
    if (queryType.current === 'current') {
      params.title = [text];
    } else if (queryType.current === 'result') {
      params.title = queryData;
    }
    if (fileTypeSelected.length) params.fileFormatType = [fileTypeSelected.join()];
    if (fileSizeSelected) {
      switch (fileSizeSelected) {
        case '0.1':
          params.querySizeList = [{ minFileSize: '', maxFileSize: 0.1 }];
          break;
        case '0.1~1':
          params.querySizeList = [{ minFileSize: 0.1, maxFileSize: 1 }];
          break;
        default:
          params.querySizeList = [{ minFileSize: 1, maxFileSize: '' }];
      }
    }
    if (fileTimeSelected) {
      const date = new Date(new Date().getTime() - Number(fileTimeSelected) * 24 * 60 * 60 * 1000);
      const dateStr = dayjs(date).format('YYYY-MM-DD') + ' 00:00:00';
      console.log('dateStr', dateStr);
      params.startTime = [dateStr];
    }
    searchConditionAll(params).then(({ data }: any) => {
      pageNo === 1 ? setLibraryData(data.list) : setLibraryData([...libraryData, ...data.list]);
      if (libraryData.length + data.list.length >= data.total) {
        setHasMore(false);
      }
    });
  };
  const fetchListNext = () => {
    setPageNo(pageNo + 1);
  };
  const todoTips = () => {
    message.warning('功能正在开发中...');
  };

  useEffect(() => {
    console.log('文件格式变化触发数据请求');
    pageNo === 1 ? fetchList() : setPageNo(1);
  }, [fileTypeSelected, fileSizeSelected, fileTimeSelected, shareGroupId]);

  useEffect(() => {
    console.log('queryType:触发过滤', queryType);
    fetchList();
  }, [queryType, pageNo]);
  return (
    <div className={styles.secretaryLibraryWrap}>
      <Flex className={styles.headRow}>
        <div
          className={styles.headBtn}
          onClick={() => {
            todoTips();
          }}
        >
          黑名单管理
        </div>
        <div
          className={styles.headBtn}
          onClick={() => {
            todoTips();
          }}
        >
          分享
        </div>
      </Flex>
      <Flex className={styles.filterRow}>
        <Dropdown
          menu={{
            items: timeOptions,
            selectable: true,
            selectedKeys: [fileTimeSelected],
            onClick: ({ key }) => {
              setFileTimeSelected(fileTimeSelected !== key ? key : '');
            },
          }}
          className={styles.filterItem}
        >
          <Space>
            时间范围
            <DownOutlined style={{ fontSize: 10 }} />
          </Space>
        </Dropdown>
        <Dropdown
          menu={{
            items: fileSizeOptions,
            selectable: true,
            selectedKeys: [fileSizeSelected],
            onClick: ({ key }) => {
              setFileSizeSelected(fileSizeSelected !== key ? key : '');
            },
          }}
          className={styles.filterItem}
        >
          <Space>
            文件大小
            <DownOutlined style={{ fontSize: 10 }} />
          </Space>
        </Dropdown>
        <Dropdown
          menu={{
            items: fileTypeOptions,
            selectable: true,
            multiple: true,
            selectedKeys: fileTypeSelected,
            onClick: ({ key }) => {
              if (fileTypeSelected.includes(key)) {
                setFileTypeSelected(fileTypeSelected.filter((item: string) => item != key));
              } else {
                setFileTypeSelected([...fileTypeSelected, key]);
              }
            },
          }}
          className={styles.filterItem}
        >
          <Space>
            文件格式
            <DownOutlined style={{ fontSize: 10 }} />
          </Space>
        </Dropdown>
      </Flex>
      <SecretaryLibraryList list={libraryData} hasMore={hasMore} fetchListNext={fetchListNext} />
    </div>
  );
};

export default SecretaryLibrary;
