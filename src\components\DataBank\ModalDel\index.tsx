import { getWidth } from '@/utils/common';
import { Button, Divider, Modal } from 'antd';
import { FC, useEffect } from 'react';
import styles from './index.module.less';

interface ModalTitleProps {
  isConfirmOpen?: boolean;
  setIsConfirmOpen?: any;
  confirmDel?: any;
  title?: any;
  onConfirmText?: any;
  children?: any;
  loading?: boolean;
}

const ModalTitle: FC<ModalTitleProps> = ({
  isConfirmOpen,
  setIsConfirmOpen,
  confirmDel,
  title = '确认删除吗？',
  onConfirmText = '确认删除',
  children,
  loading = false,
}) => {
  useEffect(() => {}, []);
  return (
    <Modal
      title=""
      open={isConfirmOpen}
      closable={false}
      footer={null}
      width={getWidth(550)}
      centered
      zIndex={1200}
      wrapClassName={styles.modalOtherWrap}
      onCancel={() => setIsConfirmOpen(false)}
    >
      <div className={styles.headWrap}>
        <div className={styles.leftHeader}>
          <div className={styles.title}>{title}</div>
          <Button
            type="primary"
            className={styles.close}
            onClick={() => {
              setIsConfirmOpen(false);
            }}
          >
            关闭
          </Button>
        </div>
      </div>
      <Divider />
      <div className={styles.modalContent}>
        {children || (
          <>
            确认后，已选择的 <span className="text-[#3D5AFE]">2</span>
            个文件将放入回收站！你可以在回收站<span className="text-[#F4511E]">取消删除</span>
            或者 <span className="text-[#F4511E]">永久删除</span>
          </>
        )}
      </div>
      <Divider />
      <div className={styles.btnsWrap}>
        <Button
          type="primary"
          className={styles.btnAdjust}
          onClick={() => confirmDel()}
          loading={loading}
        >
          {onConfirmText}
        </Button>
        <Button type="primary" className={styles.close} onClick={() => setIsConfirmOpen(false)}>
          取消
        </Button>
      </div>
    </Modal>
  );
};

export default ModalTitle;
