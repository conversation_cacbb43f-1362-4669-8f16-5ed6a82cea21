import { getWidth } from '@/utils/common';
import { Button, Divider, Modal } from 'antd';
import { FC, useEffect } from 'react';
import styles from './index.module.less';

interface ModalTitleProps {
  isTipOpen?: boolean;
  setisTipOpen?: any;
  children?: any;
  title?: any;
}

const ModalTitle: FC<ModalTitleProps> = ({ isTipOpen, setisTipOpen, children, title }) => {
  useEffect(() => {}, []);
  return (
    <Modal
      title=""
      open={isTipOpen}
      closable={false}
      footer={null}
      width={getWidth(550)}
      centered
      zIndex={1200}
      wrapClassName={styles.modalOtherWrap}
      onCancel={() => setisTipOpen(false)}
    >
      <div className={styles.headWrap}>
        <div className={styles.leftHeader}>
          <div className={styles.title}>{title || '数据已全部删除成功'}</div>
          <Button type="primary" className={styles.close} onClick={() => setisTipOpen(false)}>
            关闭
          </Button>
        </div>
      </div>
      <Divider />
      <div className={styles.modalContent}>
        {children || (
          <>
            你可以在回收站选择<span className="text-[#F4511E]">取消删除</span>
            或者 <span className="text-[#F4511E]">永久删除</span>
          </>
        )}
      </div>
    </Modal>
  );
};

export default ModalTitle;
