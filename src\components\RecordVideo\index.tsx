import videoOn from '@/assets/images/mail/videoOn.png';
import videoOff from '@/assets/images/mail/voiceOff.png';
import { getWidth } from '@/utils/common';
import { Timeout } from 'ahooks/lib/useRequest/src/types';
import { Button, Flex, message, Modal } from 'antd';
import { useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
interface Props {
  open: boolean;
  title: string;
  onCancel: () => void;
  onAdd: (data: any) => void;
}

const Component = ({ open, title, onCancel, onAdd }: Props) => {
  const [duration, setDuration] = useState<number>(0);
  const intervalRef = useRef<Timeout>();
  const [isRunning, setIsRunning] = useState<boolean>(false); //是否已开启录视频
  //TODO
  const videoChunksRef = useRef<Blob[]>([]);
  const mediaStreamRef = useRef<MediaStream>();
  const mediaRecorderRef = useRef<MediaRecorder>();
  const [videoBlob, setvideoBlob] = useState<Blob>();
  const videoRef = useRef<HTMLVideoElement>(null);

  const startTimer = () => {
    if (!isRunning) {
      setIsRunning(true);
      intervalRef.current = setInterval(() => {
        setDuration((prev) => prev + 1);
      }, 1000);
    }
  };
  const stopTimer = () => {
    if (isRunning) {
      clearInterval(intervalRef.current);
      setIsRunning(false);
    }
  };

  const formatTime = () => {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const secs = duration % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  useEffect(() => {
    return () => {
      stopTimer();
    };
  }, []);
  useEffect(() => {
    if (videoBlob && duration < 120) {
      onAdd({ duration, videoBlob, type: 'mp4' });
      onCancel();
    }
  }, [videoBlob]);

  useEffect(() => {
    if (duration === 120) {
      stopRecording();
      message.warning('录制时长最长为120秒,已自动结束并保存');
      setTimeout(() => {
        onAdd({ duration, videoBlob, type: 'mp4' });
        onCancel();
      }, 2000);
    }
  }, [duration]);

  const startRecord = () => {
    startRecording();
  };
  const stopRecord = () => {
    stopRecording();
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
      mediaStreamRef.current = stream;
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          videoChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(videoChunksRef.current, {
          type: 'video/webm',
        });
        setvideoBlob(blob);
        videoChunksRef.current = [];
      };

      mediaRecorder.start();
      startTimer();
    } catch (err) {
      console.error('获取媒体输入权限失败:', err);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      stopTimer();
      mediaRecorderRef.current.stop();
      mediaStreamRef.current?.getTracks().forEach((track) => track.stop());
    }
  };

  useEffect(() => {
    // 清理函数，确保在组件卸载时停止录制并释放资源
    return () => {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop();
        mediaStreamRef.current?.getTracks().forEach((track) => track.stop());
      }
      stopTimer();
    };
  }, []);

  return (
    <Modal
      title={null}
      open={open}
      centered
      closable={false}
      footer={null}
      keyboard={false}
      maskClosable={false}
      width={getWidth(462)}
      mask={false}
      className={styles.recordContent}
    >
      {/* 头部 */}
      <Flex justify={'space-between'} align={'center'} className={styles.header}>
        <span className={styles.headerSpan}>{title}</span>
        <Button className={styles.headerButton} onClick={onCancel}>
          关闭
        </Button>
      </Flex>
      <Flex vertical justify={'space-between'} className={styles.content}>
        {/* 计时器 */}
        <span className={styles.duration}>{formatTime()}</span>
        {/* 录制播放区 */}
        <video
          ref={videoRef}
          autoPlay
          className={styles.autoPlay}
          style={{ width: getWidth(460), height: getWidth(928), objectFit: 'cover' }}
        ></video>
        {/* 操作区 */}
        <div className={styles.float}>
          {isRunning ? (
            <img src={videoOff} onClick={stopRecord} />
          ) : (
            <img src={videoOn} onClick={startRecord} />
          )}
        </div>
      </Flex>
    </Modal>
  );
};

export default Component;
