import { But<PERSON>, Flex } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import type { ColumnField, FileRecord } from './columns';
import Context from './Context';
import FilesList from './FilesList';
import Filter from './Filter';
import styles from './index.module.less';
import SelectedFilesList from './SelectedFilesList';
import createUseCtxStore from './useCtxStore';

interface Props {
  open: boolean;
  onCancel: () => void;
  onOk: (records: FileRecord[]) => void;
  defaultValue?: FileRecord[];
  columnFields?: ColumnField[];
}

const Component = ({ open, defaultValue, columnFields, onCancel, onOk }: Props) => {
  const [useCtxStore] = useState(() => createUseCtxStore());
  const [list, selectedList, setSelectedList] = useCtxStore((state) => [
    state.list,
    state.selectedList,
    state.setSelectedList,
  ]);
  const okDisabled = useMemo(() => {
    return selectedList.length === 0;
  }, [selectedList]);
  const cancel = () => {
    onCancel();
  };
  const ok = () => {
    setSelectedList([]);
    onOk([
      ...selectedList.map((item: any) => {
        return Object.assign(item, { status: 'init' });
      }),
    ]);
  };

  useEffect(() => {
    if (open === false) {
      useCtxStore.reset();
    } else {
      if (defaultValue?.length) {
        setSelectedList([...defaultValue]);
      }
    }
  }, [open]);

  return (
    <Context.Provider value={{ useCtxStore, columnFields }}>
      <Flex vertical className={styles.selectFiles}>
        <div className={styles.header}>
          <div className={styles.title}>
            <span>选择文件</span>
            <span>
              共计 <b>{list.length}</b> 个，已选择 <b>{selectedList.length}</b> 个
            </span>
          </div>
          <Button type="primary" ghost onClick={cancel}>
            关闭
          </Button>
        </div>
        <Filter />
        <FilesList />
        <SelectedFilesList />
        <div className={styles.footer}>
          <Button key="submit" type="primary" disabled={okDisabled} onClick={ok}>
            确认选择
          </Button>
        </div>
      </Flex>
    </Context.Provider>
  );
};

export default Component;
