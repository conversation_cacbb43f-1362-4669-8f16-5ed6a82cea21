import { createContext } from 'react';
interface ContextType {
  queryResult?: any; //结果查询
  queryResultRight?: any; //右侧表单结果查询
  queryAll?: any; //全部查询
  queryAllRight?: any; // 右侧表单全部查询
  queryList?: any; //左侧查询列表,基础查询用来支持滚动查询
  confirmSelect?: any; //二次确认后
  formConfig?: any; //表单配置，默认initFormConfig
  titleConfig?: any; //标题配置，配置titleConfig
  data?: any; //左侧数据
  total?: any; //选择数量总数
}
// 查询条件默认的配置
const initFormConfig = [
  { name: 'name', label: '姓名', type: 'input', placeholder: '请输入' },
  { name: 'title', label: '标题', type: 'input', placeholder: '请输入' },
];
const titleConfig = {
  leftTitle: '选择文件',
  rightTitle: '已选文件',
};
const contextData: ContextType = { formConfig: initFormConfig, titleConfig };
const RootContext = createContext(contextData);
export default RootContext;
