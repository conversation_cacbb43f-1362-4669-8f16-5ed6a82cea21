import { FC, useState, useEffect } from 'react'
import styles from './SelectDownloadLocation.module.less';
import {Select,Input} from 'antd';
interface ModalProps {
  onCloseModal?: () => void; // 关闭弹框回调方法
  GetFilePosition?: (value:any) => void; // 显示选择文件
  open?: boolean;
}
const SelectDownloadLocation = (props:ModalProps) => {
  useEffect(() => {
   
  }, [])
  return (
    <>
    {props.open?<div className={styles.SelectDownloadLocation}>
        <div className={styles.containerBox}>
          <div className={styles.header}>
            <span>新建文件夹</span>
            <div className={styles.closeBut} onClick={()=>{
                props.onCloseModal&&props.onCloseModal();
              }}>关闭</div>
          </div>                               
          <main>
            <div>
           <span> 选择文件夹位置：</span>
            <Select
              className={styles.selectItem}
              popupClassName={styles.popupClassName}
              placeholder="请选择"
              options={[
                {
                  label: 'C盘',
                  value: 'c',
                },
                {
                  label: 'D盘',
                  value: 'D',
                },
                {
                  label: 'E盘',
                  value: 'E',
                },
                {
                  label: '移动硬盘',
                  value: 'F',
                },
              ]}
            />
            </div>
            <div>
           <span> 新建文件夹名称：</span>
            <Input  className={styles.inputItem} placeholder='请输入新建文件夹名称'/>
            </div>
          </main>
          <footer>
            <div>
              <span onClick={()=>{
                props.GetFilePosition&&props.GetFilePosition('');
                props.onCloseModal&&props.onCloseModal();
              }}>确认</span>
              <span onClick={()=>{
                props.onCloseModal&&props.onCloseModal();
              }}>取消</span>
            </div>
          </footer>
        </div>
       </div>:null}
      
    </>
  )
}

export default SelectDownloadLocation
