import { createContext } from 'react';
export { default as createUseMainPanelCtxStore } from './MainPanel/useCtxStore';
export { default as createUseSelectFilesCtxStore } from './SelectFiles/useCtxStore';
import type { UseStore } from '@/store/middlewares';
import type {
  State as MainPanelState,
  SetState as MainPanelSetState,
} from './MainPanel/useCtxStore';
import type {
  State as SelectFilesState,
  SetState as SelectFilesSetState,
} from './SelectFiles/useCtxStore';
import type { LoadingItem } from '@/components/LoadingList';

interface Value {
  useMainPanelCtxStore: null | UseStore<MainPanelState, MainPanelSetState>;
  useSelectFilesCtxStore: null | UseStore<SelectFilesState, SelectFilesSetState>;
  config: null | LoadingItem;
}

export default createContext<Value>({
  useMainPanelCtxStore: null,
  useSelectFilesCtxStore: null,
  config: null,
});
