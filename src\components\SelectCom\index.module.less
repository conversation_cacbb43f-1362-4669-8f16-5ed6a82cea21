.headerStyle {
  display: none !important;
}

.body1 {
  border-radius: 12px 12px 0 0;
  padding: 0 !important;
  background: #F2F3F5;
}

.comSelectContainer {
  background: #F2F3F5;
  width: 100%;
  max-height: 458px;
  overflow-y: hidden;
  .comSelectHeader {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 15px;
    font-weight: 500;
    line-height: 21px;
    text-align: center;
    letter-spacing: 0;
    color: #000000;
    height: 72px;
    background: #FFFFFF;

    .closeButton {
      position: absolute;
      right: 20px;
      font-size: 12px;
      line-height: normal;
      color: rgba(0, 0, 0, 0.85);
      width: 34px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      background: #F2F2F2;
      cursor: pointer;
    }
  }

  .comSelectList {
    overflow: auto;
    max-height: 324px;
  }

  .listItem {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 54px;
    font-size: 16px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0;
    color: #1D2129;
    box-shadow: inset 0px -0.5px 0px 0px #E5E6EB;
    background: #FFFFFF;
    cursor: pointer;
  }

  .listItem:hover {
    background: #F2F2F2;
  }
}