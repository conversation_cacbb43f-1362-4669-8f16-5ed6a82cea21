import request from '../index';
// 回收站验证
export const validateRecyclebinAuth = (data: RecycleParams) => {
  return request.post({
    url: '/web-api/account/auth/recyclebinAuth',
    data,
  });
};
// 企业版回收站验证
export const validateRecyclebinAuthEE = (data: RecycleParams) => {
  return request.post({
    url: '/web-api/account/auth/enterprise/recyclebinAuth',
    data,
  });
};
// 永久删除和取消删除
export const permanentRemoveOrCancel = (data: any) => {
  return request.delete({
    url: '/web-api/library/recycleBin/permanentRemoveOrCancel/all',
    data,
  });
};
// 回收站各模块删除数量
export const countTempDelete = () => {
  return request.post({
    url: '/web-api/library/es/doc/countTempDelete',
  });
};
