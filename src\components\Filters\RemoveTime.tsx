import { useState } from 'react';
import type { Value } from './TimeFilter';
import TimeFilter from './TimeFilter';
import { Button } from 'antd';

interface Props {
  onOk: (value: Value) => void;
}

const Component = ({ onOk }: Props) => {
  const [value, setValue] = useState<Value>({});
  const change = (value: Value) => {
    setValue({ ...value });
  };
  const ok = () => {
    onOk(value);
  }; 

  return (
    <TimeFilter
      value={value}
      sections={[true, true, true, false]}
      onChange={change}
      footer={
        <>
          <span></span>
          <Button type="primary" disabled={!Object.keys(value).length} onClick={ok}>
            确定
          </Button>
        </>
      }
    />
  );
};

export default Component;
