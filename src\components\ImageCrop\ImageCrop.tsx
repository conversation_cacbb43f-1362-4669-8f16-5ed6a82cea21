import { uploadByPublic } from '@/api/library';
import { Button } from 'antd';
import html2canvas from 'html2canvas';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import ReactCrop, { type Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import styles from './ImageCrop.module.less';
type CropModel = { getBase64: () => void; uploadImg: (type: string) => void };
interface ImageCropType {
  src: string;
  onAdd: (value: string) => void;
  closeModal: () => void;
  aspect?: {
    width: number;
    height: number;
    ratio: number;
    isFixedsize: boolean;
  };
}
const ImageCrop: any = forwardRef<CropModel, ImageCropType>(
  (
    {
      src,
      onAdd,
      closeModal,
      aspect = {
        width: 150,
        height: 150,
        ratio: 1,
        isFixedsize: true,
      },
    },
    ref,
  ) => {
    // 这里设置了一个状态Crop，Crop是剪切框的初始数据，里面保存着XY坐标，宽度和高度
    const [crop, setCrop] = useState<Crop>({
      unit: 'px',
      x: 0,
      y: 0,
      width: aspect.width,
      height: aspect.height,
    });
    const reactCropRef = useRef(null);
    // canvasRef 是一个引用，用来获取canvas 的真实DOM
    const canvasRef = useRef<HTMLCanvasElement>(null);
    // imgRef 是一个引用，用来获取img 的真实DOM
    const imgRef = useRef<HTMLImageElement>(null);
    const [url, setUrl] = useState(null);
    useImperativeHandle(
      ref,
      () => ({
        getBase64,
        uploadImg,
      }),
      [],
    );
    useEffect(() => {
      if (aspect.isFixedsize) {
        const WH = getDivRealHtmlWH();
        setCrop({
          unit: 'px',
          x: (WH.width - WH.height) / 2,
          y: 0,
          width: WH.height,
          height: WH.height,
        });
      }
    }, []);
    const getDivRealHtmlWH = () => {
      const CropRef: any = reactCropRef.current;
      const style = window.getComputedStyle(CropRef.componentRef.current);
      const CropHeight: string = style.height.replace('px', '');
      const CropWidth: string = style.width.replace('px', '');
      return {
        width: Number(CropWidth),
        height: Number(CropHeight),
        CropDiv: CropRef.componentRef.current as HTMLDivElement,
      };
    };
    /* 
      这是一个回调函数，是在图片初次被加载时调用。
     */
    const onImageLoad = () => {
      imgRef.current!.crossOrigin = 'anonymous';
      //获取canvas真实dom
      const canvas: any = canvasRef.current;
      //获取img真实dom
      const image: any = imgRef.current;
      // 设置canvas 容器的宽度
      canvas.style.width = '200px';
      // 设置canvas 容器的高度
      canvas.style.height = '200px';
      // 放大我们的画布宽度
      canvas.width = 200 * devicePixelRatio;
      // 放大我们的画布高度
      canvas.height = 200 * devicePixelRatio;
      //context 可以简单的认为是画笔
      const context = canvas.getContext('2d');
      // width 是我们在真实图片上截取区域的宽度
      const width = (200 / image.width) * image.naturalWidth;
      // height 是我们在真实图片上截取区域的高度
      const height = (200 / image.height) * image.naturalHeight;
      // 进行渲染
      context.drawImage(image, 0, 0, width, height, 0, 0, canvas.width, canvas.height);
    };
    /* 
    这是一个回调函数，是在Crop位置发生位移的时候调用
  */
    const onCropChange = (c: any) => {
      const Crop = c;
      if (aspect.isFixedsize) {
        const canvasWidthHeight = getDivRealHtmlWH();
        Crop.width = canvasWidthHeight.height;
        Crop.height = canvasWidthHeight.height;
        Crop.x =
          c.x > canvasWidthHeight.height ? canvasWidthHeight.width - canvasWidthHeight.height : c.x;
        Crop.y = 0;
      }
      setCrop(Crop);
      // const canvas: any = canvasRef.current;
      // const image: any = imgRef.current;
      // canvas.style.width = '100px';
      // canvas.style.height = '100px';
      // // canvas.width = image.width * devicePixelRatio;
      // // canvas.height = image.height * devicePixelRatio;
      // canvas.width = 600;
      // canvas.height = 600;
      // const context = canvas.getContext('2d');
      // const width = Crop.width * (image.naturalWidth / image.width);
      // const height = Crop.height * (image.naturalHeight / image.height);
      // const x = Crop.x * (image.naturalWidth / image.width);
      // const y = Crop.y * (image.naturalHeight / image.height);
      // // context.drawImage(image, x, y, width, height, 0, 0, canvas.width, canvas.height);
      // context.drawImage(image, x, y, width, height, 0, 0, 600, 600);
    };

    // 将base64字符串转换为Blob对象
    function dataURLtoBlob(dataurl: any) {
      const arr = dataurl.split(',');
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    }

    // 获取画布内容转base64
    const getBase64 = () => {
      return canvasRef.current?.toDataURL('image/png') || '';
    };

    // 完成按钮
    const handleOk = async () => {
      const result = await captureScreenshot();
      onAdd(result.url);
      closeModal();
    };
    // 上传图片到文件服务器
    const uploadImg = async (bizType: string) => {
      const base64Image = getBase64();
      // 创建Blob对象
      const blob = dataURLtoBlob(base64Image);
      // console.log(777777777, base64Image, blob);
      const fileSize = blob.size / 1000 + '';
      // 创建FormData并附加文件
      const formData = new FormData();
      formData.append('file', blob, `avater${+new Date()}.png`); // 'image.png'是文件名
      formData.append('bizType', bizType); // 2-魔术相册，11-通讯录，18-账户管理
      // formData.append('title', '头像');
      // formData.append('fileType', 'png');
      // formData.append('fileFormatType', 'library_file_type_pic');
      // formData.append('fileSize', fileSize);

      const res: any = await uploadByPublic({
        data: formData,
        onUploadProgress: (event: any) => {},
      }).catch(() => {
        console.log('失败了！');
        return '';
      });
      console.log('成功了！', res.data);
      return res?.data;
    };
    const captureScreenshot = async () => {
      const { CropDiv } = getDivRealHtmlWH();
      const canvas = await html2canvas(CropDiv, { useCORS: true });
      const { x, y, width, height } = crop;
      // 创建一个新的 canvas 用于裁剪
      const croppedCanvas = document.createElement('canvas');
      croppedCanvas.width = width;
      croppedCanvas.height = height;
      const ctx = croppedCanvas.getContext('2d')!;

      // 复制原始 canvas 上的指定区域到新的 canvas
      ctx.drawImage(canvas, x, y, width, height, 0, 0, width, height);
      return { url: croppedCanvas.toDataURL('image/png') };
    };
    return (
      <div>
        <div className={styles.paper}>
          <ReactCrop
            crop={crop}
            aspect={aspect.ratio}
            minWidth={80}
            onChange={onCropChange}
            ref={reactCropRef}
            className={styles.cropper}
          >
            <img ref={imgRef} src={src}/>
          </ReactCrop>
          <div hidden>
            <canvas ref={canvasRef}></canvas>
          </div>
          <div className={styles.cropFooter}>
            <p>请调整需要的位置到取景框内</p>
            <div>
              <Button className="ml-4" onClick={closeModal}>
                取消
              </Button>
              <Button type="primary" className="ml-4" onClick={handleOk}>
                完成
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  },
);

export default ImageCrop;
