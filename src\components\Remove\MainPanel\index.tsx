import { libraryDataBankDeleteSelect, libraryRecycleRemoveOrCancel } from '@/api/library';
import MultiModal from '@/components/MultiModal';
import { Button, message } from 'antd';
import { useRef, useContext } from 'react';
import RemoveModal from '../RemoveModal';
import SelectFiles from './SelectFiles';
import ToolBar from './ToolBar';
import styles from './index.module.less';
import Context from '../Context';
import type { Config } from '../Context';

interface Props {
  onCancel?: () => void;
  onToolBarClick?: (type: string) => void;
}

const Component = ({ onCancel, onToolBarClick }: Props) => {
  const removeModalRef = useRef<{ open: () => void }>();
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [mainPanelOpen, selectedFileList] = useMainPanelCtxStore!((state) => [
    state.mainPanelOpen,
    state.selectedFileList,
  ]);
  const getFileFormatType = (config: Config) => {
    switch (config.module) {
      case 'audioPlay':
        return 'library_file_type_audio';
      case 'videoPlay':
        return 'library_file_type_video';
    }
  };
  const cancel = () => {
    useMainPanelCtxStore!.reset();
    if (onCancel) {
      onCancel();
    }
  };
  const ok = () => {
    removeModalRef.current?.open();
  };
  const removeOk = (next: () => void) => {
    if (config!.removeType === 'dataBank-select-remove') {
      return libraryDataBankDeleteSelect({
        idList: selectedFileList.map((item: any) => item.id),
      }).then((res: any) => {
        if (res.code === 0) {
          message.info('删除成功').then(() => {
            next();
            cancel();
          });
        }
      });
    }
    if (config!.removeType === 'recycle-select-remove') {
      return libraryRecycleRemoveOrCancel({
        idList: selectedFileList.map((item: any) => item.id),
        operateType: 2,
        startTime: undefined,
        endTime: undefined,
        fileFormatType: getFileFormatType(config!),
      }).then((res: any) => {
        if (res.code === 0) {
          message.info('删除成功').then(() => {
            next();
            cancel();
          });
        }
      });
    }
    if (config!.removeType === 'recycle-select-cancel') {
      return libraryRecycleRemoveOrCancel({
        idList: selectedFileList.map((item: any) => item.id),
        operateType: 0,
        startTime: undefined,
        endTime: undefined,
        fileFormatType: getFileFormatType(config!),
      }).then((res: any) => {
        if (res.code === 0) {
          message.info('取消成功').then(() => {
            next();
            cancel();
          });
        }
      });
    }
  };
  const title = (
    <div className={styles.title}>
      <span>已选文件</span>
      <span>
        已选择 <b>{selectedFileList.length}</b> 个
      </span>
    </div>
  );

  return (
    <>
      <MultiModal
        layoutGroup={`remove_${config?.module}_${config?.removeType}`}
        layoutClassName="modalRight"
        destroyOnClose={true}
        title={title}
        open={mainPanelOpen}
        onCancel={cancel}
        footer={[
          <Button key="1" type="primary" disabled={!selectedFileList.length} onClick={ok}>
            {config!.buttonText.replace('选择', '确认')}
          </Button>,
        ]}
      >
        <ToolBar onClick={onToolBarClick} />
        <SelectFiles />
      </MultiModal>
      <RemoveModal ref={removeModalRef} onOk={removeOk} />
    </>
  );
};

export default Component;
