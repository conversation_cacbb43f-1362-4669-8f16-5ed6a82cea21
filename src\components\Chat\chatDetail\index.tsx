import { getSingleMsgList } from '@/api/chat';
import MyAvatar from '@/components/Avatar';
import Header from '@/components/SmartSecretary/publicComponents/Header';
import useAppStore from '@/store/useAppStore';
import useUserStore from '@/store/useUserStore';
import { getDomRealWH, getWidth } from '@/utils/common';
import { Card, Drawer } from 'antd';
import classNames from 'classnames';
import { FC, useEffect, useState } from 'react';
import ChatDataManager from '../services/chat-data-manager';
import styles from './index.module.less';
import InputArea from './InputArea';

interface ChatProps {
  onClose: () => void;
  targetUser: any;
}
const ChatIndex: FC<ChatProps> = ({ onClose, targetUser = {} }) => {
  const smartSecretaryRoot = document.getElementById('smartSecretaryRoot');
  const { channel } = useAppStore((state: any) => state);
  const [userInfo] = useUserStore((state) => [state.userInfo]);
  const [open, setOpen] = useState(true);
  if (!smartSecretaryRoot) {
    return <div>smartSecretaryRoot不存在</div>;
  }
  const selfUsername = userInfo?.username;
  const [msgList, setMsgList] = useState<any[]>([]);

  const fetchList = async () => {
    try {
      const params = {
        pageNo: 1,
        pageSize: 10,
        targetId: targetUser.userId,
        oldTime: 1734680635762,
      };
      const list = await getSingleMsgList(params);
    } catch (error) {
      //
    }
  };

  useEffect(() => {
    fetchList();
  }, []);

  const onSendMsg = async (msg: string) => {
    const params = {
      sendUserId: userInfo?.id,
      sendUserName: userInfo?.username,
      receiveUserId: targetUser.userId,
      msgType: 1,
      content: msg,
      extra: '',
    };
    // const result = await sendSingleMsg(params);
    // setMsgList((prev) => [...prev, curMsg]);
    await ChatDataManager.sendMessage(params);
  };
  console.log('targetUser', targetUser);

  console.log('open', open);

  return (
    <Drawer
      title={
        <Header
          title={targetUser.contactName}
          onCancel={() => {
            setOpen(false);
            onClose();
          }}
        />
      }
      placement="right"
      closable={false}
      onClose={onClose}
      open={open}
      getContainer={false}
      width={getDomRealWH('#smartSecretaryRoot').width}
      footer={<InputArea onSend={onSendMsg} />}
    >
      <div className={styles.chatWindow}>
        <div
          className={styles.messageList}
          style={{ height: window.innerHeight - getWidth(channel === 'web' ? 300 : 140) }}
        >
          {msgList.map((item) => (
            <Card
              key={item.id}
              bordered={false}
              className={classNames(
                styles.chatCard,
                item.username === selfUsername ? styles.selfMessageCard : '',
              )}
            >
              <Card.Meta
                avatar={<MyAvatar userName={item.name} Avatar={''} isUser fontSize={12} />}
                title={`${item.name} ${item.username}`}
                description={<p>{item.msg}</p>}
              ></Card.Meta>
            </Card>
          ))}
        </div>
      </div>
      ,
    </Drawer>
  );
};
export default ChatIndex;
