import { dealInvite } from '@/api/audioVideo/videoMeeting';
import { refuse } from '@/api/audioVideo/webRTCSignaling';
import useJavaWebSocketStore from '@/store/useJavaWebSocketStore';
import getMediaUrl from '@/utils/getMediaUrl';
import getCamera from '@/webRTC/getCamera';
import { Button, message } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import VideoCall from '../VideoCall';
import VoiceCall from '../VoiceCall';
import AlertDialog from './AlertDialog';
import useAlertDialogStore from './AlertDialog/useAlertDialogStore';
import styles from './AudioVideoCallGlobal.module.less';
import useAudioVideoCallGlobalStore from './useAudioVideoCallGlobalStore';
interface callData {
  command: string;
  pullUrl: string;
  roomId: string;
  type: string;
  fromUser: any;
  sourceType: 1 | 2;
}
interface livingRoom {
  fromDeviceId: string;
  fromDeviceType: string;
  isAudio: boolean;
  liveId: number;
  liveRoomCreator: string;
  liveTitle: string;
  pullAssign: string;
  pullUrl: string;
}
interface meetingData {
  command: string;
  meetingTitle: string;
  meetingId: string;
  type: string;
  fromUserInfo: any;
}
let timer: ReturnType<typeof setTimeout>;
const AudioVideoCallGlobal = () => {
  const history = useNavigate();
  const location = useLocation();
  const [alertDialogOpen, alertDialogTitle] = useAlertDialogStore((state) => [
    state.alertDialogOpen,
    state.alertDialogTitle,
  ]);
  const [isBusyline] = useAudioVideoCallGlobalStore((state) => [state.isBusyline]);
  const [response] = useJavaWebSocketStore((state) => [state.response]);
  const [callRtcInfo, setCallRtcInfo] = useState<callData>({
    command: '',
    pullUrl: '',
    roomId: '',
    type: '',
    fromUser: {
      avatar: '',
      email: '',
      mobile: '',
      realName: '',
    },
    sourceType: 1,
  });
  const [meetingRtcInfo, setMeetingRtcInfo] = useState<meetingData>({
    command: '',
    meetingTitle: '',
    meetingId: '',
    type: '',
    fromUserInfo: {
      avatar: '',
      email: '',
      mobile: '',
      realName: '',
    },
  });
  const [livingRoomInfo, setLivingRoomInfo] = useState<livingRoom>();
  const [sendUserId, setSendUserId] = useState<string>('');
  const [isAudioCall, setIsAudioCall] = useState<boolean>(false);
  const [audioVideoCallModal, setAudioVideoCallModal] = useState<boolean>(false); // 音视频通话模态框展示
  const [open, setOpen] = useState(false);
  const [VideoMeetingModal, setVideoMeetingModal] = useState<boolean>(false); // 视频会议邀请展示
  const audio = useRef<HTMLAudioElement | null>(null);
  const [isRinged, setIsRinged] = useState<boolean>(false); //已接听提示
  const [isRingedText, setIsRingedText] = useState<string>(''); //其他设备接听处理
  const isOwnRefuse = useRef<boolean>(false);
  useEffect(() => {
    if (audio.current === null) {
      audio.current = new Audio(`${window.location.origin}/Freesound.mp3`);
      audio.current.volume = 1;
      audio.current.loop = true;
    }
    if (VideoMeetingModal || audioVideoCallModal) {
      audio.current.play();
    } else {
      audio.current.pause();
      audio.current = null;
    }
  }, [VideoMeetingModal, audioVideoCallModal, audio.current]);
  useEffect(() => {
    console.log(response);
    // 视频通话全局弹框
    if (response?.noticeType === 'VIDEO_CALL' || response?.noticeType === 'VOICE_CALL') {
      setIsAudioCall(response?.noticeType === 'VOICE_CALL');
      const { extra, sendUserId } = response;
      setSendUserId(sendUserId);
      if (extra) {
        try {
          const data = JSON.parse(extra) as callData;
          if (data.command === 'DIALING' && !isBusyline) {
            setCallRtcInfo(data);
            setAudioVideoCallModal(true);
            useJavaWebSocketStore.reset();
          } else if (data.command === 'CANCELLED' && !isBusyline && audioVideoCallModal) {
            message.info('对方已取消通话');
            setAudioVideoCallModal(false);
            useJavaWebSocketStore.reset();
            // case 'REFUSED':
            //   setIsCallOff(true);
            //   message.info('对方已拒绝通话');
          } else if (data.command === 'ACCEPTED' && audioVideoCallModal && !isBusyline) {
            setIsRingedText('已在其他设备接听');
            setIsRinged(true);
            setAudioVideoCallModal(false);
            useJavaWebSocketStore.reset();
          } else if (
            data.command === 'REFUSED' &&
            audioVideoCallModal &&
            !isOwnRefuse.current &&
            !isBusyline
          ) {
            setIsRingedText('已在其他设备拒绝');
            setIsRinged(true);
            setAudioVideoCallModal(false);
            useJavaWebSocketStore.reset();
          }
        } catch (error) {
          console.log(error);
        }
      }
    }
    // 直播全局处理
    if (response?.noticeType === 'LIVE' && location.pathname !== '/audioPlay/liveRoom') {
      //
      const { extra } = response;
      if (extra) {
        try {
          const data = JSON.parse(extra) as livingRoom;
          console.log(data);
          if (data.pullAssign === 'start') {
            //
            history('/audioPlay/liveRoom', {
              state: {
                id: data.liveId,
                title: data.liveTitle,
                pullUrl: data.pullUrl,
                isAduio: data.isAudio,
                isFull: true,
              },
            });
          }
        } catch (error) {
          console.log(error);
        }
      }
    }
    // 视频会议全局弹框
    if (response?.noticeType === 'VIDEO_CONFERENCE') {
      const { extra } = response;
      if (extra) {
        try {
          const data = JSON.parse(extra) as meetingData;
          console.log(data);
          if (data.command === 'INVITE' && !isBusyline) {
            setMeetingRtcInfo(data);
            setVideoMeetingModal(true);
            useJavaWebSocketStore.reset();
          } else if (
            data.command === 'JOINED' &&
            VideoMeetingModal &&
            !isOwnRefuse.current &&
            !isBusyline
          ) {
            setIsRingedText('已在其他设备接听');
            setIsRinged(true);
            setVideoMeetingModal(false);
            useJavaWebSocketStore.reset();
          } else if (data.command === 'END' && VideoMeetingModal) {
            setIsRingedText('会议已结束！');
            setIsRinged(true);
            setVideoMeetingModal(false);
            useJavaWebSocketStore.reset();
          }
        } catch (error) {
          console.log(error);
        }
      }
    }
    // 监听直播间 消息
    //useJavaWebSocketStore.reset();
  }, [response, audioVideoCallModal, VideoMeetingModal]);
  useEffect(() => {
    if (isRinged) {
      setTimeout(() => {
        setIsRinged(false);
      }, 5000);
    }
  }, [isRinged]);
  useEffect(() => {
    if (VideoMeetingModal) {
      timer = setTimeout(() => {
        setVideoMeetingModal(false);
        agreeOrRefusedMeeting(false);
      }, 30_000);
    }
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [meetingRtcInfo, VideoMeetingModal]);
  //拒绝一对一通话
  const refuseCall = useCallback(
    debounce(async () => {
      isOwnRefuse.current = true;
      await refuse({
        roomId: callRtcInfo.roomId,
        mediaType: isAudioCall ? 0 : 1,
      });
      setAudioVideoCallModal(false);
    }, 500),
    [callRtcInfo.roomId, isAudioCall],
  );
  const agreeCall = useCallback(
    debounce(async (flag) => {
      const result = await getCamera(flag);
      if (result.length === 0) {
        setAudioVideoCallModal(false);
        refuseCall();
        return false;
      }
      setOpen(true);
      setAudioVideoCallModal(false);
    }, 500),
    [callRtcInfo.roomId, isAudioCall],
  );
  // 同意或拒绝视频会议
  const agreeOrRefusedMeeting = useCallback(
    debounce(async (flg: boolean) => {
      if (flg) {
        isOwnRefuse.current = true;
        const result = await getCamera(false);
        if (result.length === 0) {
          setVideoMeetingModal(false);
          return false;
        }
      }
      setVideoMeetingModal(false);
      await dealInvite({
        meetingId: meetingRtcInfo.meetingId,
        flg,
      });
      if (flg) {
        history('/VideoMeeting/MeetingRoom', {
          state: {
            meetingId: meetingRtcInfo.meetingId,
            userType: 'normal',
          },
        });
      }
    }, 500),
    [meetingRtcInfo.meetingId],
  );
  return (
    <>
      {isRinged ? (
        <div className={styles.videoCallRingedModal}>
          <span />
          <div>{isRingedText}</div>
        </div>
      ) : null}
      {audioVideoCallModal ? (
        <div className={styles.videoCallModal}>
          <div className={styles.userInfo}>
            <img
              className={styles.userAvatar}
              src={
                callRtcInfo?.fromUser?.avatar
                  ? getMediaUrl(callRtcInfo?.fromUser?.avatar)
                  : `${window.location.origin}/avatar.png`
              }
            />
            <div>
              <div>
                {callRtcInfo?.fromUser?.realName}({callRtcInfo.fromUser?.username})
              </div>
              <span>邀请你{isAudioCall ? '语音' : '视频'}通话</span>
            </div>
          </div>
          <div className={styles.operateButList}>
            <span onClick={refuseCall}>拒绝</span>
            {isAudioCall ? (
              <span
                className={styles.audioTb}
                onClick={async () => {
                  agreeCall(true);
                }}
              >
                接受
              </span>
            ) : (
              <span
                className={styles.videoTb}
                onClick={async () => {
                  agreeCall(false);
                }}
              >
                接受{callRtcInfo.fromUser.sourceType}
              </span>
            )}
          </div>
        </div>
      ) : null}
      {callRtcInfo.roomId && open && !isAudioCall ? (
        <VideoCall
          title={
            '视频通话' +
            (callRtcInfo.sourceType === 2 ? callRtcInfo.fromUser?.realName : '正文输入')
          }
          roomId={callRtcInfo.roomId}
          open={open}
          sourceType={callRtcInfo.sourceType || 1}
          addressData={[
            {
              addressee: callRtcInfo.fromUser.email,
              mobile: callRtcInfo.fromUser.email,
              realName: callRtcInfo.fromUser.realName || callRtcInfo.fromUser.username,
              addresseeStatus: 1,
              addresseeName: '',
              userId: '',
              avatar: callRtcInfo.fromUser.avatar,
            },
          ]}
          isCalled={true}
          onCancel={() => setOpen(false)}
        />
      ) : null}
      {callRtcInfo.roomId && open && isAudioCall ? (
        <VoiceCall
          sourceType={callRtcInfo.sourceType || 1}
          title={
            '语音通话' +
            (callRtcInfo.sourceType === 2 ? callRtcInfo.fromUser?.realName : '正文输入')
          }
          roomId={callRtcInfo.roomId}
          open={open}
          addressData={[
            {
              addressee: callRtcInfo.fromUser.email,
              mobile: callRtcInfo.fromUser.email,
              realName: callRtcInfo.fromUser.realName || callRtcInfo.fromUser.username,
              addresseeStatus: 1,
              addresseeName: '',
              userId: '',
              avatar: callRtcInfo.fromUser.avatar,
            },
          ]}
          isCalled={true}
          onCancel={() => setOpen(false)}
        />
      ) : null}
      {VideoMeetingModal ? (
        <div className={styles.videoMeetingModal}>
          <div className={styles.userInfo}>
            <span />
            <div>
              {meetingRtcInfo?.fromUserInfo?.realName}({meetingRtcInfo.fromUserInfo.username}
              )邀请您加入视频会议，是否同意？
            </div>
          </div>
          <div className={styles.operateButList}>
            <Button
              color="primary"
              className={styles.refusedBut}
              onClick={() => {
                agreeOrRefusedMeeting(false);
              }}
            >
              拒绝
            </Button>
            <Button
              type="primary"
              onClick={() => {
                agreeOrRefusedMeeting(true);
              }}
            >
              同意
            </Button>
          </div>
        </div>
      ) : null}
      <AlertDialog open={alertDialogOpen} title={alertDialogTitle} />
    </>
  );
};

export default AudioVideoCallGlobal;
