// 人事列表入参
export interface GetHrPageParams {
  pageNo: number;
  pageSize: number;
  /*员工编号 */
  hrNumber?: string;
  /*姓名,身份证上真实名字 */
  realName?: string;
  /*工作手机 */
  workPhone?: string;
  /*模糊查询字段,检索的字段包括：编号、账号、部门、职务、身份证号、民族、籍贯、毕业院校、第一学历、最高学历 */
  conditionVale?: string;
  /*在职状态  0-在职  1-离职 */
  status?: number;
}
export const sexEnum = {
  1: '男',
  2: '女',
} as const;
export interface User {
  /*唯一标记 */
  id: number;
  /*员工编号 */
  hrNumber: string;
  /*姓名 */
  realName: string;
  /*账号ID，以账户管理模块yb_sys_users中的为准 */
  username: string;
  /*用户id */
  userId: number;
  /*工作手机 */
  workPhone: string;
  /*账户类型（角色名称） */
  roleName: string;
  /*部门id */
  deptId: number;
  /*部门名称 */
  deptName: string;
  /*职务id */
  postId: number;
  /*职务 */
  postName: string;
  /*生日日期 */
  birthday: string;
  /*身份证号 */
  identityCard: string;
  /*性别  1-男，2-女 */
  sex: keyof typeof sexEnum;
  /*民族 */
  nation: string;
  /*籍贯 */
  nativePlace: string;
  /*身份证有效期 */
  idCardExpire: string;
  /*身份证住址 */
  address: string;
  /*签发机关 */
  issuanceOrganization: string;
  /*邮箱 */
  email: string;
  /*毕业院校 */
  graduateSchool: string;
  /*第一学历 */
  firstDegree: number;
  /*最高学历 */
  highDegree: number;
  /*毕业时间（第一学历） */
  graduateTimeFirst: string;
  /*毕业时间（最高学历） */
  graduateTimeHigh: string;
  /*入职时间 */
  entryDate: string;
  /*转正时间 */
  confirmationDate: string;
  /*离职时间 */
  resignationDate: string;
  /*在职状态  0-在职  1-离职 */
  status: number;
  /*修改时间 */
  updateTime: string;
  /*修改人id */
  updater: string;
  /*最后操作人姓名 */
  updaterName: string;
  /* */
  transMap: Record<string, unknown>;
  source: string;
  [key: string]: any;
}
export interface UserListInfo extends User {
  hrHistoryDO: Partial<User>;
}
export interface GetHrPageRes {
  /*数据 */
  list: User[];
  /*总量 */
  total: number;
}

// 目录入参
export interface GetUserCatalog {
  hrId: number;
}
// 目录列表返回值
export interface UserCatalog {
  id: number;
  catalogName: string;
  createTime?: string;
  /*内置目录唯一标记id，外键 */
  catalogId: number;
  /*目录排序 */
  catalogSort?: number;
  /*来源，0-系统内置，1-自定义 */
  source: number;
  /*目录下图片数量 */
  picNumber: number;
  /*自定义页码 */
  pageNo?: number;
  /*目录状态 insert、update、delelte */
  catalogStatus?: 'insert' | 'update' | 'delete' | 'default';
}
// 目录下图片入参
export interface GetUserCatalogPic {
  hrCatalogId: number;
  id: number;
}
// 目录下图片响应接口
export interface CatalogPicInUser {
  id: number;
  /*图片地址 */
  picUrl: string;
  createTime?: Record<string, unknown>;
  /*人事目录表的唯一标记，外键 */
  hrCatalogId?: number;
  /*图片排序 */
  picSort?: number;
}
export interface DataRes {
  list: any[];
  [key: string]: any;
  total: number;
}
// 列表公共返回值
export interface ListCommonRes {
  code: number;
  data: DataRes;
  msg: string;
  traceId: string;
}

export interface GetShareHrListParams {
  pageNo: number;
  pageSize: number;
  fuzzySearchKeyList: [];
}

export interface ShareHrListParams {
  hrId: string;
  userIdList: string[];
}
