import { options } from '@/api/mail/chat/mailModels';
import { FC } from 'react';
type parentProps = {
  status: number;
};

const ItemSpan: FC<parentProps> = ({ status }) => {
  return options.map((i) => {
    if (+i.value == status) {
      return (
        // <span key={i.value} className={`${styles[`${i.styles}`]} ${styles.item}`}>
        //   {i.label}
        // </span>
        <span key={i.value}>{i.label}</span>
      );
    }
  });
};

export default ItemSpan;
