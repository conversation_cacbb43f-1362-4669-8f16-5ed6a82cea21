import { formatFileSize, getWidth } from '@/utils/common';
import { ColumnType } from 'antd/es/table';
import dayjs from 'dayjs';

export const COLUMN_WIDTH = Object.freeze({
  INDEX: getWidth(70), // 序号
  NAME: getWidth(90), // 姓名
  TIME: getWidth(180), // 时间
  ACCOUNT: getWidth(100), // 账号
  MOBILE: getWidth(100), // 手机号
  CHECK: getWidth(110), // 扣考核绩效
  CACCOUNT: getWidth(130) // 考勤账号
});

export const COLUMN = Object.freeze({
  // 序号(通过传入分页大小、页码计算)
  INDEX: (pageSize = 0, pageNo = 0): ColumnType<any> => ({
    title: '序号',
    key: 'index',
    align: 'center',
    render: (_: any, _record: any, index: number) =>
      `${pageSize * (pageNo - 1) + index + 1}`.padStart(2, '0'),
    width: COLUMN_WIDTH.INDEX,
  }),
  // 创建时间
  CREATE_TIME: {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: COLUMN_WIDTH.TIME,
    render: (createTime: any) => dayjs(createTime).format('YYYY-MM-DD HH:mm:ss'),
  },
  // 文件大小
  FILE_SIZE: (key = 'fileSize') => ({
    title: '文件大小',
    key: key,
    dataIndex: key,
    render: (fileSize: any) => formatFileSize(fileSize, 0),
  }),
});

/**
 * 租户类型
 */
export enum TenantType {
  GENERAL = 1, // 综合版
  ENTERPRISE = 2, // 企业版
}

/**
 * 用户类型
 */
export enum UserType {
  NORMAL = 0, // 母账号
  CHILD = 1, // 子账号
}

/**
 * 分页查询参数
 */
export interface Page {
  pageNo: number;
  pageSize: number;
}

/**
 * 办公模式
 */
export enum WorkMode {
  CLOSED = 0, // 全封闭
  HALF_OPEN = 1, // 半开放
  OPEN = 2, // 全开放
  FIXED_CLOSED = 3, // 固定办公+全封闭办工
}

/**
 * 办公模式字典
 */
export const WORK_MODE_DIC = {
  [WorkMode.CLOSED]: '全封闭',
  [WorkMode.HALF_OPEN]: '半开放',
  [WorkMode.OPEN]: '全开放',
  [WorkMode.FIXED_CLOSED]: '固定办公+全封闭办工',
};
