import request from '../index';

// 登录
export const loginApi = (data: any, noTip?: boolean) => {
  return request.post({
    url: '/web-api/account/auth/login',
    headers: {
      Notip: noTip,
    },
    data,
  });
};
// 登出
export const logout = (data: any) => {
  return request.post({
    url: '/web-api/account/auth/logout',
    data,
  });
};
// 注册
export const registerApi = (data: any) => {
  return request.post({
    url: '/web-api/account/auth/register',
    data,
  });
};
// 修改用户密码
export const updatePassword = (data: any, noTip?: boolean) => {
  return request.post({
    url: '/web-api/account/auth/updatePassword',
    headers: {
      Notip: noTip,
    },
    data,
  });
};
// 发送短信接口
// 短信类型：3重置密码  5用户注册、6修改备忘手机、7修改工作手机、8新工作手机
export const getSmsCodeApi = (data: any) => {
  return new Promise((resolve, reject) => {
    request
      .post({
        url: '/web-api/account/auth/sendSmsCode',
        data,
      })
      .then((res: any) => {
        const smsRegx = /^\d{4,}$/;
        if (smsRegx.test(res.data)) {
          window.alert(`短信验证码为${res.data}。`);
        }
        return resolve(res);
      })
      .catch((err) => {
        return reject(err);
      });
  });
};
// 重置密码接口
export const restPasswordApi = (data: any) => {
  return request.post({
    url: '/web-api/account/auth/resetPassword',
    data,
  });
};
// 登录密码验证
export const authPassword = (data: any, noTip?: boolean) => {
  return request.post({
    url: '/web-api/account/auth/authPassword',
    headers: {
      Notip: noTip,
    },
    data,
  });
};
// 获取登录用户详情
export const getUserInfo = (data: any) => {
  return request.get({
    url: '/web-api/account/manage/getUserInfo',
    params: data,
  });
};
// 根据手机号码获取账号
export const getUserNameByMobile = (data: any) => {
  return request.post({
    url: '/web-api/account/manage/getUserNameByMobile',
    data,
  });
};
// 判断手机号是否注册
export const isRegister = (data: any) => {
  return request.post({
    url: '/web-api/account/auth/isMobileRegister',
    data,
  });
};
// 判断手机号是否在企业内注册或当前母账号下作为工作手机或备用手机,true:已注册，false:未注册
export const isMobileRegisterOfTenant = (data: any) => {
  return request.post({
    url: '/web-api/account/auth/isMobileRegisterOfTenant',
    data,
  });
};

// 登录回顾
// 获得系统用户登录记录分页
export const getLoginLog = (data: any) => {
  return request.get({
    url: '/web-api/account/loginReview/loginLog',
    params: data,
  });
};

// 根据手机号生成账号
export const generateUsernameByMobile = (data: any) => {
  return request.post({
    url: '/web-api/account/auth/generateUsernameByMobile',
    data,
  });
};

// 判断手机号是否可注册(企业版),true:可注册，抛异常:不可注册
export const checkMobileCanRegister = (data: any) => {
  return request.post({
    url: '/web-api/account/auth/checkMobileCanRegister',
    data,
  });
};

// 检查手机号与账号是否匹配(重置密码)：true 匹配， 抛异常：不匹配-提示异常信息
export const checkMobileAndUsernameMatch = (data: any) => {
  return request.post({
    url: '/web-api/account/auth/checkMobileAndUsernameMatch',
    data,
  });
};
// 检查当前设备是否固定办公电脑
export const isFixedOfficeComputer = () => {
  return request.get({
    url: '/web-api/account/auth/isFixedOfficeComputer',
  });
};
