import getMediaUrl from '@/utils/getMediaUrl';
import { Image } from 'antd';
import type { File } from '../interface';
import styles from './ImagePreview.module.less';

interface Props {
  file: File;
}

const ImagePreview = ({ file }: Props) => {
  const { visitPath, secretKey } = file;
  const src = `${getMediaUrl(visitPath!, secretKey)}`;
  return (
    <div className={styles.ImagePreview}>
      <Image src={src} preview={{ mask: <></> }} />
      {/* <img src={src} /> */}
    </div>
  );
};

export default ImagePreview;
