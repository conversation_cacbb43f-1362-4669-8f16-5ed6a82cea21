import { Col, Flex, Row, Typography } from 'antd';
import { FC } from 'react';
import styles from './index.module.less';
export const MyTitle: FC = () => {
  return (
    <>
      <Row>
        <Col span={4} className="text-base font-bold">
          <Flex justify={'center'}>姓名</Flex>
        </Col>
        <Col span={5} className="text-base font-bold">
          <Flex justify={'center'}>账号</Flex>
        </Col>
        <Col span={5} className="text-base font-bold">
          <Flex justify={'center'}>手机号</Flex>
        </Col>
        <Col span={10} className="text-base font-bold">
          <Flex justify={'center'}>邮箱</Flex>
        </Col>
      </Row>
    </>
  );
};
export const MyContent: FC<{
  data: {
    contactName: string;
    username: string;
    email: string;
    mobile: string;
    isFriend: boolean;
  };
}> = ({ data }) => {
  const { contactName, username, email, mobile } = data;
  const { Text } = Typography;
  return (
    <>
      <button className={styles.customBlockBut} disabled={data.isFriend}>
        <Row>
          <Col span={4}>
            <span style={{ whiteSpace: 'wrap', wordBreak: 'break-all' }}>{contactName}</span>
          </Col>
          <Col span={5}>{username}</Col>
          <Col span={5}>{mobile}</Col>
          <Col span={10} title={email}>
            <Text ellipsis>{email}</Text>
          </Col>
        </Row>
      </button>
    </>
  );
};

// 优化清空输入框，还展示下拉选项问题
export const timeOutFn = (fn: any) => {
  setTimeout(() => {
    fn();
  }, 1000);
};
