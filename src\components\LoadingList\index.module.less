.loadingList {
  position: fixed;
  z-index: 99999;
  top: 60px;
  right: 0;
  border-radius: 6px 0 0 6px;
  box-shadow:
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
  height: 52px;
  overflow: hidden;
  transition: height 0.6s cubic-bezier(0.08, 0.82, 0.17, 1);

  &.expend {
    height: auto;
  }

  .item {
    display: flex;
    align-items: center;
    flex-grow: 0;
    flex-shrink: 0;
    padding: 8px;
    border-bottom: 1px solid #eee;
    background-color: #fff;
    opacity: 1;    
    transition:
      opacity 0.6s ease,
      background-color 0.6s ease;

    &.removed {
      opacity: 0;      
    }
    &:last-child {
      border-bottom-color: transparent;
    }
    &:hover {
      background-color: #eee;
    }
  }

  .icon {
    margin-right: 8px;
    img {
      width: auto;
      height: 36px;
    }
  }

  .text {
    width: 160px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .status {
    color: #3d5afe;
  }

  .dropdown {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background-color: #3d5afe;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 10px;
    height: 14px;
    width: 28px;
    margin-left: 8px;
  }
}
