.cosSpan {
  display: inline-block;
  min-width: 40px;
  height: 18px !important;
  border-radius: 4px;
  background: #d3d3d3;
  cursor: pointer;
  font-size: 12px;
  line-height: 18px !important;
  text-align: center;
  color: #4d4d4d;
  margin-left: 3px;
  margin-bottom: 5px;
}

.hoverSpan:hover {
  cursor: pointer;
  color: red;
}

.voSpan {
  display: inline-block;
  max-width: 330px; //显示宽度
  white-space: nowrap; //文本不换行。
  overflow: hidden; //超过部分隐藏
  text-overflow: ellipsis; //超过部分用...代替
}

.conDiv {
  display: flex;
  margin-bottom: 5px;
}

.consDiv {
  margin-bottom: 5px;
}
