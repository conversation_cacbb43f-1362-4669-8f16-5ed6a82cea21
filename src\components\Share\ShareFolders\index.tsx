import MultiModal from '@/components/MultiModal';
import { Button } from 'antd';
import { useContext, useEffect, useMemo } from 'react';
import Context from '../Context';
import Filter from './Filter';
import List from './List';
import styles from './index.module.less';

const Component = () => {
  const { useShareFoldersCtxStore, useMainPanelCtxStore, config } = useContext(Context);
  const [shareFoldersOpen, setShareFoldersOpen, list, selectedList] = useShareFoldersCtxStore!(
    (state) => [
      state.shareFoldersOpen,
      state.setShareFoldersOpen,
      state.list,
      state.selectedList,
      state.setSelectedList,
    ],
  );
  const [setSharedFolderList, sharedFolderMap, setSharedFolderMap] = useMainPanelCtxStore!(
    (state) => [state.setSharedFolderList, state.sharedFolderMap, state.setSharedFolderMap],
  );
  const okEnabled = useMemo(() => {
    return selectedList.some((item: any) => !sharedFolderMap[item.id]);
  }, [selectedList, sharedFolderMap]);
  const cancel = () => {
    setShareFoldersOpen(false);
  };
  const ok = () => {
    const map = { ...sharedFolderMap };
    selectedList.forEach((item: any) => {
      if (!map[item.id]) {
        map[item.id] = item;
      }
    });
    setSharedFolderMap(map);
    setSharedFolderList(Object.values(map));
  };
  useEffect(() => {
    if (shareFoldersOpen === false) {
      useShareFoldersCtxStore!.reset();
    }
  }, [shareFoldersOpen]);
  const title = (
    <div className={styles.title}>
      <span>选择共享群</span>
      <span>
        共计 <b>{list.length}</b> 条，已选择 <b>{selectedList.length}</b> 条
      </span>
    </div>
  );

  return (
    <MultiModal
      layoutGroup={`share_${config?.module}`}
      layoutClassName="modalLeft"
      destroyOnClose={true}
      title={title}
      open={shareFoldersOpen}
      onCancel={cancel}
      footer={[
        <Button key="submit" type="primary" disabled={!okEnabled} onClick={ok}>
          确认选择
        </Button>,
      ]}
    >
      <Filter />
      <List />
    </MultiModal>
  );
};

export default Component;
