.SelectDownloadLocation {
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200;
  .containerBox {
    width: 550px;
    height: 275px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0px 20px 40px 0px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    * {
      box-sizing: border-box;
    }
    .header {
      width: 100%;
      height: 62px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      & > span {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-size: 20px;
        color: #4d70fe;
        padding-right: 20px;
        font-weight: 600;
      }
      .closeBut {
        width: 60px;
        height: 32px;
        border-radius: 4px;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #3d5afe;
        border: 1px solid #3d5afe;
        cursor: pointer;
      }
    }
    & > main {
      width: 100%;
      height: 144px;
      padding: 12px 32px;
      font-size: 14px;
      & > div {
        display: flex;
        align-items: center;
        height: 60px;
      }
      .selectItem {
        flex: 1;
        height: 32px;
        border: none !important;
        :global {
          .ant-select-selector,
          .ant-select-focused,
          .ant-select-outlined {
            border: none !important;
            box-shadow: none !important;
            background-color: #f3f3f5 !important;
            &:focus {
              border: none !important;
              box-shadow: none !important;
            }
            input {
              &:focus {
                border: none !important;
              }
            }
          }
        }
      }
      .inputItem {
        flex: 1;
        height: 32px;
      }
    }
    & > footer {
      width: 100%;
      & > div {
        width: 146px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0 auto;
        span {
          width: 68px;
          height: 40px;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          &:first-of-type{
            background-color: #3d5afe;
            color: #fff;
          }
          &:last-of-type{
            color: #3d5afe;
            border: 1px solid #3d5afe;
          }
        }
      }
    }
  }
}
.popupClassName {
  border: 1px solid #4d70f1;
  :global {
    .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
      background-color: #e8eaf6 !important;
    }
  }
}
