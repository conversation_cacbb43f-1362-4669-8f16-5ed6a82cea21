import { useMemo } from 'react';
import TimeFilter from './TimeFilterOther';

const Component = ({ setSelectedKeys, selectedKeys, confirm, close, clearFilters }: any) => {
  const value = useMemo(() => {
    return selectedKeys[0] ?? {};
  }, [selectedKeys]);
  const change = (value: any) => {
    if (Object.keys(value).find((key) => value[key])) {
      setSelectedKeys([
        {
          ...value,
        },
      ]);
    } else {
      setSelectedKeys([]);
    }
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
    } else if (type === 'ok') {
      confirm();
      close();
    }
  };

  return <TimeFilter value={value} onChange={change} onSubmit={submit} />;
};

export default Component;
