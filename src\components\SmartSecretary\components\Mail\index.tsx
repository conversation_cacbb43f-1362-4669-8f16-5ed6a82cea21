import { FC, useContext, useEffect, useState } from 'react';
import { Context } from '../../context';
import MailDetail from './Detail';
import MailList from './List';
const Mail: FC<any> = () => {
  const { rootDom, mailType, shareGroupId } = useContext(Context);
  const [showDetail, setShowDetail] = useState(false);
  const [addressee, setAddressee] = useState('');
  const [headName, setHeadName] = useState('');
  const [groupId, setGroupId] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const goDetail = (item: any, index: number) => {
    console.log('item-index', item, index);
    setAddressee(item.addressee);
    setShowDetail(true);
    setHeadName(
      item.groupId ? item.groupName : item.mailStatus === 1 ? item.addresseeName : item.senderName,
    );
    setGroupId(item.groupId);
    setSelectedIndex(index);
  };
  const hideDetail = () => {
    setShowDetail(false);
  };
  useEffect(() => {
    if (!mailType || mailType.index === 1000) {
      return;
    } else {
      setShowDetail(true);
      setGroupId('');
      setHeadName('');
      setAddressee('');
      setSelectedIndex(0);
    }
  }, [mailType]);
  return (
    <Context.Provider
      value={{
        rootDom,
        goDetail,
        hideDetail,
        mailType: mailType || { index: 1000 },
      }}
    >
      {shareGroupId ? undefined : <MailList />}
      {showDetail || shareGroupId ? (
        <MailDetail headName={headName} shareGroupId={shareGroupId} selectedIndex={selectedIndex} />
      ) : undefined}
    </Context.Provider>
  );
};
export default Mail;
