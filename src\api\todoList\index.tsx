import request from '../index';
//  获得待办事项列表
export const getTodoList = (data: any) => {
  return request.post({
    url: '/web-api/contacts/sys-todo/page',
    data,
  });
};

// 更新待办事项
export const updateTodoList = (data: any) => {
  return request.put({
    url: '/web-api/contacts/sys-todo/update',
    data,
  });
};

// 重新发起视频会议
export const meetingReissue = (data: any) => {
  return request.post({
    url: '/web-api/media/meeting/reissue',
    data,
  });
};
