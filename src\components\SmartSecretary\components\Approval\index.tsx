import { getApprovalListHome, getConfigTree } from '@/api/Approval';
import { DataRes } from '@/api/employee/employee.type';
import approvalIcon from '@/assets/images/home/<USER>/approvalIcon.png';
import Drawer from '@/components/Drawer';
import MobileHeaderTitle from '@/components/SmartSecretary/publicComponents/Header';
import useAppStore from '@/store/useAppStore';
import useUserStore from '@/store/useUserStore';
import { getWidth } from '@/utils/common';
import { DownOutlined } from '@ant-design/icons';
import { Button, Divider, Dropdown, Flex, Image, List, MenuProps, message, Space, Tag } from 'antd';
import { FC, useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useLocation, useNavigate } from 'react-router-dom';
import ApprovalDetail from './ApprovalDetail';
import styles from './index.module.less';
import InitiateApproval from './InitiateApproval';
const Approval: FC<any> = () => {
  const { channel } = useAppStore((state: any) => state);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize] = useState(10);
  const [dataSource, setDataSource] = useState<any>([]);
  const [queryType, setQueryType] = useState(1);
  const [showInitiateApproval, setShowInitiateApproval] = useState(false);
  const [showApprovalDetail, setShowApprovalDetail] = useState(false);
  const [approvalDetailId, setApprovalDetailId] = useState('');
  const [approvalDetailStatus, setApprovalDetailStatus] = useState(0);
  const userInfo = useUserStore((state) => state.userInfo);
  const location = useLocation();
  const navigate = useNavigate();
  const [approvalTypeOptions, setApprovalTypeOptions] = useState([]); // 审批类别
  const approvalStatusOptions: MenuProps['items'] = [
    { key: '1', label: '已通过' },
    { key: '2', label: '审批中' },
    { key: '3', label: '被退回' },
  ]; // 审批状态

  const approvalNatureOptions: MenuProps['items'] = [
    { key: '1', label: '我发起的' },
    { key: '2', label: '中间审批' },
  ]; // 审批性质
  const [approvalNatureSelected, setApprovalNatureSelected] = useState('');

  const handleClick = (item: any) => {
    message.warning('功能正在开发中...');
  };
  const getTagColor = (status: number) => {
    switch (status) {
      case 1:
        return 'orange';
      case 2:
        return 'success';
      case 3:
        return 'error';
      default:
        return 'processing';
    }
  };
  const getApprovalStatus = (status: number) => {
    switch (status) {
      case 1:
        return '待审批';
      case 2:
        return '已审批';
      case 3:
        return '退回';
      default:
        return '状态未知';
    }
  };
  const getFlatList = (data: any) => {
    return data.flatMap((item: any) => {
      if (item.children.length > 0) {
        const children = getFlatList(item.children);
        return [item, ...children];
      } else {
        return item;
      }
    });
  };
  const getConfigList = () => {
    getConfigTree().then((res: any) => {
      const mainData = (res.data || []).flatMap((item: any) => item.tree || []);
      // const list = getFlatList(mainData);
      console.log('mainData', mainData);
      setApprovalTypeOptions(mainData);
    });
  };
  const fetchList = async () => {
    try {
      // setLoading(true);
      const params: any = { pageNo, pageSize };
      if (approvalNatureSelected) params.queryType = approvalNatureSelected;
      const { data } = (await getApprovalListHome(params)) as {
        data: DataRes;
      };
      pageNo === 1 ? setDataSource(data.list) : setDataSource([...dataSource, ...data.list]);
      if (dataSource.length + data.list.length >= data.total) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      setTotal(data.total);
    } catch (error) {
      // setLoading(false);
    }
  };
  const fetchNextList = () => {
    if (!dataSource.length) return;
    setPageNo(pageNo + 1);
  };
  const resetFetchList = () => {
    if (pageNo === 1) {
      fetchList();
    } else {
      setPageNo(1);
    }
  };
  useEffect(() => {
    console.log('文件格式变化触发数据请求');
    resetFetchList();
  }, [approvalNatureSelected]);

  useEffect(() => {
    fetchList();
  }, [pageNo]);

  // 监听带参数跳转来的
  useEffect(() => {
    if (location.state && location.state.activeTab) {
      setShowInitiateApproval(true);
    }
  }, [location]);
  useEffect(() => {
    getConfigList();
  }, []);
  return (
    <div className={styles.ApprovalContainer}>
      <div className={styles.headRow}>
        {/* <div
          className={classNames(styles.headBtn, queryType === 1 ? styles.active : '')}
          onClick={() => {
            setQueryType(queryType !== 1 ? 1 : 0);
            setDataSource([]);
          }}
        >
          我发起的
        </div>
        <div
          className={classNames(styles.headBtn, queryType === 2 ? styles.active : '')}
          onClick={() => {
            setQueryType(queryType !== 2 ? 2 : 0);
            setDataSource([]);
          }}
        >
          中间审批
        </div> */}
        <div className={styles.btnRow}>
          <div
            className={styles.headBtn}
            onClick={() => {
              setShowInitiateApproval(true);
            }}
          >
            发起审批
          </div>
        </div>
        <Flex className={styles.filterRow}>
          <Dropdown
            menu={{
              items: approvalStatusOptions,
              selectable: true,
              selectedKeys: [],
              onClick: ({ key }) => {},
            }}
            className={styles.filterItem}
          >
            <Space>
              审批状态
              <DownOutlined style={{ fontSize: 10 }} />
            </Space>
          </Dropdown>
          <Dropdown
            menu={{
              items: approvalTypeOptions,
              selectable: true,
              selectedKeys: [],
              onClick: ({ key }) => {},
            }}
            className={styles.filterItem}
          >
            <Space>
              审批类别
              <DownOutlined style={{ fontSize: 10 }} />
            </Space>
          </Dropdown>
          <Dropdown
            menu={{
              items: approvalNatureOptions,
              selectable: true,
              multiple: true,
              selectedKeys: [approvalNatureSelected],
              onClick: ({ key }) => {
                setApprovalNatureSelected(approvalNatureSelected !== key ? key : '');
              },
            }}
            className={styles.filterItem}
          >
            <Space>
              审批性质
              <DownOutlined style={{ fontSize: 10 }} />
            </Space>
          </Dropdown>
        </Flex>
      </div>
      <div className={styles.todoList}>
        <div
          id="scrollableDivApprovalList"
          className={styles.scrollableDiv}
          style={{ height: window.innerHeight - getWidth(channel === 'web' ? 470 : 405) }}
        >
          <InfiniteScroll
            dataLength={dataSource.length} // 已加载的数据长度
            next={() => fetchNextList()} // 加载更多数据的函数
            hasMore={hasMore} // 是否还有更多数据
            loader={false}
            // loader={<div>加载中...</div>} // 加载中的提示信息
            endMessage={dataSource.length ? <Divider plain>全部加载完毕</Divider> : <></>}
            scrollThreshold="100px"
            scrollableTarget="scrollableDivApprovalList"
          >
            <List
              className={styles.todoList}
              itemLayout="horizontal"
              dataSource={dataSource}
              // loadMore={loadMore}
              renderItem={(item: any) => (
                <List.Item className={styles.listContentItem}>
                  <List.Item.Meta
                    avatar={
                      <div className={styles.leftContent}>
                        <Image
                          src={approvalIcon}
                          preview={false}
                          className={styles.webIcon}
                        ></Image>
                      </div>
                    }
                    title={
                      <div className={styles.titleLine}>
                        <div className={styles.title}>
                          {item.approvalFullName}{' '}
                          <Tag bordered={false} color={getTagColor(item.status)} className="ml-2">
                            {getApprovalStatus(item.status)}
                          </Tag>
                        </div>
                        <div className={styles.time}>
                          {/* status审批状态 1待审批 2已审批 3退回 */}
                          {item.status !== 2 && item.fromUserName === userInfo?.username ? (
                            <Space>
                              <Button
                                className={styles.btnDefault}
                                onClick={() => {
                                  message.warning('功能正在开发中...');
                                }}
                                size="small"
                              >
                                退回
                              </Button>
                              <Button
                                className={styles.btnPrimary}
                                onClick={() => {
                                  setApprovalDetailId(item.id);
                                  setShowApprovalDetail(true);
                                  setApprovalDetailStatus(2);
                                }}
                                type="primary"
                                size="small"
                              >
                                去审批
                              </Button>
                            </Space>
                          ) : (
                            <Space>
                              <Button
                                className={styles.btnDefault}
                                onClick={() => {
                                  console.log(item);
                                  setApprovalDetailId(item.id);
                                  setShowApprovalDetail(true);
                                  setApprovalDetailStatus(0);
                                }}
                                size="small"
                              >
                                查看
                              </Button>
                            </Space>
                          )}
                        </div>
                      </div>
                    }
                    description={
                      <div className={styles.contentLine}>
                        <div className={styles.content}>
                          <div>
                            {item.initUserRealName} {item.initUserName}
                          </div>
                        </div>
                        <div>{item.createTime}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </InfiniteScroll>
        </div>
      </div>
      <Drawer
        destroyOnClose={true}
        title={
          <MobileHeaderTitle
            title="发起审批"
            onCancel={() => {
              setShowInitiateApproval(false);
            }}
          />
        }
        open={showInitiateApproval}
        onClose={() => setShowInitiateApproval(false)}
      >
        <InitiateApproval
          onSubmit={() => {
            resetFetchList();
            setShowInitiateApproval(false);
            // 处理完成后重置状态
            navigate(location.pathname, { replace: true });
          }}
        />
      </Drawer>
      <ApprovalDetail
        open={showApprovalDetail}
        approvalId={approvalDetailId}
        approvalStatus={approvalDetailStatus}
        close={() => {
          setShowApprovalDetail(false);
        }}
      ></ApprovalDetail>
    </div>
  );
};
export default Approval;
