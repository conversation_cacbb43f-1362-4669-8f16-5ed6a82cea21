import {
  getFileFormatTypeFilterProps,
  getFileSizeFilterProps,
  getLocalSourceFilterProps,
} from '@/components/Filters';
import { formatFileSize, getWidth } from '@/utils/common';
import { Tooltip } from 'antd';

export const getColumns = (hasFilters: boolean, config: any) => {
  return [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: getWidth(4.5),
      render: (value: any, row: any, index: number) => index + 1,
    },
    {
      title: '姓名',
      dataIndex: 'realName',
      key: 'realName',
      width: getWidth(7.5),
      ellipsis: true,
    },
    {
      title: '文件名称',
      dataIndex: 'fileName',
      key: 'fileName',
      width: getWidth(12.0),
      render: (value: any, row: any) => (
        <Tooltip placement="bottom" title={row.filePath} arrow>
          <div className="ant-table-cell-ellipsis">{value}</div>
        </Tooltip>
      ),
    },
    {
      title: '文件来源',
      dataIndex: 'fileSource',
      key: 'fileSource',
      width: getWidth(10.0),
      ...(hasFilters ? getLocalSourceFilterProps({ module: 'desktopFile' }) : {}),
      render: (value: any, row: any) => {
        return row.fileSourceName;
      },
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: getWidth(10.0),
      ...(hasFilters ? getFileSizeFilterProps() : {}),
      render: (value: any, row: any) => {
        return formatFileSize(row.fileSize);
      },
    },
    {
      title: '文件格式',
      dataIndex: 'fileFormatType',
      key: 'fileFormatType',
      width: getWidth(10.0),
      ellipsis: true,
      ...(hasFilters && !(config.module === 'audioPlay' || config.module === 'videoPlay')
        ? getFileFormatTypeFilterProps(config)
        : {}),
      render: (value: any, row: any) => {
        return row.fileFormatName;
      },
    },
    {
      title: '操作',
      dataIndex: 'actions',
      key: 'actions',
      width: getWidth(11.0),
    },
  ];
};
