import { useState } from 'react';
import MenuFilter from './MenuFilter';

const Component = ({ setSelectedKeys, selectedKeys, confirm, close, clearFilters }: any) => {
  const [items, setItems] = useState([
    { label: '办公软件', key: '0' },
    { label: '教育学习', key: '1' },
    { label: '金融财经', key: '2' },
    { label: '日常生活', key: '3' },
    { label: '软件编程', key: '4' },
    { label: '社交软件', key: '5' },
    { label: '图形图像', key: '6' },
    { label: '系统网络', key: '7' },
    { label: '音频视频', key: '8' },
    { label: '游戏娱乐', key: '9' },
    { label: '其他', key: '10' },
  ]);
  const toggleItem = (key: string) => {
    if (selectedKeys.includes(key)) {
      setSelectedKeys(selectedKeys.filter((k: string) => k !== key));
    } else {
      setSelectedKeys([...selectedKeys, key]);
    }
    submit('ok');
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
    } else if (type === 'ok') {
      confirm({ closeDropdown: false });
      // close();
    } else if (type === 'close') {
      close();
    }
  };

  return (
    <MenuFilter items={items} selectedKeys={selectedKeys} onSelect={toggleItem} onSubmit={submit} />
  );
};

export default Component;
