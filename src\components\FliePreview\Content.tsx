import AudioVideoPreview from './AudioVideoPreview';
import ErrorPreview from './ErrorPreview';
import styles from './FilePreview.module.less';
import IframePreview from './IframePreview';
import ImagePreview from './ImagePreview';
import type { File } from './interface';
import YBWordPreview from './YBWordPreview';

interface Props {
  file: File;
}

export default ({ file }: Props) => {
  const { verifyValue, verifyText, fileFormatType, fileType } = file;
  const lcFileType = fileType!.toLowerCase();
  if (verifyValue === false) {
    return <ErrorPreview errorText={verifyText || '浏览错误'} />;
  }
  if (
    fileFormatType === 'library_file_type_pic' &&
    (lcFileType === 'jpg' ||
      lcFileType === 'jpeg' ||
      lcFileType === 'png' ||
      lcFileType === 'gif' ||
      lcFileType === 'bmp' ||
      lcFileType === 'webp')
  ) {
    return <ImagePreview file={file} />;
  }
  if (fileFormatType === 'library_file_type_audio') {
    //&&(lcFileType === 'mp3' || lcFileType === 'wav' || lcFileType === 'ogg')
    return <AudioVideoPreview open={true} file={file} MulitiModalClassName={styles.FliePreview} />;
  }
  if (fileFormatType === 'library_file_type_video') {
    //&&(lcFileType === 'mp4' || lcFileType === 'webm' || lcFileType === 'ogg')
    return <AudioVideoPreview open={true} file={file} MulitiModalClassName={styles.FliePreview} />;
  }
  if (
    (fileFormatType === 'library_file_type_doc' || fileFormatType === 'library_file_type_yb') &&
    (lcFileType === 'ybd' || lcFileType === 'yb')
  ) {
    return <YBWordPreview file={file} />;
  }
  return <IframePreview file={file} />;
};
