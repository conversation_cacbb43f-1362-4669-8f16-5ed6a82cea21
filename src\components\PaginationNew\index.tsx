import React, { useState } from 'react';
import Styles from './index.module.less';

interface CustomPaginationProps {
  total: number; // 数据总数
  pageSize: number; // 每页条目数
  current: number;
  onChange?: (page: number, pageSize: number) => void; // 页码变动时的回调
}

const CustomPagination: React.FC<CustomPaginationProps> = ({ total, pageSize, onChange }) => {
  const [current, setCurrent] = useState(1);
  const totalPages = Math.ceil(total / pageSize); // 计算总页数

  // 上一页按钮的点击事件处理
  const goToPrevPage = () => {
    if (current > 1) {
      setCurrent(current - 1);
      onChange?.(current - 1, pageSize);
    }
  };
  // 下一页按钮的点击事件处理
  const goToNextPage = () => {
    if (current < totalPages) {
      setCurrent(current + 1);
      onChange?.(current + 1, pageSize);
    }
  };
  //首页
  const goToFirstPage = () => {
    setCurrent(1);
    onChange?.(1, pageSize);
  };
  //末页
  const goToLastPage = () => {
    const lastPage = Math.ceil(total / pageSize);
    setCurrent(lastPage);
    onChange?.(lastPage, pageSize);
  };

  return (
    <div className={Styles.pagination}>
      <button onClick={goToFirstPage} className={`${current === 1 && Styles.disabled}`}>
        首页
      </button>
      <button onClick={goToPrevPage} className={`${current === 1 && Styles.disabled}`}>
        上一页
      </button>
      <div style={{ margin: '0 18px' }}>
        共计 <span>{totalPages}</span> 页 当前 <span>{current}</span> 页
      </div>
      <button onClick={goToNextPage} className={`${current === totalPages && Styles.disabled}`}>
        下一页
      </button>
      <button onClick={goToLastPage} className={`${current === totalPages && Styles.disabled}`}>
        末页
      </button>
    </div>
  );
};

export default CustomPagination;
