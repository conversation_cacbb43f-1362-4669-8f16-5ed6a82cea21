.albumContainOut {
  position: relative;
  width: 100%;
  height: 760px;
  background-color: #cdcdcd;
  font-family: PingFang SC;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  flex-direction: column;
}
.flexbox {
  display: flex;
  justify-content: center;
  align-items: center;
}
.albumContain {
  position: relative;
  width: 922px;
  height: 616px;
  background-image: url('@/assets/images/magicAlbum/albumDetatilBg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 22px 20px;
  // box-sizing: content-box;
  display: flex;
  justify-content: center;
}
.magazine {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  // overflow: hidden;
  height: 566px;
  .pageBox {
    width: 100%;
    height: 100%;
    background-color: #fff;
    &:nth-child(2n) .page {
      border-left: 1px solid #eee;
    }
  }
  .page {
    position: relative;
    width: 100%;
    height: 100%;
    background: #fff;
    // padding: 10px;
    // border-radius: 8px;
    img {
      position: absolute;
      object-fit: cover;
      cursor: pointer;
      &.selected {
        border: 3px solid var(--yb-primary-color);
      }
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}
.albumRightBtn {
  position: absolute;
  right: 0;
  top: 80px;
  height: 100%;
  display: flex;
  align-items: top;
  .btnItem {
    width: 140px;
    height: 47px;
    line-height: 47px;
    border-radius: 11px 0px 0px 11px;
    background: linear-gradient(180deg, #faf8f6 0%, #f0efec 100%);
    box-sizing: border-box;
    border-image: linear-gradient(
        180deg,
        rgba(192, 191, 191, 0.8) 0%,
        rgba(128, 128, 128, 0.8) 100%
      )
      0.5;
    box-shadow:
      0px 4px 4px 0px rgba(0, 0, 0, 0.05),
      0px 4px 8px 0px rgba(0, 0, 0, 0.2),
      inset 0px 1px 1px 0px rgba(255, 255, 255, 0.1),
      inset 0px -1px 2px 0px rgba(0, 0, 0, 0.1);

    font-size: 20px;
    color: rgba(0, 0, 0, 0.65);
    margin-bottom: 4px;
    text-align: center;
    cursor: pointer;
    &:hover {
      color: #fff;
      background: linear-gradient(180deg, #ff9863 -3%, #f16e42 100%);
      border-image: linear-gradient(
          180deg,
          rgba(214, 135, 111, 0.8) -3%,
          rgba(132, 83, 65, 0.8) 100%
        )
        0.5;
    }
  }
  .btnItemActive {
    color: #fff;
    background: linear-gradient(180deg, #ff9863 -3%, #f16e42 100%);
    border-image: linear-gradient(180deg, rgba(214, 135, 111, 0.8) -3%, rgba(132, 83, 65, 0.8) 100%)
      0.5;
  }
}
@btnFontColor: rgba(255, 255, 255, 0.85);
@btnFontSize: 11px;
.btnBoxLeft {
  position: absolute;
  width: 30px;
  display: flex;
  flex-direction: column;
  justify-content: start;
  span {
    display: block;
    width: 30px;
    height: 24px;
    background: rgba(0, 0, 0, 0.6);
    color: @btnFontColor;
    font-size: @btnFontSize;
    border-radius: 1px;
    text-align: center;
    line-height: 24px;
    cursor: pointer;
    &:hover,
    &.active {
      background: #f57f17;
      color: @btnFontColor;
    }
  }
}
.pager {
  width: 100%;
  font-size: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px 24px;
  .shareBtnBottom {
    margin-right: 16px;
  }
  .blueFont {
    color: #3d5afe;
  }
}
