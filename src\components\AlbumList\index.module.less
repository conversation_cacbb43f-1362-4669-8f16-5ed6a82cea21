.albumContainOut {
  position: relative;
  width: 100%;
  min-height: 100%;
  background-color: #c8c6b8;
  font-family: PingFang SC;
}
.net-album-contain {
  justify-content: center;
  align-items: center;
  min-height: 560px;
}
.albumContainInner {
  display: flex;
  justify-content: center;
  width: 1920px;
}
.albumContain {
  display: flex;
  flex-wrap: wrap;
  width: 1760px;
  padding: 60px 0 0;
  // padding-right: 126px;
  .albumItem {
    position: relative;
    width: 370px;
    height: 324px;
    margin: 0 20px 30px;
    cursor: pointer;
    background: #ffffff;
    box-shadow: 0px 0px 20px 12px rgba(0, 0, 0, 0.1);
    .albumName {
      font-family: Helvetica;
      font-size: 36px;
      line-height: 36px;
      font-weight: 600;
      color: #3e3b23;
      text-align: center;
      margin: 24px 0 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 0 40px;
    }
    .albumNameInput {
      width: 280px;
      height: 44px;
      font-size: 34px;
      font-weight: 600;
      text-align: center;
      border: 1px solid var(--yb-primary-color);
    }
    .imageWrap {
      position: absolute;
      bottom: 0;
      height: 224px;
      text-align: center;
      .image {
        width: 370px; // 136
        height: 224px; // 120
        object-fit: cover;
        border-top: 1px solid #eeeeeed1;
      }
    }
    .imageRight {
      position: absolute;
      left: 0;
      top: 0;
      width: 40px;
      height: 100%;
      background-image: url('@/assets/images/magicAlbum/bg_right.png');
      background-repeat: no-repeat;
      background-size: cover;
    }
    .timeRow {
      text-align: center;
      font-size: 16px;
      color: #9c9564;
    }
    .shareTag {
      position: absolute;
      right: 0;
      top: 0;
      width: 64px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      background: #fff3e0;
      color: #ff9100;
      font-size: 22px;
    }
  }
}

.pager {
  position: absolute;
  bottom: 20px;
  width: 100%;
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-right: 126px;
  .shareBtnBottom {
    margin-right: 16px;
  }
  .blueFont {
    color: #3d5afe;
  }
}
.noData {
  position: absolute;
  top: 45%;
  color: rgba(0, 0, 0, 0.5);
  font-size: 16px;
}
