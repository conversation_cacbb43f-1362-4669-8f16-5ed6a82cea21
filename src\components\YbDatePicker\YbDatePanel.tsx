/**
 * @description 时间选择组件(返回赋值待完成，四种都选择的情况取值逻辑待确认)
 * <AUTHOR>
 * @date 2024-08-12
 */
import { CalendarOutlined } from '@ant-design/icons';
import { Button, DatePicker, Select } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { useImmer } from 'use-immer';
import styles from './index.module.less';
const YBDatePanel = (props: any) => {
  const { onSelect } = props; // value, onClose
  const currentYear = dayjs().year();
  const YearOptions = [];
  const MonthOptions = [];
  const HourOptions = [];
  interface OptionItem {
    value: string;
    label: string;
  }
  interface timeType {
    year: string | null;
    month: string | null;
    day: string | null;
    hour: string | null;
  }
  for (let i = currentYear; i <= currentYear + 20; i++) {
    YearOptions.push({ value: i.toString(), label: `${i}年` });
  }
  for (let i = 1; i <= 12; i++) {
    MonthOptions.push({ value: i.toString().padStart(2, '0'), label: `${i}月` });
  }
  for (let i = 0; i <= 23; i++) {
    HourOptions.push({ value: i.toString().padStart(2, '0'), label: `${i}时` });
  }
  const ref = useRef(null);
  const [dayOptionsLeft, setDayOptionsLeft] = useState<OptionItem[]>([]); // 日期范围左 所选年月拥有的天数
  const [dayOptionsRight, setDayOptionsRight] = useState<OptionItem[]>([]); // 日期范围右 所选年月拥有的天数
  const [dayOptionsBottom, setDayOptionsBottom] = useState<OptionItem[]>([]); // 最下方 所选年月拥有的天数
  const [startYear, setStartYear] = useState<string>();
  const [endYear, setEndYear] = useState<string>();
  const [startMonth, setStartMonth] = useState<string>();
  const [endMonth, setEndMonth] = useState<string>();
  const [startDay, setStartDay] = useState<string>();
  const [endDay, setEndDay] = useState<string>();
  const [dayBefore, setDayBefore] = useState<any>('');
  const [dayBehind, setDayBehind] = useState<any>('');
  const [timeSearch, setTimeSearch] = useImmer<timeType>({
    year: null,
    month: null,
    day: null,
    hour: null,
  });
  // console.log(dayjs().format('YYYY-MM-DD HH:mm:ss')); //HH 24小时制
  const getDaysInMonth = (year: any, month: any, type: number) => {
    if (!(year && month)) {
      return;
    }
    const dateStr = `${year}-${month}`;
    const daysNum = dayjs(dateStr).daysInMonth();
    const dayOptions = [];
    for (let i = 1; i <= daysNum; i++) {
      dayOptions.push({ value: i.toString().padStart(2, '0'), label: `${i}日` });
    }
    switch (type) {
      case 1:
        setDayOptionsLeft(dayOptions);
        setStartDay(undefined);
        break;
      case 2:
        setDayOptionsRight(dayOptions);
        setEndDay(undefined);
        break;
      case 3:
        setDayOptionsBottom(dayOptions);
        setTimeSearch((draft) => {
          draft.day = null;
          draft.hour = null;
        });
        break;
    }
  };

  const confirmDatePick = () => {
    let timeObj: any = {};
    if (timeSearch.year && timeSearch.month && timeSearch.day && timeSearch.hour) {
      timeObj = {
        startTime: `${timeSearch.year}-${timeSearch.month}-${timeSearch.day} ${timeSearch.hour}:00:00`,
        endTime: `${timeSearch.year}-${timeSearch.month}-${timeSearch.day} ${timeSearch.hour}:59:59`,
      };
    } else if (startYear && endYear) {
      const startMonthCopy = startMonth || '01';
      const endMonthCopy = endMonth || '12';
      const startDayCopy = startDay || '01';
      const endDayCopy = endMonth ? endDay || dayjs(`${endYear}-${endMonth}`).daysInMonth() : '31';
      timeObj = {
        startTime: `${startYear}-${startMonthCopy}-${startDayCopy} 00:00:00`,
        endTime: `${endYear}-${endMonthCopy}-${endDayCopy} 23:59:59`,
      };
    } else if (dayBefore) {
      timeObj = {
        startTime: '',
        endTime: `${dayBefore} 23:59:59`,
      };
    } else if (dayBehind) {
      timeObj = {
        startTime: `${dayBehind} 00:00:00`,
        endTime: '',
      };
    } else {
      timeObj = {
        startTime: '',
        endTime: '',
      };
      // message.warning('请选择完整过滤时间');
    }
    if (!timeObj.startTime && !timeObj.endTime) {
      onSelect('');
    } else {
      onSelect(`${timeObj.startTime}~${timeObj.endTime}`);
    }
  };
  const clearAll = (clearType: Array<1 | 2 | 3 | 4>) => {
    if (clearType.includes(1)) {
      setStartYear(undefined);
      setEndYear(undefined);
      setStartMonth(undefined);
      setEndMonth(undefined);
      setStartDay(undefined);
      setEndDay(undefined);
    }
    if (clearType.includes(2)) {
      setDayBefore('');
    }
    if (clearType.includes(3)) {
      setDayBehind('');
    }
    if (clearType.includes(4)) {
      setTimeSearch({ year: null, month: null, day: null, hour: null });
    }
  };
  return (
    <div ref={ref} className={styles.uploadTimeFilter}>
      {/* 年份选择 */}
      <div className={styles.datePickerBox}>
        <Select
          placeholder="选择年份"
          style={{ width: 176, height: 24 }}
          options={YearOptions}
          suffixIcon={<CalendarOutlined />}
          value={startYear}
          onChange={(e) => {
            setStartYear(e);
            getDaysInMonth(e, startMonth, 1);
            clearAll([2, 3, 4]);
            if (endYear && +e > +endYear) setEndYear(e);
          }}
        />

        <span>到</span>
        <Select
          placeholder="选择年份"
          style={{ width: 176, height: 24 }}
          options={YearOptions}
          suffixIcon={<CalendarOutlined />}
          value={endYear}
          onChange={(e) => {
            setEndYear(e);
            getDaysInMonth(e, endMonth, 1);
            clearAll([2, 3, 4]);
            if (startYear && +e < +startYear) setStartYear(e);
          }}
        />
      </div>
      <div className={styles.datePickerBox}>
        <Select
          placeholder="选择月份"
          style={{ width: 176, height: 24 }}
          options={MonthOptions}
          suffixIcon={<CalendarOutlined />}
          disabled={!startYear}
          value={startMonth}
          onChange={(e: any) => {
            setStartMonth(e);
            getDaysInMonth(startYear, e, 1);
            clearAll([2, 3, 4]);
            if (startYear === endYear && endMonth && +e > +endMonth) {
              setEndMonth(e);
            }
          }}
        />
        <span>到</span>
        <Select
          placeholder="选择月份"
          style={{ width: 176, height: 24 }}
          options={MonthOptions}
          suffixIcon={<CalendarOutlined />}
          disabled={!endYear}
          value={endMonth}
          onChange={(e: any) => {
            setEndMonth(e);
            getDaysInMonth(endYear, e, 2);
            clearAll([2, 3, 4]);
            if (startYear === endYear && startMonth && +e < +startMonth) {
              setStartMonth(e);
            }
          }}
        />
      </div>
      <div className={styles.datePickerBox}>
        <Select
          placeholder="选择日期"
          style={{ width: 176, height: 24 }}
          options={dayOptionsLeft}
          suffixIcon={<CalendarOutlined />}
          disabled={!startYear || !startMonth}
          value={startDay}
          onChange={(e: any) => {
            setStartDay(e);
            clearAll([2, 3, 4]);
            if (startYear === endYear && startMonth === endMonth && endDay && +e > +endDay) {
              setEndDay(e);
            }
          }}
        />
        <span>到</span>
        <Select
          placeholder="选择日期"
          style={{ width: 176, height: 24 }}
          options={dayOptionsRight}
          suffixIcon={<CalendarOutlined />}
          disabled={!endYear || !endMonth}
          value={endDay}
          onChange={(e: any) => {
            setEndDay(e);
            clearAll([2, 3, 4]);
            if (startYear === endYear && startMonth === endMonth && startDay && +e < +startDay) {
              setStartDay(e);
            }
          }}
        />
      </div>
      <div className={styles.searchTips}>
        查询范围：
        <br />{' '}
        从哪年哪月之间添加的文件，或从哪年哪月到哪年哪月之间添加的文件，或从哪年哪月哪日到哪年哪月哪日添加的文件。
      </div>
      <div className={styles.borderBottom1MB10} />
      {/* 日期选择 */}
      <div className={styles.datePickerDateBox}>
        <DatePicker
          style={{ width: '176px', height: '24px' }}
          placeholder="请选择"
          format="YYYY-MM-DD"
          value={dayBefore === '' ? '' : dayjs(dayBefore)}
          onChange={(_, dateString) => {
            setDayBefore(dateString);
            clearAll([1, 3, 4]);
          }}
        />
        <span>之前添加</span>
      </div>
      <div className={styles.searchTips}>
        查询范围：
        <br /> 从哪年之前添加的文件，或从哪年哪月前添加的文件，或从哪年哪月哪日前添加的文件。
      </div>
      <div className={styles.borderBottom1MB10} />
      <div className={styles.datePickerDateBox}>
        <DatePicker
          style={{ width: '176px', height: '24px' }}
          placeholder="请选择"
          value={dayBehind === '' ? '' : dayjs(dayBehind)}
          onChange={(_, dateString: any) => {
            setDayBehind(dateString);
            clearAll([1, 2, 4]);
          }}
        />
        <span>之后添加</span>
      </div>
      <div className={styles.searchTips}>
        查询范围：
        <br /> 从哪年之后添加的文件，或从哪年哪月后添加的文件，或从哪年哪月哪日后添加的文件。
      </div>
      <div className={styles.borderBottom1MB10} />
      <div className={styles.datePickerBox2}>
        <Select
          placeholder="按年查"
          style={{ width: 90, height: 24 }}
          options={YearOptions}
          value={timeSearch.year}
          onChange={(e) => {
            setTimeSearch((draft) => {
              draft.year = e;
            });
            getDaysInMonth(e, timeSearch.month, 3);
            clearAll([1, 2, 3]);
          }}
        />
        <Select
          placeholder="按月查"
          style={{ width: 90, height: 24 }}
          options={MonthOptions}
          value={timeSearch.month}
          disabled={!timeSearch.year}
          onChange={(e) => {
            setTimeSearch((draft) => {
              draft.month = e;
            });
            getDaysInMonth(timeSearch.year, e, 3);
            clearAll([1, 2, 3]);
          }}
        />
        <Select
          placeholder="按日查"
          style={{ width: 90, height: 24 }}
          options={dayOptionsBottom}
          value={timeSearch.day}
          disabled={!timeSearch.year || !timeSearch.month}
          onChange={(e) => {
            setTimeSearch((draft) => {
              draft.day = e;
            });
            clearAll([1, 2, 3]);
          }}
        />
        <Select
          placeholder="按时查"
          style={{ width: 90, height: 24 }}
          options={HourOptions}
          value={timeSearch.hour}
          disabled={!timeSearch.year || !timeSearch.month || !timeSearch.day}
          onChange={(e) => {
            setTimeSearch((draft) => {
              draft.hour = e;
            });
            clearAll([1, 2, 3]);
          }}
        />
      </div>
      <div style={{ textAlign: 'center', marginTop: '4px' }}>
        <Button type="primary" className={styles.submitBtn} onClick={confirmDatePick}>
          确定
        </Button>
        <Button type="text" onClick={() => clearAll([1, 2, 3, 4])}>
          清空
        </Button>
      </div>
    </div>
  );
};
export default YBDatePanel;
