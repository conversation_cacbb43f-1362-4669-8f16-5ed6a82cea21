.cosSpan {
  display: inline-block;
  min-width: 40px;
  height: 18px;
  border-radius: 4px;
  background: #d3d3d3;
  cursor: pointer;
  font-size: 12px;
  color: #4d4d4d;
  display: flex;
  letter-spacing: 2px;
  justify-content: center;
  align-items: center;
}

.consDiv {
  margin-bottom: 5px;
}

.conDiv {
  display: flex;
  height: 26px;
  align-items: center;
}

.hoverSpan:hover {
  cursor: pointer;
  color: red;
}

.voSpan {
  display: inline-block;
  max-width: 160px; //显示宽度
  white-space: nowrap; //文本不换行。
  overflow: hidden; //超过部分隐藏
  text-overflow: ellipsis; //超过部分用...代替
  font-size: 14px;
  padding: 0;
}

// .conDiv {
//   display: flex;
// }
