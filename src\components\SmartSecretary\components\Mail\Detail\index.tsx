import {
  getChatLatestContacts,
  getChatLatestGroups,
  getGroupData,
  getLatestContacts,
  getMailData,
  updateReadFlag,
  updateReadFlagAndRevokeflag,
  updateRevokeflag,
} from '@/api/mail/chat';
import { MailBookDataType, mailDataReq } from '@/api/mail/chat/mailModels';
import arrowIcon from '@/assets/images/home/<USER>';
import ReadFlag from '@/pages/Mail/components/Chat/ReadFlag/Index';
import useCtxStore from '@/pages/Share/Category/useCtxStore.ts';
import useAppStore from '@/store/useAppStore';
import useUserStore from '@/store/useUserStore';
import { getWidth } from '@/utils/common';
import { Timeout } from 'ahooks/lib/useRequest/src/types';
import { Button, Divider, Dropdown, Empty, Flex, Image, Space } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { FC, useContext, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Context } from '../../../context';
import ChatCards from '../components/ChatCards/ChatCards';
import EditCard from '../components/EditCard/EditCard';
import styles from './index.module.less';
const MailDetail: FC<any> = ({ headName, selectedIndex, shareGroupId }) => {
  interface pageType {
    min: number;
    max: number;
    type: boolean;
  }

  const { rootDom, hideDetail, mailType, query } = useContext(Context);
  const [groupNameInHome] = useCtxStore((state) => [state.groupNameInHome]);
  const { channel } = useAppStore((state: any) => state);
  // const mockData = `{"id":"1852602376647655424","mailId":"1852602376387608576","senderId":"1666666666666666666","mailTitle":"欢迎使用裕邦智慧办公平台个人版","mailContent":"<p dir=\\"ltr\\" style=\\"text-align: left;\\"><span style=\\"white-space: pre-wrap;\\">    亲爱的用户：</span></p><p dir=\\"ltr\\" style=\\"text-align: left;\\"><span style=\\"white-space: pre-wrap;\\">您好！</span></p><p dir=\\"ltr\\" style=\\"text-align: left;\\"><span style=\\"white-space: pre-wrap;\\">欢迎使用裕邦智慧办公平台个人版，您的账号为：5802666698-95，邮箱为：<EMAIL>，如有任何问题，可以联系我们的客服热线40003-91668，我们将竭诚为您服务！</span></p><p dir=\\"ltr\\" style=\\"text-align: left;\\"><span style=\\"white-space: pre-wrap;\\">                                                                            谢谢！</span></p><p dir=\\"ltr\\" style=\\"text-align: left;\\"><span style=\\"white-space: pre-wrap;\\">                                                                          2024年11月2日</span></p>","sendTime":1730529801000,"timedTime":null,"serverCurrentTime":1735027234974,"confidentialityStatus":0,"senderAddress":"<EMAIL>","shareFlag":"0","addressList":[{"id":"1852602376647655424","mailId":"1852602376387608576","readFlag":1,"addresseeStatus":0,"revokeFlag":0,"blackFlag":0,"addressStatus":1,"addressee":"<EMAIL>","shareFlag":null,"addresseeName":"周广成","groupId":null,"groupFlag":null,"contactPersonId":null,"userId":"1852602374472863746","mobile":"18219952439","realName":"周广成","username":"5802666698-95"}],"addressGroupList":[],"copyAddressList":[],"copyAddressGroupList":[],"secretAddressList":[],"mailChatAttachVOList":[],"contractPersonId":null,"senderName":"裕邦客服","mobile":"4000391668","username":"00000000001","readFlag":1,"revokeFlag":0,"isSystemSend":true,"isInGroup":null,"sendType":"from"}`;
  const [dataList, setDataList] = useState<MailBookDataType[]>([]);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [editCardOpen, setEditCardOpen] = useState<boolean>(false);
  const userInfo = useUserStore((state) => state.userInfo);
  const [addressDataList, setAddressDataList] = useState<any[]>([]);
  const [addressDataIndex, setAddressDataIndex] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<pageType>({ min: 0, max: 0, type: false });
  const [addresseeType, setAddresseeType] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(false);
  const [mailUserName, setMailUserName] = useState(headName);
  const [replyData, setReplyData] = useState<any>({});
  const fetchList = () => {
    setCurrentPage(
      JSON.parse(JSON.stringify({ ...currentPage, max: currentPage.max + 1, type: true })),
    );
  };

  //重置滚动条
  const scrollTopRefresh = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop = 0;
    }
  };
  // web端最近联系人查询（对应首页人、群的排序以及上一封-下一封）
  const getLatestContactsFn = async () => {
    const params =
      mailType?.index === 1000
        ? { pageNo: 1, pageSize: 1000 }
        : { mailType: mailType.index, mailAddress: userInfo?.email };
    const requestPath =
      mailType?.index === 1000
        ? getLatestContacts
        : mailType?.index === 109 || mailType?.index === 209
          ? getChatLatestGroups
          : getChatLatestContacts;
    requestPath(params).then((res: any) => {
      if (res.code === 0) {
        setAddressDataList(res.data.list ? res.data.list : res.data);
        setCurrentPage(JSON.parse(JSON.stringify({ min: 0, max: 1, type: true })));
        if (shareGroupId) {
          console.log(res.data.list.findIndex((item: any) => item.groupId === shareGroupId));
          setAddressDataIndex(
            res.data.list.findIndex((item: any) => item.groupId === shareGroupId),
          );
        } else {
          setAddressDataIndex(selectedIndex ? selectedIndex : 0);
        }
      }
    });
  };
  //请求联系人列表

  //请求共享群列表

  //请求联系人往来信件
  const fechAddressBookData = () => {
    setLoading(true);
    const params: mailDataReq = {
      pageNo: currentPage.max,
      pageSize: 10,
      addressee: addressDataList[addressDataIndex].email,
      senderAddress: userInfo?.email,
      addresseeStatus: addresseeType,
    };
    getMailData(params)
      .then((res: any) => {
        setLoading(false);
        if (res.code === 0) {
          if (res.data.total == dataList.length + res.data.list?.length) {
            setHasMore(false);
          } else {
            setHasMore(true);
          }
          if (currentPage.max == 1) {
            setDataList([...res.data.list]);
          } else {
            setDataList([...dataList, ...res.data.list]);
          }
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };
  //请求共享群往来信件
  const fechGroupBookData = () => {
    setLoading(true);
    const params: mailDataReq = {
      pageNo: currentPage.max,
      pageSize: 10,
      groupId: addressDataList[addressDataIndex].groupId,
      senderAddress: userInfo?.email,
      mailType: '300',
    };
    getGroupData(params)
      .then((res: any) => {
        if (res.code === 0) {
          setLoading(false);
          if (res.data.total == dataList.length + res.data.list?.length) {
            setHasMore(false);
          } else {
            setHasMore(true);
          }
          if (currentPage.max == 1) {
            setDataList([...res.data.list]);
          } else {
            setDataList([...dataList, ...res.data.list]);
          }
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };
  //续发回复1，转发2
  const handClickReply = (type: number, item: any) => {
    if (type === 1) {
      setEditCardOpen(true);
      setReplyData({ address: addressDataList[addressDataIndex], mail: item, isSet: true });
    } else {
      console.log('开发中', item);
    }
  };
  //上一封
  const [choButton, setChuButton] = useState<number>(0);
  const handleLastClick = () => {
    setChuButton(0);
    setAddressDataIndex(addressDataIndex - 1);
    setCurrentPage(JSON.parse(JSON.stringify({ min: 0, max: 1, type: true })));
    scrollTopRefresh();
  };
  //下一封
  const handleNextClick = () => {
    setChuButton(0);
    setAddressDataIndex(addressDataIndex + 1);
    setCurrentPage(JSON.parse(JSON.stringify({ min: 0, max: 1, type: true })));
    scrollTopRefresh();
  };
  useEffect(() => {
    if (addressDataIndex < 0) {
      shareGroupId && setMailUserName(groupNameInHome);
      return;
    }
    console.log(addressDataList, addressDataIndex);
    if (!addressDataList.length) return;
    if (addressDataList[addressDataIndex]?.groupFlag) {
      setMailUserName(addressDataList[addressDataIndex].groupName);
    } else {
      setMailUserName(addressDataList[addressDataIndex].realName);
    }
  }, [addressDataIndex, addressDataList]);
  useEffect(() => {
    setDataList([]);
    getLatestContactsFn();
  }, [mailType]);
  useEffect(() => {
    if (addressDataIndex < 0) return;
    if (currentPage.max > 0 && addressDataList.length > 0) {
      if (addressDataList[addressDataIndex].groupFlag) {
        fechGroupBookData();
      } else {
        fechAddressBookData();
      }
      if (currentPage.max == 1) {
        scrollTopRefresh();
      }
    } else {
      setDataList([]);
    }
  }, [currentPage]);
  const addressDataListClick = (item: any) => {
    setAddressDataIndex(Number(item.key));
    setCurrentPage(JSON.parse(JSON.stringify({ min: 0, max: 1, type: true })));
  };
  const getScrollHeight = () => {
    if (shareGroupId) {
      return window.innerHeight - getWidth(channel === 'web' ? 460 : 400);
    }
    return window.innerHeight - getWidth(channel === 'web' ? 270 : 210);
  };
  const restChat = () => {
    setCurrentPage(JSON.parse(JSON.stringify({ min: 0, max: 1, type: true })));
  };
  //卡片进入视图的逻辑处理
  const timerIds = useRef<Timeout[]>([]);
  useEffect(() => {
    return () => {
      //清理定时撤回队列
      timerIds.current.forEach((id) => clearTimeout(id));
      timerIds.current = [];
    };
  }, []);
  const onInView = (data: any) => {
    //自动触发已读
    if (
      data.readFlag == 0 &&
      data.sendType == 'from' &&
      data.confidentialityStatus != 6 &&
      data.confidentialityStatus != 7
    ) {
      updateReadFlag({ id: data.id as string, type: 1 }).then();
    }
    //加入定时撤回队列
    if (
      [4, 5, 7].includes(data.confidentialityStatus) &&
      !data.revokeFlag &&
      data.sendType == 'from'
    ) {
      const timerId = setTimeout(
        () => {
          updateRevokeflag({ id: data.id as string }).then((res: any) => {
            if (res.data) {
              setCurrentPage(JSON.parse(JSON.stringify({ min: 0, max: 1 })));
            }
          });
        },
        dayjs(data.timedTime).valueOf() - (data.serverCurrentTime ? data.serverCurrentTime : 0),
      );
      timerIds.current.push(timerId);
    }
  };
  //手动确认已读并撤回
  const [ReadFlagData, setReadFlagData] = useState<MailBookDataType>();
  const [openReadFlagModal, setOpenReadFlagModal] = useState<boolean>(false);
  const checkReadFlag = (data: MailBookDataType) => {
    setReadFlagData(data);
    setOpenReadFlagModal(true);
  };
  const submitRevokeflag = () => {
    setOpenReadFlagModal(false);
    updateReadFlagAndRevokeflag({ id: ReadFlagData?.id as string }).then((res: any) => {
      if (res.data) {
        setCurrentPage(JSON.parse(JSON.stringify({ min: 0, max: 1 })));
      }
    });
  };
  const closeRevokeflag = () => {
    setOpenReadFlagModal(false);
    setReadFlagData(undefined);
  };
  return createPortal(
    <div
      className={classNames(styles.mailDetail, shareGroupId ? styles.mailDetailShare : undefined)}
      id="mailDetailContainer"
    >
      <Flex className={styles.header}>
        <div className={styles.nameBox}>
          <Dropdown
            menu={{
              items: addressDataList.map((v, i) => {
                return v.groupFlag
                  ? Object.assign(v, { key: i, label: v.groupName })
                  : Object.assign(v, { key: i, label: v.realName });
              }),
              onClick: (item) => {
                addressDataListClick(item);
              },
            }}
            trigger={['click']}
          >
            <div>
              <span className={styles.nameText} title={mailUserName}>
                {mailUserName}
              </span>
              <Image src={arrowIcon} className={styles.icon} preview={false}></Image>
            </div>
          </Dropdown>
        </div>
        <Space>
          <Button
            size="small"
            type={editCardOpen ? 'primary' : undefined}
            className={editCardOpen ? styles.btnActive : undefined}
            onClick={() => {
              setEditCardOpen(!editCardOpen);
            }}
          >
            写邮件
          </Button>
          <Button
            size="small"
            onClick={handleLastClick}
            disabled={addressDataIndex <= 0 ? true : false}
          >
            上一个
          </Button>
          <Button
            size="small"
            onClick={handleNextClick}
            disabled={addressDataIndex < addressDataList.length - 1 ? false : true}
          >
            下一个
          </Button>
          <Button size="small">转发</Button>
          {!shareGroupId ? (
            <Button
              size="small"
              onClick={() => {
                hideDetail();
              }}
            >
              返回
            </Button>
          ) : undefined}
        </Space>
      </Flex>
      <div
        id="scrollableDivMail"
        className={styles.scrollableDiv}
        style={{ height: getScrollHeight() }}
      >
        <InfiniteScroll
          dataLength={dataList.length} // 已加载的数据长度
          next={() => fetchList()} // 加载更多数据的函数
          hasMore={hasMore} // 是否还有更多数据
          loader={false}
          // loader={<div>加载中...</div>} // 加载中的提示信息
          endMessage={
            dataList.length && dataList.length >= 4 ? <Divider plain>全部加载完成</Divider> : <></>
          }
          scrollThreshold="100px"
          scrollableTarget="scrollableDivMail"
        >
          <div className={styles.mailContent} ref={scrollContainerRef}>
            {/* 编辑邮件组件  editCardOpen*/}
            {editCardOpen && (
              <EditCard
                replyData={replyData}
                close={() => {
                  setEditCardOpen(false);
                }}
                restChat={restChat}
              ></EditCard>
            )}
            {dataList.length > 0 ? (
              dataList.map((item: any, index: number) => {
                return (
                  <ChatCards
                    key={`${item.id}-${index}`}
                    item={item}
                    onInView={onInView}
                    handClickReply={handClickReply}
                    checkReadFlag={checkReadFlag}
                  ></ChatCards>
                );
              })
            ) : (
              <Empty />
            )}
          </div>
        </InfiniteScroll>
      </div>
      {openReadFlagModal && (
        <ReadFlag
          type={ReadFlagData?.confidentialityStatus as number}
          close={closeRevokeflag}
          submit={submitRevokeflag}
        ></ReadFlag>
      )}
    </div>,
    shareGroupId ? document.getElementById('SmartSecretaryInnerContent') : rootDom,
  );
};
export default MailDetail;
