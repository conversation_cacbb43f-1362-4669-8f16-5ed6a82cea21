import useLibraryStore from '@/store/useLibraryStore';
import MenuFilter from './MenuFilter';

const Component = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  close,
  clearFilters,
  config,
}: any) => {
  const [safeLevelList] = useLibraryStore((state) => [state.safeLevelList]);
  const toggleItem = (key: string, isSubmit: boolean) => {
    if (selectedKeys.includes(key)) {
      setSelectedKeys(selectedKeys.filter((k: string) => k !== key));
    } else {
      setSelectedKeys([...selectedKeys, key]);
    }
    isSubmit && submit('ok');
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
      submit('ok');
    } else if (type === 'close') {
      close();
      config?.setSafeLevelOpen(false);
    } else if (type === 'ok') {
      confirm({ closeDropdown: false });
      // close();
    }
  };
  return (
    <MenuFilter
      items={safeLevelList}
      selectedKeys={selectedKeys}
      onSelect={toggleItem}
      onSubmit={submit}
    />
  );
};

export default Component;
