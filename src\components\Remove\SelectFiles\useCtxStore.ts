import { create, autoUseShallow, autoCreateSetters } from '@/store';
import useUserStore from '@/store/useUserStore';
import type { Setter } from '@/store';
import useFromModuleStore from '@/store/useFromModuleStore';
import type { Config } from '../Context';

interface FormFilterData {
  realName: string;
  title: string;
}

interface ColumnsFilterData {
  fileFormatTypeList: string[];
  sourceList: string[];
  minFileSize: string;
  maxFileSize: string;
}

export interface State {
  selectFilesOpen: boolean;
  formFilterData: FormFilterData;
  columnsFilterData: ColumnsFilterData;
  queryData: (FormFilterData & ColumnsFilterData)[];
  queryType: { current: 'all' | 'results' };
  list: any[];
  selectedList: any[];
  selectedMap: Record<string, any>;
}

export interface SetState {
  setSelectFilesOpen: Setter;
  setFormFilterData: Setter;
  setColumnsFilterData: Setter;
  setQueryData: Setter;
  setQueryType: Setter;
  setList: Setter;
  setSelectedList: Setter;
  setSelectedMap: Setter;
}

export const formatQueryData = (queryData: any, { pageNumber, pageSize, config }: any) => {
  const { userInfo } = useUserStore.getState();
  const { fromModule, fromModuleQuery } = useFromModuleStore.getState();
  const result: any = {};
  const add = (key: any, value: any) => {
    result[key] = result[key] || [];
    if (value) {
      result[key].push(value);
    }
  };
  const getFileFormatType = (item: any) => {
    if (config.module === 'audioPlay') {
      return 'library_file_type_audio';
    } else if (config.module === 'videoPlay') {
      return 'library_file_type_video';
    } else {
      return item.fileFormatTypeList.join(',');
    }
  };
  queryData.forEach((item: any) => {
    result.pageNo = pageNumber.current;
    result.pageSize = pageSize;
    result.userId = userInfo?.id;
    result.tenantId = '';
    result.sortField = 'createTime';
    result.sortMethod = 'desc';
    result.groupId = fromModuleQuery?.groupId;
    result.deleted = fromModule === 'recycle' ? 1 : 0;    
    add('realName', item.realName);
    add('userName', '');
    add('title', item.title);
    add('content', '');
    add('startTime', '');
    add('endTime', '');
    add('fileFormatType', getFileFormatType(item));
    add('source', item.sourceList.join(','));
    add('safeLevel', '');
    add('querySizeList', { minFileSize: item.minFileSize, maxFileSize: item.maxFileSize });
  });
  return result;
};

const createUseCtxStore = function (config: Config) {
  const useCtxStore = autoUseShallow<State, SetState>(
    create(
      autoCreateSetters<State>((set, get, api): any => {
        const formFilterData = {
          realName: '',
          title: '',
        };
        const columnsFilterData = {
          fileFormatTypeList: [],
          sourceList: [],
          minFileSize: '',
          maxFileSize: '',
        };
        return {
          selectFilesOpen: false,
          formFilterData,
          columnsFilterData,
          queryData: [
            {
              ...formFilterData,
              ...columnsFilterData,
            },
          ],
          queryType: { current: 'all' },
          list: [],
          selectedList: [],
          selectedMap: {},
        };
      }),
    ),
  );

  useCtxStore.subscribe((state, prevState) => {
    if (state.queryType === prevState.queryType) {
      return;
    }
    switch (state.queryType.current) {
      case 'all':
        state.setQueryData([
          {
            ...state.formFilterData,
            ...state.columnsFilterData,
          },
        ]);
        break;
      case 'results':
        state.setQueryData((value: any) => [
          ...value,
          {
            ...state.formFilterData,
            ...state.columnsFilterData,
          },
        ]);
        break;
    }
  });

  return useCtxStore;
};

export default createUseCtxStore;
