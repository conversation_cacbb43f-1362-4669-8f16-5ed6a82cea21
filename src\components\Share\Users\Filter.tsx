import { getDataByResult } from '@/api/addressBook';
import { MyContent, MyTitle } from '@/components/Serach';
import { getWidth } from '@/utils/common';
import { AutoComplete, AutoCompleteProps, Button, Col, Form, Row, Space } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useContext, useState } from 'react';
import Context from '../Context';
import styles from './index.module.less';

const Component = () => {
  const { useUsersCtxStore } = useContext(Context);
  const [setQueryType, setTemporaryData] = useUsersCtxStore!((state) => [
    state.setQueryType,
    state.setTemporaryData,
  ]);
  const [form] = Form.useForm();
  const handleFilter = () => {
    return form.validateFields().then(({ keywords }) => {
      setTemporaryData([keywords]);
    });
  };
  const queryAll = () => {
    setQueryType({ current: 'all' });
    form.resetFields();
  };
  const queryResults = () => {
    handleFilter()
      .then(() => {
        setQueryType({ current: 'results' });
      })
      .then(() => {
        form.resetFields();
      });
  };
  //todo serach
  const [options, setOptions] = useState<AutoCompleteProps['options']>([]);
  const [val, setVal] = useState<string>('');
  const onSelect = (data: any) => {
    const value = data?.contactName?.includes(val)
      ? data.contactName
      : data?.username?.includes(val)
        ? data.username
        : data?.mobile?.includes(val)
          ? data.mobile
          : data?.email;
    form.setFieldsValue({ keywords: value });
    handleFilter().then(() => {
      setQueryType({ current: 'current' });
    });
  };

  const handleKeyDown = (e: any) => {
    if (e.keyCode === 13 || e.code === 'Enter' || e.key === 'Enter') {
      handleFilter().then(() => {
        setQueryType({ current: 'current' });
      });
    }
  };

  const onSearch = (data: string) => {
    setVal(data);
    const params: any = {
      pageNo: 1,
      pageSize: 10,
      blackType: 1,
      keyWordList: data,
    };
    getDataByResult(params).then((res: any) => {
      const list = res.data.list;
      setOptions(list);
    });
    querySelectFn();
  };
  const querySelectFn = useCallback(
    debounce(() => {
      handleFilter().then(() => {
        setQueryType({ current: 'current' });
      });
    }, 500),
    [],
  );
  return (
    <div className={styles.filter}>
      <Form form={form} labelCol={{ span: 0 }}>
        <Row gutter={8}>
          <Col span={6}>
            <Form.Item label="" name="keywords">
              <AutoComplete
                placeholder="请输入"
                popupMatchSelectWidth={false}
                onSelect={(e) => onSelect(e)}
                className={styles.searchInput}
                onInputKeyDown={handleKeyDown}
                onSearch={onSearch}
              >
                {options?.map((e, index) => {
                  return (
                    <AutoComplete.Option key={index} value={e} style={{ width: getWidth(650) }}>
                      {index === 0 && <MyTitle />}
                      <MyContent
                        data={{
                          contactName: e.contactName,
                          username: e.username,
                          mobile: e.mobile,
                          isFriend: e.isFriend,
                          email: e.email,
                        }}
                      />
                    </AutoComplete.Option>
                  );
                })}
              </AutoComplete>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Space>
              <Button type="primary" onClick={queryAll}>
                全部查询
              </Button>
              <Button type="primary" onClick={queryResults}>
                结果查询
              </Button>
            </Space>
          </Col>
          <Col span={12}>
            <div className={styles.rightBar}>
              <Space></Space>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default Component;
