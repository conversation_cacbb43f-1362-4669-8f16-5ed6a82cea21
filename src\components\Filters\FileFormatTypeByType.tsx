/**
 * 表格列筛选-文件格式，区别于标准数据
 */
import useLibraryStore from '@/store/useLibraryStore';
import MenuFilter from './MenuFilter';

// 人事管理
export const employeeFileType = [
  'library_file_type_doc',
  'library_file_type_pic',
  'library_file_type_ppt',
  'library_file_type_table',
];
// 重新分类
export const resetClassifyFileType = [
  'library_file_type_doc',
  'library_file_type_pic',
  'library_file_type_ppt',
  'library_file_type_table',
  'library_file_type_audio',
  'library_file_type_video',
];

const Component = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  close,
  clearFilters,
  config,
}: any) => {
  const [fileFormatTypeList] = useLibraryStore((state) => [state.fileFormatTypeList]);
  const toggleItem = (key: string) => {
    if (selectedKeys.includes(key)) {
      setSelectedKeys(selectedKeys.filter((k: string) => k !== key));
    } else {
      setSelectedKeys([...selectedKeys, key]);
    }
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
    } else if (type === 'ok') {
      confirm();
      close();
    }
  };

  const typeProps = {
    employee: employeeFileType,
    resetClassifyFile: resetClassifyFileType,
  };

  const getItems = () => {
    if (config?.module) {
      const moduleKey = config.module as keyof typeof typeProps;
      const otherList = typeProps[moduleKey];
      return fileFormatTypeList.filter((file: any) => otherList.includes(file.key));
    }
    return fileFormatTypeList;
  };
  return (
    <MenuFilter
      items={getItems()}
      selectedKeys={selectedKeys}
      onSelect={toggleItem}
      onSubmit={submit}
    />
  );
};

export default Component;
