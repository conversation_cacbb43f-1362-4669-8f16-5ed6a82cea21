import { CommonResponse } from '../editor/type';
import http from '../index';
import { DetailByIdRes, GetArticleListParam, GetArticleListRes, SaveParams } from './types';

/**获取文档列表 */
export function getArticleList(params: GetArticleListParam) {
  return http.get<CommonResponse<GetArticleListRes>>({
    url: '/web-api/approval/article/page',
    params,
  });
}

export function getArticleListPost(data: GetArticleListParam) {
  return http.post<CommonResponse<GetArticleListRes>>({
    url: '/web-api/approval/article/page',
    data,
  });
}

/**获取文详情表 */
export function getArticleDetails(id: string) {
  return http.get<CommonResponse<DetailByIdRes>>({
    url: `/web-api/approval/article/detail/${id}`,
  });
}

/** 新增岗位审批 */
export function postArticle(data: SaveParams) {
  return http.post<CommonResponse<string>>({
    url: '/web-api/approval/article/save',
    data,
  });
}

/**获取某类型文章最新内容 */
export function getArticleLatest(typeCode: string) {
  return http.get<CommonResponse<DetailByIdRes>>({
    url: `/web-api/approval/article/latest/${typeCode}`,
  });
}

/**删除文章 */
export function delArticle(id: string) {
  return http.put<CommonResponse>({
    url: `/web-api/approval/article/deListed/${id}`,
  });
}
