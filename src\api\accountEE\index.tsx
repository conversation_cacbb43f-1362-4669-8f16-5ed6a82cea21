import request from '../index';
//  管理员配置1
export const getManagePage = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/manage/userRoleRef/page',
    data,
  });
};
//  部门配置|岗位|工作内容1
export const getMobileConfigNew = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/config/log/page',
    data,
  });
};
// 获取账单分页（我要支付）1
export const getPay = (data: any) => {
  return request.get({
    url: '/web-api/bill/page',
    params: data,
  });
};
// 用户存储信息分页1
export const getStorage = (data: any) => {
  return request.get({
    url: '/web-api/bill/user/storage/page',
    params: data,
  });
};

// 删除账户
export const deletedBill = (data: any) => {
  return request.put({
    url: '/web-api/ee/account/manage/userRoleRef/delete',
    data,
  });
};

// 删除部门管理
export const deletedDep = (data: any) => {
  return request.put({
    url: '/web-api/ee/account/config/log/delete',
    data,
  });
};

// 删除账单1
export const deletedAccount = (data: any) => {
  return request.delete({
    url: '/web-api/bill/deleted',
    data,
  });
};

// 删除-恢复存储信息
export const deletedStorage = (data: any) => {
  return request.delete({
    url: '/web-api/bill/user/storage/deleted',
    data,
  });
};

// 登录回顾获得指定用户账单分页
export const getBillRetrospect = (data: any) => {
  return request.get({
    url: '/web-api/bill/login/retrospect/page',
    params: data,
  });
};

// 登录回顾获得指定用户存储信息分页
export const getStorageRetrospect = (data: any) => {
  return request.get({
    url: '/web-api/bill/user/storage/login/retrospect/page',
    params: data,
  });
};
