import { getHeight } from '@/utils/common';
import { useEffect, useRef } from 'react';

const useTableHeight = (calHeight?: number) => {
  const tableContainerRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const calculateTableHeight = () => {
      if (tableContainerRef.current) {
        const containerTop = tableContainerRef.current.offsetTop;
        const innerHeight = window.innerHeight;

        const height = innerHeight - containerTop - getHeight(90) - (calHeight || 0);

        const body = tableContainerRef.current.querySelector('.ant-table-body') as HTMLElement;

        if (body) {
          body.style.height = `${height}px`;
        }
      }
    };

    calculateTableHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', calculateTableHeight);

    return () => {
      window.removeEventListener('resize', calculateTableHeight);
    };
  }, []);
  return tableContainerRef;
};
export default useTableHeight;
