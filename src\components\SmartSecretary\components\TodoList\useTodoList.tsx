import { updateTodoList } from '@/api/todoList';
import { TodoListResProps } from '@/api/todoList/module';
import AudioType from '@/assets/images/todolist/audioType.png';
import OtherType from '@/assets/images/todolist/otherType.png';
import PdfType from '@/assets/images/todolist/pdf.png';
import PicType from '@/assets/images/todolist/picType.png';
import PptType from '@/assets/images/todolist/ppt.png';
import SpType from '@/assets/images/todolist/sp.png';
import Video from '@/assets/images/todolist/video.png';
import VideoType from '@/assets/images/todolist/videoType.png';
import VideoWj from '@/assets/images/todolist/videoWj.png';
import Voice from '@/assets/images/todolist/voice.png';
import YbWordType from '@/assets/images/todolist/ybwordType.png';
import Avatar from '@/components/Avatar';
import useCtxStore from '@/pages/Share/Category/useCtxStore';
import getMediaUrl from '@/utils/getMediaUrl';
import { useNavigate } from 'react-router-dom';
import styles from './index.module.less';

const useTodoList = (data: TodoListResProps) => {
  const extra1 = JSON.parse(data.extra1); // 待办事项的额外属性
  const navigate = useNavigate();
  const history = useNavigate();
  const [setGroupName] = useCtxStore((state) => [state.setGroupName]);

  // 更新待办事项状态(已读未读 状态(0-未读，1-已读)、忽略标记（0-未忽略，1-已忽略）)
  const updateTodo = async (id: number, params: any) => {
    try {
      await updateTodoList({ id, ...params });
    } catch (error) {
      //
    }
  };
  const goPage = (url: string, stateParams: any) => {
    navigate(url, {
      state: {
        ...stateParams,
        fromModule: 'todoList',
        content: data,
      },
    });
    // const goShary = () => {
    //   setGroupName(data.groupName);
    //   history('/share/category', {
    //     state: {
    //       groupId: data.receiveGroupId,
    //       groupName: data.groupName,
    //       // memberNum: data.memberNum,
    //     },
    //   });
    // };
    // const go = () => {
    //   // // 判断是否跳群组
    //   // if (data.receiveGroupId) {
    //   //   goShary();
    //   // } else {
    //   navigate(url, {
    //     state: {
    //       ...stateParams,
    //       fromModule: 'todoList',
    //       content: data,
    //     },
    //   });
    //   // }
    // };

    // // 未读状态需要更新待办事项状态
    // if (!data.status) {
    //   updateTodo(data.id, params).then(() => {
    //     go();
    //   });
    // } else {
    //   go();
    // }
  };
  // 获得头像
  const getAvatar = () => {
    const apiAvatar = data.groupAvatar || data.sendAvatar;
    const myAvatar = (
      <Avatar
        userName={data.groupName || data.sendRealName}
        Avatar={apiAvatar ? getMediaUrl(apiAvatar) : ''}
        isUser={!data.receiveGroupId}
      />
    );
    return myAvatar;
  };
  // 获得待办事项类型
  const getType = () => {
    const previewText = data.status ? '已浏览' : '浏览';
    const callText = data.status ? '已回拨' : '回拨';
    // 待办类型（100-裕邦邮箱, 101-语音通话, 102-视频通话, 103-视频通话正文邮件, 104-语音通话正文, 105-视频会议, 107-音频, 108-图片, 109-视频, 110-裕邦文档, 111-其他, 112-表格, 113-ppt幻灯片, 114-pdf, 115-程序文件, 116-文档格式,117-今日新闻, 3-审批）
    switch (data.todoType) {
      case 100:
        return {
          className: styles.tag_red,
          typeTitle: '裕邦邮箱',
          avatar: getAvatar(),
          primaryBtnText: data.status ? '已查看' : '查看',
        };
      case 101: // 语音通话未接
        return {
          className: styles.tag_red,
          typeTitle: '',
          avatar: Voice,
          primaryBtnText: callText,
        };
      case 102: // 视频通话未接
        return {
          className: styles.tag_red,
          typeTitle: '',
          avatar: Video,
          primaryBtnText: callText,
        };
      case 103: // 视频通话正文邮件未接
        return {
          className: styles.tag_red,
          typeTitle: '',
          avatar: getAvatar(),
          primaryBtnText: callText,
        };
      case 104: // 语音通话正文未接
        return {
          className: styles.tag_red,
          typeTitle: '',
          avatar: getAvatar(),
          primaryBtnText: callText,
        };
      case 105:
        return {
          className: styles.tag_red,
          typeTitle: '',
          avatar: VideoWj,
          primaryBtnText: extra1.meetingEnd ? '重新发起' : '加入',
        };
      case 107:
        return {
          className: styles.tag_purple1,
          typeTitle: '音频文件',
          avatar: AudioType,
          primaryBtnText: previewText,
        };
      case 108:
        return {
          className: styles.tag_green,
          typeTitle: '图片文件',
          avatar: PicType,
          primaryBtnText: previewText,
        };

      case 109:
        return {
          className: styles.tag_purple2,
          typeTitle: '视频文件',
          avatar: VideoType,
          primaryBtnText: previewText,
        };

      case 110:
        return {
          className: styles.tag_blue,
          typeTitle: '裕邦文档',
          avatar: YbWordType,
          primaryBtnText: previewText,
        };

      case 111:
        return {
          className: styles.tag_yellow,
          typeTitle: '其他文件',
          avatar: OtherType,
          primaryBtnText: previewText,
        };
      case 112:
        return {
          className: styles.tag_green,
          typeTitle: '表格文件',
          avatar: OtherType,
          primaryBtnText: previewText,
        };
      case 113:
        return {
          className: styles.tag_red,
          typeTitle: '演示文件',
          avatar: PptType,
          primaryBtnText: previewText,
        };
      case 114:
        return {
          className: styles.tag_red2,
          typeTitle: 'PDF文件',
          avatar: PdfType,
          primaryBtnText: previewText,
        };
      case 115:
        return {
          className: styles.tag_yellow,
          typeTitle: '程序文件',
          avatar: OtherType,
          primaryBtnText: previewText,
        };
      case 116:
        return {
          className: styles.tag_yellow,
          typeTitle: '文档格式',
          avatar: OtherType,
          primaryBtnText: previewText,
        };
      case 117:
        return {
          className: styles.tag_yellow,
          typeTitle: '今日新闻',
          avatar: OtherType,
          primaryBtnText: previewText,
        };
      case 3:
        return {
          className: styles.tag_yellow,
          typeTitle: '',
          avatar: SpType,
          primaryBtnText: '去审批',
        };
      default:
        return {
          className: styles.tag_yellow,
          typeTitle: '其他文件',
          avatar: OtherType,
          primaryBtnText: previewText,
        };
    }
  };
  return {
    updateTodo,
    goPage,
    getType,
  };
};
export default useTodoList;
