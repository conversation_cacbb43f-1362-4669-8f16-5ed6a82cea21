import useAppStore from '@/store/useAppStore';
import useCppBridgeStore, { cppbRequest } from '@/store/useCppBridgeStore';
import useUserStore from '@/store/useUserStore';
import { getRandom } from '@/utils/common';
import { CppParams } from './typing';

const { accessToken } = useUserStore.getState();

// 判断是否是base64
function isBase64(str: any) {
  // 检查是否所有字符都在Base64字符集中
  const base64Pattern = /^[A-Za-z0-9+/]+={0,2}$/;
  if (!base64Pattern.test(str)) return false;

  // 长度应该是4的倍数
  if (str.length % 4 !== 0) return false;

  // 尝试解码并重新编码，看结果是否与原字符串相同
  try {
    const decodedStr = atob(str);
    const reencodedStr = btoa(decodedStr);
    return str === reencodedStr;
  } catch (e) {
    // 如果解码失败，说明不是有效的Base64
    return false;
  }
}

// Base64解码
function base64ToUtf8(base64Str: any) {
  const bytes = atob(base64Str)
    .split('')
    .map((char) => char.charCodeAt(0));
  return new TextDecoder().decode(new Uint8Array(bytes));
}

async function getHtmlContent(data: any) {
  let env = '';
  const domain = window.location.host;
  if (process.env.NODE_ENV === 'development' || domain.includes('client-dev')) {
    env = 'dev';
  } else if (domain.includes('client-sit')) {
    env = 'sit';
  } else if (domain.includes('client-uat')) {
    env = 'uat';
  }
  const result = await fetch(`/smartweb/${env}/api/web-api/webconvert`, {
    method: 'POST',
    headers: {
      authorization: `Bearer ${accessToken}`,
    },
    body: data,
  });

  return result;
}

const stringToJson = (decodedString: string) => {
  const dataInfo: CppParams = JSON.parse(decodedString);
  console.log('转JSON后的数据', dataInfo);
  useCppBridgeStore.getState().setResponse(dataInfo);
  try {
    if (dataInfo.id === 0 && dataInfo.msg === 'wake') {
      window.checkedWebsocket();
    }
  } catch (error) {
    console.log(error);
  }
};

/**
 * 从浏览器接收消息(c++调用js)
 * @param data CppParams as string
 */
async function receiveFromBrowser(data: string) {
  console.log('receiveFromBrowser', data);
  const decodedString = data;
  // 由于右键菜单-网页编辑会使用base64编码传递过来，故增加此逻辑
  if (isBase64(data)) {
    const res = base64ToUtf8(data); // 使用atob()进行Base64解码
    const dataInfo: CppParams = JSON.parse(res);
    const { content, pageurl, processable } = dataInfo.data.params; // 拿到正文
    console.log('processable', processable);

    if (processable === 'false' || !processable) {
      useCppBridgeStore.getState().setResponse(dataInfo);
      return;
    }
    console.log('res', content);

    const blob = new Blob([content], { type: 'text/plain;charset=UTF-8' });
    const file = new File([blob], `htmlEdit${new Date().getTime()}.txt`);
    const formData = new FormData();
    formData.append('url', pageurl);
    formData.append('file', file);
    try {
      const response = await getHtmlContent(formData);
      const aiContentRes = await response.json();
      // 加随机数处理网页编辑，多次抓取相同的网页不重复渲染的问题
      dataInfo.data.params.content = `<h1 class='class_${getRandom(10)}'>${aiContentRes.data.title}</h1>${aiContentRes.data.content}`;
      useCppBridgeStore.getState().setResponse(dataInfo);
    } catch (error) {
      //
    }
  } else {
    stringToJson(decodedString);
  }
}

/**
 * 调用c++的方法
 * @param data CppParams
 */
function callCppFunction(data: CppParams) {
  // @ts-expect-error C++注入全局变量
  if (window.sendToBrowser) {
    // @ts-expect-error C++注入全局变量
    window.sendToBrowser(JSON.stringify(data));
  }
}
function onPageLoad() {
  //因刷新页面后 再次打招呼由于c++ 不会回复
  if (sessionStorage.getItem('yb-tab-height') === 'has') {
    // 去掉tabHeight
    document.documentElement.style.setProperty('--yb-tab-height', '0px');
    return false;
  }
  // 初次跟cpp打招呼
  cppbRequest({
    module: '',
    method: 'firstmeeting',
    data: {
      name: 'mainpage',
    },
  }).then((res) => {
    if (res.code === 0) {
      // 得到cpp端回复
      useAppStore.getState().setChannel('cpp');
      sessionStorage.setItem('yb-tab-height', 'has');
      // 去掉tabHeight
      document.documentElement.style.setProperty('--yb-tab-height', '0px');
    }
  });
}
window.receiveFromBrowser = receiveFromBrowser;
window.callCppFunction = callCppFunction;
window.onload = onPageLoad;
