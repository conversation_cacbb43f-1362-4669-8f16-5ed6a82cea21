import type { FilePreviewAPI } from '@/components/FliePreview';
import FilePreview from '@/components/FliePreview';
import { Button, Space, Table } from 'antd';
import { useContext, useMemo, useRef } from 'react';
import Context from '../Context';
import { getColumns } from '../SelectFiles/columns';
import styles from './index.module.less';

const Component = () => {
  const filePreviewRef = useRef<FilePreviewAPI>();
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [selectedFileList, setSelectedFileList, selectedFileMap, setSelectedFileMap] =
    useMainPanelCtxStore!((state) => [
      state.selectedFileList,
      state.setSelectedFileList,
      state.selectedFileMap,
      state.setSelectedFileMap,
    ]);
  const columns = useMemo(() => {
    const isPreview = config?.module === 'preview';
    return getColumns(false, config).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              {(isPreview || selectedFileList.length === 0) && <span>操作</span>}
              {!isPreview && selectedFileList.length > 0 && (
                <Button
                  type="primary"
                  size="small"
                  ghost
                  onClick={() => {
                    setSelectedFileMap({});
                    setSelectedFileList([]);
                  }}
                >
                  全部取消
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          return (
            <Space>
              {!isPreview && (
                <Button
                  ghost
                  type="primary"
                  size="small"
                  onClick={() => {
                    const map = { ...selectedFileMap };
                    delete map[row.id];
                    setSelectedFileMap(map);
                    setSelectedFileList(Object.values(map));
                  }}
                >
                  取消
                </Button>
              )}
              <Button
                style={{ paddingLeft: 0, paddingRight: 0 }}
                type="link"
                size="small"
                disabled={isPreview}
                onClick={() => {
                  preview(row);
                }}
              >
                浏览
              </Button>
            </Space>
          );
        };
      }
      return item;
    });
  }, [selectedFileList, selectedFileMap, config]);
  const preview = (row: any) => {
    filePreviewRef.current?.open({ ...row });
  };

  return (
    <div className={styles.list}>
      <div className={styles.title}>
        已选{config?.typeName || '文件'}({selectedFileList.length})
      </div>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={selectedFileList}
        rowKey={'id'}
        scroll={{ y: 163 }}
        pagination={false}
      />
      <FilePreview ref={filePreviewRef} />
    </div>
  );
};

export default Component;
