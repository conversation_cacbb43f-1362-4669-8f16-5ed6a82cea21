import { libraryCallbackUpload } from '@/api/library';
import { useLoadingListStore } from '@/components/LoadingList';
import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create } from '@/store';
import useAlbumStore from '@/store/useAlbumStore';
import { cwsEmitter } from '@/store/useCppWebSocketStore';
import useFromModuleStore from '@/store/useFromModuleStore';
import { add, divide, subtract } from 'lodash';

export interface State {
  mainPanelOpen: boolean;
  selectedFileList: Record<string, any>[];
  selectedFileMap: Record<string, any>;
  uploadFileList: Record<string, any>[];
  uploadFileMap: Record<string, any>;
  uploadFilePathList: Record<string, any>[];
  errorFiles: number;
  successFiles: number;
  abortFiles: number;
  filesSize: number;
  uploadedFilesSize: number;
  loadingStatus: 'init' | 'waiting' | 'loading' | 'pause' | 'complete';
  timestamp: number;
}

export interface SetState {
  setMainPanelOpen: Setter;
  setSelectedFileList: Setter;
  setSelectedFileMap: Setter;
  setUploadFileList: Setter;
  setUploadFileMap: Setter;
  setUploadFilePathList: Setter;
  setFilesSize: Setter;
  setUploadedFilesSize: Setter;
  setErrorFiles: Setter;
  setSuccessFiles: Setter;
  setAbortFiles: Setter;
  setLoadingStatus: Setter;
  setTimestamp: Setter;
}

interface Config {
  module: string;
  type: string;
  [prop: string]: any;
}

const getImgIsStandard = (file: any) => {
  if (file.fileFormatType === 'library_file_type_pic') {
    return file.width > file.height ? 1 : file.width < file.height ? 2 : 3;
  }
};

export const setPercent = (loadingSize: number, totalSize: number, loadingStatus: string) => {
  if (totalSize === 0) {
    return loadingStatus === 'complete' ? 100 : 0;
  } else {
    console.log('loadingSize', loadingSize);
    console.log('totalSize', totalSize);
    return Math.floor(divide(loadingSize, totalSize) * 100);
  }
};

const createUseCtxStore = function (config: Config) {
  const { fromModule, fromModuleQuery } = useFromModuleStore.getState();
  const useCtxStore = autoUseShallow<State, SetState>(
    create(
      autoCreateSetters<State>({
        mainPanelOpen: false,
        selectedFileList: [],
        selectedFileMap: {},
        uploadFileList: [],
        uploadFileMap: {},
        uploadFilePathList: [],
        filesSize: 0,
        uploadedFilesSize: 0,
        errorFiles: 0,
        successFiles: 0,
        abortFiles: 0,
        loadingStatus: 'init',
        timestamp: Date.now(),
      }),
    ),
  );

  useCtxStore.subscribe((state, prev) => {
    if (
      (state.abortFiles > prev.abortFiles ||
        state.errorFiles > prev.errorFiles ||
        state.successFiles > prev.successFiles) &&
      state.abortFiles + state.errorFiles + state.successFiles === state.uploadFilePathList.length
    ) {
      state.setLoadingStatus('complete');
      state.setUploadedFilesSize(state.filesSize);
      const data = Object.values(state.uploadFileMap)
        .filter((file: any) => file.status === 'success')
        .map((file: any) => {
          return {
            title: file.fileName,
            source: 10,
            filePath: file.remotePath,
            fileType: file.fileType,
            fileFormatType: file.fileFormatType,
            fileSize: file.fileSize,
            imgWidth: file.width,
            imgHeight: file.height,
            imgIsStandard: getImgIsStandard(file),
            duration: file.duration,
            groupId: fromModuleQuery?.groupId,
            thumbnailOne: file.thumbnailId
              ? state.uploadFileMap[file.thumbnailId].remotePath
              : undefined,
            secretKey: file.secretKey,
          };
        });
      if (data.length) {
        const list = [];
        let i = 0;
        let j = 500;
        let sliceData = data.slice(i, j);
        while (sliceData.length) {
          list.push(libraryCallbackUpload(sliceData));
          i = j;
          j += 500;
          sliceData = data.slice(i, j);
        }
        Promise.all(list).then(() => {
          if (data.some((item) => item.fileFormatType === 'library_file_type_pic')) {
            useAlbumStore.setState({ isImgsUploaded: true });
          }
        });
      }
    }
  });

  useCtxStore.subscribe((state, prev) => {
    if (
      state.uploadedFilesSize > prev.uploadedFilesSize ||
      state.loadingStatus !== prev.loadingStatus
    ) {
      const { setLoadingList } = useLoadingListStore.getState();
      let status: string;
      switch (state.loadingStatus) {
        case 'init':
        case 'waiting':
          status = '等待中';
          break;
        case 'loading':
          status = `${setPercent(state.uploadedFilesSize, state.filesSize, state.loadingStatus)}%`;
          break;
        case 'pause':
          status = '暂停上传';
          break;
        case 'complete':
          status = '已完成';
          break;
      }
      setLoadingList((prev: any) => {
        return [
          ...prev.map((item: any) => {
            if (item.module === config.module && item.type === config.type) {
              item.status = status;
            }
            return { ...item };
          }),
        ];
      });
    }
  });

  cwsEmitter.on(`${config.module}-${config.type}.uploadfile`, (response: any) => {
    const {
      uploadedFilesSize,
      uploadFileMap,
      setErrorFiles,
      setSuccessFiles,
      setUploadedFilesSize,
      setSelectedFileList,
      setUploadFileList,
    } = useCtxStore.getState();
    const items = response.data;
    let errorFiles = 0;
    let successFiles = 0;
    let filesSize = 0;
    items.forEach((item: any) => {
      const { filepath, nowsize, totalsize, remoteFilepath, status, MD5 } = JSON.parse(item);
      const file = uploadFileMap[MD5];
      console.log(uploadedFilesSize)
      if (!file || file.status === 'error' || file.status === 'success') {
        return;
      }
      if (status === 'error') {
        file.status = 'error';
        file._status = '';
        errorFiles += 1;
      } else {
        if (status === 'success') {
          filesSize += subtract(file.fileSize, file.uploadedSize);
          file.status = 'success';
          file._status = '';
          file.uploadedSize = file.fileSize;
          file.remotePath = remoteFilepath;
          console.log(filesSize);
          successFiles += 1;
        } else if (status === 'loading') {
          filesSize += subtract(nowsize, file.uploadedSize);
          file.status = 'loading';
          console.log('loading', filesSize);
          file.uploadedSize = Math.min(nowsize, file.fileSize);
        }
      }
    });
    setErrorFiles((prev: any) => prev + errorFiles);
    setSuccessFiles((prev: any) => prev + successFiles);
    setUploadedFilesSize((prev: number) => add(prev, filesSize));
    setUploadFileList((prev: any) => [...prev]);
    setSelectedFileList((prev: any) => [...prev]);
  });

  return useCtxStore;
};

export default createUseCtxStore;
