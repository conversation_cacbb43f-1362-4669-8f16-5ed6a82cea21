import { FC, useState, useEffect, useRef } from 'react';
import { Button, Table, TableProps } from 'antd';
import styles from './CustomTable.module.less';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
let scrollStep: number = 0;
let currentScrollTop = 0;
interface CustomTableProps extends TableProps {
  customOnscroll?:(e:React.UIEvent<HTMLDivElement>)=> void;
}
const CustomTable = (props: CustomTableProps) => {
  const [topFlag, setTopFlag] = useState<boolean>(false);
  const tableRef = useRef<any>(null);
  const [scrollTop, setScrollTop] = useState<number>(0);
  useEffect(() => {
    if (props.virtual) {
      const scrollY: any = props.scroll?.y || 40;
      scrollStep = scrollY * 0.1;
      console.log(scrollStep);
    }
  }, []);
  useEffect(() => {
    if (tableRef.current) {
      tableRef.current.scrollTo({
        top: scrollTop,
      });
    }
  }, [scrollTop]);

  const getCustomTableBoxClassName = () => {
    return props.virtual && topFlag
      ? `${styles.VirtualScrollTop10}`
      : `${styles.VirtualScrollTop70}`;
  };
  return (
    <>
      <div className={styles.CustomTable}>
        <div className={styles.CustomTableBox + ' ' + getCustomTableBoxClassName()}>
          <Table
            ref={tableRef}
            style={{ width: '100%' }}
            {...props}
            onScroll={(e: React.UIEvent<HTMLDivElement>) => {
              if(props&&props.customOnscroll){
                props.customOnscroll(e)
              }
              const target = e.target as HTMLDivElement
              const dom = target.parentNode&&target.parentNode.querySelector('.ant-table-tbody-virtual-scrollbar-thumb');
              if (dom) {
                const style = window.getComputedStyle(dom);
                console.log(style.top);
                const top = style.top.split('px')[0];
                currentScrollTop = target.scrollTop;
                console.log(top);
                if (Number(top) > 82) {
                  setTopFlag(true);
                } else {
                  setTopFlag(false);
                }
              }
            }}
          />
        </div>
        {props.virtual ? <div className={styles.VirtualScroll}></div> : null}
        {props.virtual ? (
          <span
            className={styles.upArrow}
            onClick={() => {
              setScrollTop(currentScrollTop + scrollStep);
            }}
          >
            <CaretDownOutlined />
          </span>
        ) : null}
        {props.virtual ? (
          <span
            className={styles.downArrow}
            onClick={() => {
             setScrollTop(currentScrollTop - scrollStep);
            }}
          >
            <CaretUpOutlined />
          </span>
        ) : null}
      </div>
    </>
  );
};

export default CustomTable;
