@import url('../FlieDownload/FlieDownload.module.less');
.FileChoose {
  width: 720px;
  min-height: 300px;
  .searchBox {
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px;
    padding: 0 8px;
    box-sizing: border-box;
    margin-bottom: 10px;
    .searchInput {
      display: flex;
      align-items: center;
      margin-right: 10px;
      span {
        padding-right: 8px;
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
      }
      input {
        width: 176px;
        height: 32px;
        border-radius: 4px;
        background: #fff;
        box-sizing: border-box;
        border: 1px solid rgba(0, 0, 0, 0.15);
        font-size: 12px;
        padding-left: 8px;
        outline: none;
      }
    }
    .searchButton {
      width: 88px;
      height: 32px;
      border-radius: 4px;
      background: #3d5afe;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      margin-right: 8px;
      font-size: 14px;
      cursor: pointer;
      &:last-of-type {
        margin-right: 0;
      }
    }
  }
  .TableBox {
    width: 100%;
    height: 461px;
    box-sizing: border-box;
    padding: 0 8px;
    :global {
      .ant-table-wrapper table,
      .ant-table-container,
      .ant-table-container,
      .ant-table-wrapper .ant-table .ant-table-header {
        border-radius: 0 !important;
      }
      .ant-table-cell-scrollbar:not([rowspan]) {
        box-shadow: none;
      }
      .ant-table {
        .ant-table-thead > tr {
          border-radius: 0 !important;
          &:first-child > *:first-child,
          &:first-child > *:last-child {
            border-start-start-radius: 0;
            border-start-end-radius: 0;
          }
          > th {
            height: 40px;
            line-height: 40px;
            padding: 0 !important;
            font-size: 14px !important;
            font-weight: normal !important;
            background-color: #e8eaf6 !important;
            &::before {
              height: 0 !important;
            }
          }
        }
        tr:nth-of-type(2n),
        div.ant-table-row:nth-of-type(2n) {
          background: #f8fafd;
        }
        td,
        .ant-table-cell {
          height: 40px;
          line-height: 40px;
          padding: 0 !important;
          font-size: 14px !important;
        }
        div.ant-table-cell {
          display: flex;
          align-items: center;
          justify-content: center;
          > span {
            width: 100%;
            text-align: left;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: keep-all;
          }
        }
      }
    }
    .activedTr {
      color: rgba(0, 0, 0, 0.25);
    }
    .tableHeaderBut {
      width: 72px;
      height: 26px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 12px;
      border-radius: 4px;
      border: 1px solid #3d5afe;
      color: #3d5afe;
    }
    .btnsAll {
      width: 100%;
      display: flex;
      span {
        width: 44px;
        height: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
        border-radius: 4px;
        &:first-of-type {
          color: #3d5afe;
        }
      }
      .activedBut {
        background-color: #7986cb;
        color: #fff;
      }
      .nomalBut {
        border: 1px solid #3d5afe;
        color: #3d5afe;
      }
    }
  }
  .OperateButList {
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: center;
    padding: 7px 0 20px 0;
  }
  .ConfirmBut {
    width: 96px;
    height: 40px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #3d5afe;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
  }
}
