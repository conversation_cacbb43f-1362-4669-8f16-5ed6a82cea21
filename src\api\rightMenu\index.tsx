import api, { Extra } from '../index';
import { getOnlineChildUsersParams } from './interface';

// 获取在线子账户
export const getOnlineChildUsers = (data: getOnlineChildUsersParams, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/account/manage/onlineUser/page',
      data,
    },
    extra,
  );
};

// 获取在线子账户终端
export const getOnlineClients = (params: { userId: string }, extra?: Extra) => {
  return api.get(
    {
      url: '/web-api/account/manage/onlineUser/listOnlineClients',
      params,
    },
    extra,
  );
};

// 发起桌面监控
export const monitorDesk = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/media/monitor/desktop',
      data,
    },
    extra,
  );
};

// 获取桌面监控记录
export const getMonitorDeskInfo = (params: any, extra?: Extra) => {
  return api.get(
    {
      url: '/web-api/media/monitor/desktop',
      params,
    },
    extra,
  );
};

// 确认分享屏幕
export const shareScreen = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/media/shareScreen',
      data,
    },
    extra,
  );
};

// 加入分享屏幕
export const joinShareScreen = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/media/shareScreen/join',
      data,
    },
    extra,
  );
};

// 拒绝加入分享屏幕
export const refuseJoin = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/media/shareScreen/refuse',
      data,
    },
    extra,
  );
};

// 分享屏幕---获取在线人数
export const getShareOnlineUser = (params: any, extra?: Extra) => {
  return api.get(
    {
      url: '/web-api/media/shareScreen/onlineUsers/' + params,
    },
    extra,
  );
};

// 已分享的人
export const sharedUsers = (params: any, extra?: Extra) => {
  return api.get(
    {
      url: '/web-api/media/shareScreen/roomUsers/' + params,
    },
    extra,
  );
};
