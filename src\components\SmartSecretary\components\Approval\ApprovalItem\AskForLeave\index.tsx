import { getPageStaff } from '@/api/employee';
import Drawer from '@/components/Drawer';
import Header from '@/components/SmartSecretary/publicComponents/Header';
import Select from '@/components/SmartSecretary/publicComponents/Select';
import SelectEmployee from '@/components/SmartSecretary/publicComponents/SelectEmployee';
import { Popup } from '@/components/SmartSecretary/publicComponents/vantComponents';
import useApproval from '@/store/useApproval';
import { formatDate } from '@/utils/date';
import { Button as AntdButton } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { Button, DatetimePicker, Form, Input } from 'react-vant';
import styles from './index.module.less';
const Component = ({
  title,
  open,
  onClose,
  approvalData,
  onSubmit,
}: {
  title: string;
  open: boolean;
  onClose: () => void;
  approvalData: any;
  onSubmit: (values: any) => void;
}) => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [openErrorTime, setOpenErrorTime] = useState(false); // 异常时间弹窗
  const [openErrorType, setOpenErrorType] = useState(false); // 异常类型弹窗
  const [openEmployee, setOpenEmployee] = useState(false); // 异常人员弹窗

  const [formData, setFormData] = useState(approvalData.attendanceApproval);

  const [kqycDictList, flatConfigTreeList] = useApproval((state) => [
    state.kqycDictList,
    state.flatConfigTreeList,
  ]); // 考勤异常枚举、审批树
  const [employeeList, setEmployeeList] = useState([]);

  const getYcEmp = () => {
    getPageStaff({ pageNo: 1, pageSize: 1000 }).then((res: any) => {
      setEmployeeList(res.data.list || []);
    });
  };

  // 获得异常类别数组
  const getYcTypeList = useCallback(() => {
    console.log('flatConfigTreeList', flatConfigTreeList);

    const ycTypeList = flatConfigTreeList.find((item: any) => item.code === '201') || {}; // 异常类型
    console.log('ycTypeList', ycTypeList);
    return (ycTypeList.children || []).map((item: any) => ({
      label: item.approvalName,
      value: item.code,
    }));
  }, []);

  useEffect(() => {
    getYcEmp();
  }, []);

  const onFinish = (values: any) => {
    console.log(values);
    onSubmit({
      ...formData,
      money: values.money,
    });
  };
  console.log('formData', formData);

  const onChange = (newItem: any) => {
    setFormData((prev: any) => ({
      ...prev,
      ...newItem,
    }));
  };

  return (
    <Drawer title={<Header title={title} onCancel={onClose} />} onClose={onClose} open={open}>
      <div className={styles.employeeListContainer}>
        <Form
          form={form}
          onFinish={onFinish}
          initialValues={{
            ...formData,
            code: formData.codeName,
            time: formData.timeName,
            userName: formData.userRealName,
          }}
          footer={
            <div style={{ margin: '16px 16px 0' }}>
              <Button nativeType="submit" type="primary" block>
                提交
              </Button>
            </div>
          }
        >
          <Form.Item
            rules={[{ required: true, message: '请选择异常日期' }]}
            name="date"
            label="异常日期"
          >
            <Input
              readOnly
              align="right"
              suffix={
                <AntdButton size="small" type="primary" ghost onClick={() => setVisible(true)}>
                  选择
                </AntdButton>
              }
            />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: '请选择异常时间' }]}
            name="time"
            label="异常时间"
          >
            <Input
              readOnly
              align="right"
              suffix={
                <AntdButton
                  size="small"
                  type="primary"
                  ghost
                  onClick={() => setOpenErrorTime(true)}
                >
                  选择
                </AntdButton>
              }
            />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: '请选择异常类型' }]}
            name="code"
            label="异常类型"
          >
            <Input
              align="right"
              readOnly
              suffix={
                <AntdButton
                  size="small"
                  type="primary"
                  ghost
                  onClick={() => setOpenErrorType(true)}
                >
                  选择
                </AntdButton>
              }
            />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: '请选择异常人员' }]}
            name="userName"
            label="异常人员"
          >
            <Input
              align="right"
              readOnly
              suffix={
                <AntdButton size="small" type="primary" ghost onClick={() => setOpenEmployee(true)}>
                  选择
                </AntdButton>
              }
            />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: '请输入考勤扣款' }]}
            name="money"
            label="考勤扣款"
          >
            <Input align="right" suffix="元" />
          </Form.Item>
        </Form>
      </div>
      <Popup visible={visible} position="bottom" round onClose={() => setVisible(false)}>
        <DatetimePicker
          title="选择年月日"
          type="date"
          minDate={new Date(2020, 0, 1)}
          maxDate={new Date(2025, 10, 1)}
          defaultValue={new Date(formData.date)}
          onConfirm={(val: any) => {
            form.setFieldValue('date', formatDate(val, 'YYYY-MM-DD'));
            onChange({ date: formatDate(val, 'YYYY-MM-DD') });
            setVisible(false);
          }}
        />
      </Popup>
      <Select
        title="异常时间"
        open={openErrorTime}
        value={formData.timeName}
        onClose={() => setOpenErrorTime(false)}
        onConfirm={(val: any) => {
          form.setFieldValue('time', val.label);
          onChange({
            time: val.value,
            timeName: val.label,
          });

          setOpenErrorTime(false);
        }}
        dataSource={kqycDictList}
      />

      <Select
        title="异常类型"
        open={openErrorType}
        value={formData.codeName}
        onClose={() => setOpenErrorType(false)}
        onConfirm={(val: any) => {
          form.setFieldValue('code', val.label);
          onChange({
            code: val.value,
            codeName: val.label,
          });

          setOpenErrorType(false);
        }}
        dataSource={getYcTypeList()}
      />
      {/* 选择异常人员 */}
      <SelectEmployee
        open={openEmployee}
        mode="single"
        onClose={() => setOpenEmployee(false)}
        onSubmit={(list) => {
          console.log('list', list);
          const res = list[0];
          form.setFieldValue('userName', list[0].realName);
          onChange({
            userId: res.userId,
            userName: res.username,
            userRealName: res.realName,
          });

          setOpenEmployee(false);
        }}
        dataSource={employeeList}
      />
    </Drawer>
  );
};

export default Component;
