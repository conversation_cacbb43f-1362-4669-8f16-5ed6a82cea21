import { queryGroupByPage } from '@/api/addressBook/index';
import { getWidth } from '@/utils/common';
import { AutoComplete, AutoCompleteProps, Button, Form, Table } from 'antd';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import styles from '../MailBook/MailBook.module.less';
import AddGroupModal from './AddGroupModal';
import { groupCol } from './columns';

export interface SharedGroupModel {
  id: string; // 序号
  groupName: string; // 邮箱名称
  member: string[]; // 成员
  memberList: PersonVO[];
}

interface PersonVO {
  userId: string;
  mobile: string;
  username: string;
  contactName: string;
}
//共享群组件-通讯类型
interface ModalProps {
  onClose: (value: boolean) => void; //关闭回调方法
  onAdd: (data: SharedGroupModel[]) => void; //数据回传方法
  getContainer?: HTMLElement | null; // 共享群挂载的dom节点
  hideFooter?: boolean; // 是否隐藏底部
  notAbsolute?: boolean; // 是否取消组件的绝对定位
  selectDataList?: SharedGroupModel[]; //已选数据回显
}

const Group = forwardRef(
  ({ onClose, onAdd, hideFooter, notAbsolute = false, selectDataList, getContainer }: any, ref) => {
    //共享群数据/查询后数据
    const [sharedGroupList, setGroupList] = useState<SharedGroupModel[]>([]);
    //已选择数据
    const [selectData, setSelectData] = useState<SharedGroupModel[]>(selectDataList);

    //加载loading及分页参数
    const [loading, setLoading] = useState<boolean>(false);
    const [total, setTotal] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState<any>({ current: 1 });
    const [hasMore, setHasMore] = useState<boolean>(true);
    const [openAdd, setOpenAdd] = useState(false); // 打开新建群

    const [form] = Form.useForm();
    useEffect(() => {
      if (selectDataList && selectDataList.length) {
        setSelectData(selectDataList || []);
      }
    }, [selectDataList]);

    const resetSelectData = () => {
      form.resetFields();
      setSelectData([]);
      setCurrentPage({ current: 1 });
    };

    useImperativeHandle(ref, () => {
      return {
        getSelectData: () => selectData, // 获取当前选择数据
        resetSelectData,
        setSelectData,
      };
    }, [selectData]);

    const fetchData = () => {
      setLoading(true);
      const args: any = {
        pageNo: currentPage.current,
        pageSize: 10,
        keyWordList:
          queryType.current == 'current' ? [serachValue, ...resultKeywords] : resultKeywords,
      };

      queryGroupByPage(args)
        .then((res: any) => {
          if (res.data.list) {
            setTotal(res.data.total);
            const list = res.data.list;
            list.forEach((item: any) => {
              item.member = item.memberList.map((item: any) => item.contactName);
            });
            if (currentPage.current == 1) {
              setGroupList(list);
            } else {
              setGroupList((prevDataList) => [...prevDataList, ...list]);
            }
          }
        })
        .finally(() => {
          setLoading(false);
        });
    };

    const [resultKeywords, setResultKeywords] = useState<string[]>([]); // 结果查询列表
    const [serachValue, setSerachValue] = useState<string>('');
    const [queryType, setQueryType] = useState<any>({ current: 'current' });
    const handleFilter = () => {
      return form.validateFields().then(({ keyword }) => {
        setSerachValue(keyword);
      });
    };
    /**
     * 全部查询
     */
    const searchAll = () => {
      form.resetFields();
      setSerachValue('');
      setQueryType({ current: 'all' });
    };

    /**
     * 结果查询
     */
    const searchResult = () => {
      handleFilter().then(() => {
        setQueryType({ current: 'results' });
        form.resetFields();
      });
    };
    useEffect(() => {
      switch (queryType.current) {
        case 'current':
          break;
        case 'all':
          setResultKeywords([]);
          break;
        case 'results':
          setResultKeywords([serachValue, ...resultKeywords]);
          break;
      }
    }, [queryType]);

    useEffect(() => {
      setCurrentPage({ current: 1 });
    }, [serachValue, resultKeywords]);

    useEffect(() => {
      fetchData();
    }, [currentPage]);

    const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, clientHeight, scrollHeight } = e.target as HTMLDivElement;
      if (scrollTop + clientHeight >= scrollHeight - 20 && hasMore && !loading) {
        setCurrentPage({ current: currentPage.current + 1 });
      }
    };

    useEffect(() => {
      if (sharedGroupList.length === total && sharedGroupList.length) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
    }, [total, sharedGroupList]);

    //数据传输给父组件
    const handleClick = () => {
      onAdd(selectData);
    };

    //上表的添加方法
    const handleAdd = (record: SharedGroupModel) => {
      setSelectData([...selectData, record]);
    };
    //下表的移除方法
    const handleDelete = (record: SharedGroupModel) => {
      setSelectData(selectData.filter((item) => item.id !== record.id));
    };
    //上表表结构
    const columns = [
      ...groupCol,
      {
        title: '操作',
        key: 'action',
        width: getWidth(70),
        render: (_: any, record: SharedGroupModel) => (
          <Button
            type="primary"
            disabled={!!selectData.find((item) => item.id === record.id)}
            onClick={() => handleAdd(record)}
            className={classNames(
              !selectData.find((item) => item.id === record.id) ? styles.active : styles.deactive,
            )}
          >
            {selectData.find((item) => item.id === record.id) ? '已选' : '多选'}
          </Button>
        ),
      },
    ];

    //下表表结构
    const selectColumns = [
      ...groupCol,
      {
        title: '操作',
        key: 'action',
        width: getWidth(70),
        render: (_: any, record: SharedGroupModel) => (
          <Button
            type="primary"
            size="small"
            className={styles.active}
            ghost
            onClick={() => handleDelete(record)}
          >
            取消
          </Button>
        ),
      },
    ];

    const handleAddClick = () => {
      setOpenAdd(true);
    };
    /**
     * 添加共享群成功
     */
    const onAddSuccess = () => {
      form.resetFields();
      setResultKeywords([]);
      setOpenAdd(false);
      if (currentPage.current == 1) {
        fetchData();
      } else {
        setCurrentPage({ current: 1 });
      }
    };

    /**
     * 设置行属性
     */
    const rowClassName = (record: any) => {
      return selectData.map((item: any) => item.id).includes(record.id) ? 'row-selected' : '';
    };
    const [options7, setOptions7] = useState<AutoCompleteProps['options']>([]);
    const onSelect7 = () => {
      handleFilter().then(() => {
        setQueryType({ current: 'current' });
      });
    };

    const onChange = (e: any) => {
      console.log(e);
      if (e?.trim()) {
        queryGroupByPage({ pageNo: 1, pageSize: 10, keyWordList: [e?.trim()] }).then((res: any) => {
          setOptions7(
            res.data.list.map((item: any) => {
              return { label: item.groupName, value: item.groupName };
            }),
          );
        });
      }
      querySelectFn7();
    };

    // 下拉搜索选择
    const querySelectFn7 = useCallback(
      debounce(() => {
        handleFilter().then(() => {
          setQueryType({ current: 'current' });
        });
      }, 500),
      [],
    );

    const handleKeyDown = (e: any) => {
      if (e.keyCode === 13 || e.code === 'Enter' || e.key === 'Enter') {
        handleFilter().then(() => {
          setQueryType({ current: 'current' });
        });
      }
    };

    return (
      <div className={`${styles.contentBox} ${notAbsolute ? styles.notAbsolute : ''}`}>
        <div className={styles.title}>
          <div className={styles.leftTitle}>
            <p>{getContainer ? '' : '选择'}共享群</p>
            <span>
              共计<a>{total}</a>个，已选择<a>{selectData?.length}</a>个
            </span>
          </div>
          <Button type="primary" onClick={() => onClose(false)}>
            关闭
          </Button>
        </div>
        <div className={styles.modalBody}>
          <div className={styles.serach}>
            <Form layout={'inline'} form={form}>
              <Form.Item label="" name="keyword">
                <AutoComplete
                  placeholder="请输入群名称"
                  popupMatchSelectWidth={false}
                  onSelect={onSelect7}
                  onSearch={onChange}
                  className={styles.searchInput}
                  onInputKeyDown={handleKeyDown}
                  style={{ width: `${getWidth(176)}px` }}
                  options={options7}
                ></AutoComplete>
              </Form.Item>
              <Form.Item label="">
                <Button type="primary" onClick={searchAll}>
                  全部查询
                </Button>
              </Form.Item>
              <Form.Item label="">
                <Button type="primary" onClick={searchResult}>
                  结果查询
                </Button>
              </Form.Item>
            </Form>
            <Button type="primary" onClick={handleAddClick}>
              新建
            </Button>
          </div>
          <div className={styles.table}>
            <Table
              dataSource={sharedGroupList}
              columns={columns}
              pagination={false}
              rowClassName={rowClassName}
              {...(sharedGroupList.length > 7 ? { scroll: { y: getWidth(280) } } : {})}
              rowKey={'id'}
              loading={loading}
              onScroll={handleScroll}
            />
          </div>
          <div className={styles.checkTable}>
            <div className={styles.checkTitle}>
              <span>已选共享群({selectData?.length})</span>
            </div>
            <Table
              dataSource={selectData}
              columns={selectColumns}
              pagination={false}
              className="select-table"
              {...(selectData?.length > 4 ? { scroll: { y: getWidth(160) } } : {})}
              rowKey={'id'}
            />
          </div>
          {!hideFooter && (
            <div className={styles.footer}>
              <Button type="primary" onClick={handleClick}>
                {getContainer ? '确认添加' : '确认选择'}
              </Button>
            </div>
          )}
        </div>
        {openAdd && (
          <AddGroupModal
            open={openAdd}
            onCancel={() => setOpenAdd(false)}
            success={onAddSuccess}
          ></AddGroupModal>
        )}
      </div>
    );
  },
);

interface GroupProps {
  getSelectData: () => any[];
  resetSelectData: () => void;
  setSelectData: (data: any[]) => void;
}
const SharedGroup = forwardRef(
  (
    {
      onClose,
      onAdd,
      getContainer = null,
      hideFooter,
      notAbsolute = false,
      selectDataList,
    }: ModalProps,
    ref,
  ) => {
    const groupRef = useRef<GroupProps>();

    useImperativeHandle(ref, () => {
      return {
        getSelectData: () => groupRef.current?.getSelectData(), // 获取当前被选择的数据
        resetSelectData: () => groupRef.current?.resetSelectData(),
        setSelectData: (data: any[]) => groupRef.current?.setSelectData(data),
      };
    });

    return getContainer ? (
      createPortal(
        <Group
          ref={groupRef}
          onClose={onClose}
          onAdd={onAdd}
          getContainer={getContainer}
          hideFooter={hideFooter}
          notAbsolute={notAbsolute}
          selectDataList={selectDataList}
        />,
        getContainer,
      )
    ) : (
      <Group
        ref={groupRef}
        onClose={onClose}
        onAdd={onAdd}
        hideFooter={hideFooter}
        getContainer={getContainer}
        notAbsolute={notAbsolute}
        selectDataList={selectDataList}
      />
    );
  },
);

export default SharedGroup;
