import { useContext } from 'react';
import { Space, Button } from 'antd';
import Context from '../Context';
import styles from './index.module.less';

interface Props {
  onClick?: (type: string) => void;
}

const Component = ({ onClick }: Props) => {
  const { config } = useContext(Context);
  return (
    <div className={styles.toolBar}>
      <Space>
        <Button type="primary" onClick={() => onClick!('selectFiles')}>
          {`${config?.buttonText}文件`}
        </Button>
      </Space>
    </div>
  );
};

export default Component;
