import { getArticleDetails } from '@/api/officialSite';
import { But<PERSON>, Drawer } from 'antd';
import { FC, useEffect, useState } from 'react';
import Styles from './index.module.less';
import { DrawerClassNames } from 'antd/es/drawer/DrawerPanel';

const ArticleDetails: FC<{
  articleId: string;
  pageType: string;
  changeActiveTab: (key: string, item?: any, articleId?: string, tabItem?: any) => void;
}> = ({ articleId, pageType, changeActiveTab }) => {
  const [htmlData, setHtmlData] = useState('<Empty />');
  const [title, setTitle] = useState('');

  const createMarkup = () => {
    return { __html: htmlData };
  };

  const _getArticleDetails = async (articleId: string) => {
    const { data } = (await getArticleDetails(articleId)) as { data: any }
    setTitle(data.title);
    setHtmlData(data.visitableContent);
  };

  const goBack = () => {
    changeActiveTab(pageType);
  };

  useEffect(() => {
    _getArticleDetails(articleId).then(() => {});
  }, []);

  const classNames: DrawerClassNames = {
    header: Styles.antDrawerHeader,
    footer: Styles.antDrawerFooter,
  };

  return (
    <>
      <Drawer
        title={
          <div className={Styles.headerContainer} style={{ paddingTop: 0 }}>
            <div className={Styles.headerBox}>
              <div className={Styles.title}>{title}</div>
              <Button color="primary" className={Styles.button} onClick={goBack}>
                返回
              </Button>
            </div>
          </div>
        }
        footer={
          <div className={Styles.headerContainer}>
            <div className={Styles.footerBox}>
              <div className={Styles.footerButtonBox}>上一篇</div>
              <div className={Styles.footerButtonBox}>下一篇</div>
            </div>
          </div>
        }
        classNames={classNames}
        placement="right"
        closable={false}
        open={true}
        getContainer={false}
        width="100%"
      >
        <div className={Styles.headerContainer}>
          <div className={Styles.content} dangerouslySetInnerHTML={createMarkup()} />
        </div>
      </Drawer>
    </>
  );
};

export default ArticleDetails;
