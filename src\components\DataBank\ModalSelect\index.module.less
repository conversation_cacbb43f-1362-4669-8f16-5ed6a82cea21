.modalWrap {
  :global {
    .ant-modal-content {
      padding: 0;
      height: 651px;
    }
    .ant-modal-body {
      width: 100%;
      height: 100%;
      display: flex;
    }
    .ant-divider {
      margin-top: 0;
      margin-bottom: 24px;
    }
    .ant-table-tbody-virtual-scrollbar-horizontal {
      visibility: hidden;
    }
  }
  .modalLeft {
    margin-right: 5px;
    border-radius: 8px;
    flex: 1;
    background: #ffffff;
    box-shadow: 0px 20px 40px 0px rgba(0, 0, 0, 0.1);
  }
  .modalRight {
    flex: 1;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 20px 40px 0px rgba(0, 0, 0, 0.1);
  }
  .headWrap {
    display: flex;
    justify-content: space-between;
    padding: 17px 16px;
    .leftHeader {
      display: flex;
      align-items: center;
      .title {
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        color: #4d70fe;
        margin-right: 12px;
      }
      .desc {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        .num {
          color: #e53935;
        }
      }
    }
  }
  .haaderSearch {
    display: flex;
    margin-left: 18px;
    margin-bottom: 10px;
    .searchInput {
      margin-right: 8px;
      > span {
        font-size: 14px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.65);
      }
      input {
        width: 156px;
        height: 32px;
        border-radius: 4px;
        background: #ffffff;
        box-sizing: border-box;
        /* 黑-Black/Black-15 */
        // border: 1px solid rgba(0, 0, 0, 0.15);
      }
    }
    .btns {
      display: flex;
    }
  }
  .customTable {
    height: 448px;
    :global {
      .ant-table {
        th {
          min-height: 45px;
          line-height: 45px;
          font-weight: normal !important;
          background: #e8eaf6 !important;
          &::before {
            height: 0 !important;
          }
        }
        tr {
          min-height: 45px;
          line-height: 45px;
          &:nth-child(even) {
            background: #f8fafd;
          }
        }
      }
      .ant-table-cell {
        min-height: 45px;
        line-height: 45px;
        padding: 0 !important;
      }
    }
  }
  .customTableRight {
    height: 448px;
    :global {
      .ant-table {
        th {
          background: #fff3e0 !important;
        }
      }
    }
  }
  .btnsWrap {
    display: flex;
    justify-content: center;
  }
  .btnsAll {
    display: flex;
    align-items: center;
    justify-content: center;
    > div {
      margin-right: 8px;
    }
    .review {
      color: #3d5afe;
      cursor: pointer;
    }
  }
  .btnsParmary {
    border-radius: 4px;
    /* 自动布局 */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 16px;
    background: #3d5afe;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    margin-right: 19px;
    color: #fff;
    margin-right: 8px;
    cursor: pointer;
  }
  .close {
    border-radius: 4px;
    /* 自动布局 */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    background: #fff;
    color: #3d5afe;
    border: 1px solid #3d5afe;
    cursor: pointer;
  }
  .closeAdjust {
    box-sizing: border-box;
    width: 84px;
    padding: 2px 8px;
    text-align: left;
  }
  .closeCancel {
    padding: 2px 8px;
    height: 28px;
    line-height: 28px;
  }
  .closeSelected {
    background: #7986cb;
    color: #fff;
    border: 1px solid transparent;
    padding: 2px 8px;
    height: 28px;
    line-height: 28px;
  }
  .rowSelected {
    background: #fff;
    color: rgba(0, 0, 0, 0.25);
  }
}
.customTable {
}
