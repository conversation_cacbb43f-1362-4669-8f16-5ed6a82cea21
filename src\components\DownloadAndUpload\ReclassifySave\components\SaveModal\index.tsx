//分享文件
import { Button, Input, Modal, Select } from 'antd';
import { FC, useState } from 'react';
import styles from './index.module.less';
interface downloadSoftWareProps {
  openModal: boolean;
  onClose: () => void;
}
const File: FC<downloadSoftWareProps> = ({ openModal, onClose }) => {
  const [isDisplay, setIsDisplay] = useState<boolean>(false);
  const insertFile = () => {
    setIsDisplay(true);
  };
  const cancelFile = () => {
    setIsDisplay(false);
  };
  return (
    <div>
      <Modal
        width={600}
        open={openModal}
        wrapClassName={styles.wrapContent}
        title={
          <>
            <div className={styles.ModalTitle}>
              <div className={styles.header}>
                <div className={styles.headerTitle}>重新分类保存(裕邦操作系统版)</div>
                <div>
                  {/* <span onClick={onClose}>关闭</span> */}
                  <Button onClick={onClose} className={styles.menubtn}>
                    关闭
                  </Button>
                </div>
              </div>
            </div>
          </>
        }
        closable={false}
        onOk={() => {}}
        onCancel={onClose}
        footer={[
          <div className={styles.downloadBtn}>
            {isDisplay && (
              <Button className={styles.cancelBtn} onClick={cancelFile}>
                取消新建
              </Button>
            )}
            {!isDisplay && (
              <Button className={styles.confirmBtn} onClick={insertFile}>
                新建文件夹
              </Button>
            )}
            <Button className={styles.confirmBtn}>确认</Button>
            <Button className={styles.cancelBtn} onClick={onClose}>
              取消
            </Button>
          </div>,
        ]}
      >
        <div className={styles.file}>
          <div className={styles.fileSelect}>
            <div className={styles.filePosition}>分类保存文件夹位置：</div>
            <div>
              <Select
                style={{ width: 300 }}
                variant="filled"
                placeholder={'请选择保存位置'}
                options={[
                  { value: '1', label: 'C盘' },
                  { value: '2', label: 'D盘' },
                  { value: '3', label: 'E盘' },
                  { value: '4', label: 'F盘' },
                  { value: '5', label: 'U盘' },
                  { value: '6', label: '移动硬盘' },
                ]}
              />
            </div>
          </div>
          {isDisplay && (
            <div className={styles.fileSelect}>
              <div className={styles.filePosition}>分类保存文件夹名称：</div>
              <div>
                <Input
                  style={{ width: 300 }}
                  placeholder={'请输入文件夹名称'}
                  variant="filled"
                ></Input>
              </div>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};
export default File;
