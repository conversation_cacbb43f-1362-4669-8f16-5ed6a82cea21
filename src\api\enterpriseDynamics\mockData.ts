export const fillList = new Array(30).fill(0).map((v, i) => {
  return {
    id: i + 1 + 'a',
    questionType: 1,
    questionContent: '长沙裕邦软件开发有限公司的法人是____，他是湖南新化人，中共党员法学博士。',
    answer: '谭曼',
    questionScore: 1,
    answerContent: '谭',
    score: 1,
  };
});

export const singleList = new Array(10).fill(0).map((v, i) => {
  return {
    id: i + 1 + 'b',
    questionType: 2,
    questionContent: '长沙裕邦科技有限公司是哪个公司为彻底转型而收购的全资子公司？',
    answer: '1',
    questionScore: 1,
    answerContent: '2',
    score: 1,
    questionOptions: [
      { id: '1', optionContent: '长沙裕邦科技有限公司' },
      { id: '2', optionContent: '湖南裕邦科技有限公司' },
      { id: '3', optionContent: '湖南裕邦智能科技有限公司' },
      { id: '4', optionContent: '湖南永雄裕邦智能科技有限公司' },
    ],
  };
});

export const multipleList = new Array(10).fill(0).map((v, i) => {
  return {
    id: i + 1 + 'c',
    questionType: 3,
    questionContent: '长沙裕邦科技有限公司是哪个公司为彻底转型而收购的全资子公司？',
    answer: '1,2,4',
    questionScore: 1,
    answerContent: '2,4',
    score: 1,
    questionOptions: [
      { id: '1', optionContent: '长沙裕邦科技有限公司' },
      { id: '2', optionContent: '湖南裕邦科技有限公司' },
      { id: '3', optionContent: '湖南裕邦智能科技有限公司' },
      { id: '4', optionContent: '湖南永雄裕邦智能科技有限公司' },
    ],
  };
});

export const shortList = new Array(5).fill(0).map((v, i) => {
  return {
    id: i + 1 + 'd',
    questionType: 4,
    questionContent: '请简述“裕邦智能办公平台1.0版”将要开发的主要功能？',
    answer: '裕邦智能办公平台1.0版”将要开发的主要功能',
    questionScore: 6,
    answerContent: '裕邦智能办公平台1.0版”将要开发的主要功能',
    score: 5,
  };
});

export const essayList = new Array(2).fill(0).map((v, i) => {
  return {
    id: i + 1 + 'e',
    questionType: 5,
    questionContent: '结合本人在裕邦的职业规划谈“裕邦印象”，字数不低于300字。',
    answer: '本人在裕邦的职业规划谈“裕邦印象”',
    questionScore: 10,
    answerContent: '本人在裕邦的职业规划谈“裕邦印象”',
    score: 8,
  };
});

export const mockTestList = new Array(100).fill(0).map((v, i) => {
  return {
    id: i + 1 + 'f',
    paperName: '长沙裕邦企业文化模拟试卷',
  };
});

export const formalTestList = new Array(100).fill(0).map((v, i) => {
  return {
    id: i + 1 + 'g',
    paperName: '长沙裕邦企业文化正式试卷',
  };
});

export const formalPagerList = new Array(100).fill(0).map((v, i) => {
  return {
    id: i + 1 + 'h',
    testId: i + 1,
    userId: i + 1,
    hrId: i + 1,
    paperName: '正式考试',
    totalScore: 100,
    score: 90,
    realName: '鲁蛋',
    hrNumber: '001',
    deptName: '设计部',
    status: 2,
  };
});
