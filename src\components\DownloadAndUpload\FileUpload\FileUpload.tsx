import { FC, useState, useEffect } from 'react';
import { Form, Select, Popover, message, Table, Modal } from 'antd';
import type { TableProps } from 'antd';
import styles from './FileUpload.module.less';
import CustomTable from '../CustomTable';
interface DataType {
  key?: string;
  name?: string;
  title?: string;
  acount?: string;
  flieFormat?: string;
  fileSource?: string;
  level?: string;
  fileSize?: string;
  time?: string;
}
interface ModalProps {
  onCloseModal?: () => void; // 关闭弹框回调方法
  showFileChoose?: () => void; // 显示选择文件
  data?: DataType[];
}
const FileUpload = (ModalProps: ModalProps) => {
  const [list, setList] = useState<DataType[]>([]);
  const [value, setValue] = useState('');
  // 文库大全文件上传列表表头
  const FileChooseColumns: TableProps<DataType>['columns'] = [
    {
      title: '序号',
      dataIndex: 'key',
      key: 'key',
      width: '46px',
      align: 'center',
      render: (_: any, record: DataType) => <span>{record.key}</span>,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: '48px',
      align: 'center',
      render: (_: any, record: DataType) => <span>{record.name}</span>,
    },

    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: '267px',
      align: 'left',
      ellipsis: true,
      render: (_: any, record: DataType) => <span>{record.title}</span>,
    },
    {
      title: '文件格式',
      dataIndex: 'flieFormat',
      key: 'flieFormat',
      width: '93px',
      align: 'left',
      render: (_: any, record: DataType) => <span>{record.flieFormat}</span>,
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: '93px',
      align: 'left',
      render: (_: any, record: DataType) => <span>{record.fileSource}</span>,
    },

    {
      title: (
        <div
          className={styles.tableHeaderBut}
          onClick={() => {
            setList([]);
          }}
        >
          全部取消
        </div>
      ),
      key: 'action',
      width: '134px',
      align: 'left',
      render: (_: any, record: DataType, index: number) => (
        <div className={styles.btnsAll}>
          <span>浏览</span>
          <span
            className={styles.nomalBut}
            onClick={() => {
              list.splice(index, 1);
              setList([...list]);
            }}
          >
            取消
          </span>
        </div>
      ),
    },
  ];
  useEffect(() => {
    console.log(ModalProps.data);
    setList(list.concat(ModalProps.data || []));
  }, [ModalProps.data]);
  return (
    <>
      <div className={styles.FlieDownload + ' ' + styles.FileUpload}>
        <div className={styles.header}>
          <div>
            <span>一键上传（裕邦操作系统版）</span>
            共计<span>{list.length}</span>个，已上传<span>2</span>个，剩余<span>8</span>个
          </div>
          <div
            className={styles.closeBut}
            onClick={() => {
              if (ModalProps && ModalProps.onCloseModal) {
                ModalProps.onCloseModal();
              }
            }}
          >
            关闭
          </div>
        </div>
        <div className={styles.chooesFileBox}>
          <div
            onClick={() => {
              if (ModalProps && ModalProps.showFileChoose) {
                ModalProps.showFileChoose();
              }
            }}
          >
            选择要上传的文件
          </div>
          <div>选择文件上传至文库大全</div>
        </div>
        <div className={styles.TableBox}>
          <CustomTable
            columns={FileChooseColumns}
            scroll={{ y: 420 }}
            pagination={false}
            dataSource={list}
            virtual
          />
        </div>
        <div className={styles.OperateButList}>
          <div className={styles.ConfirmBut}>确认上传</div>
        </div>
      </div>
    </>
  );
};

export default FileUpload;
