import { simpleProps } from '@/pages/AudioPlay/CustomPlayer/indetface';
import CustomPlayer from '@/pages/AudioPlay/CustomPlayer/simplePlayer';
import usePlayer from '@/pages/AudioPlay/hooks/usePlayer/usePlayer';
import SonicDiagram from '@/pages/AudioPlay/SonicDiagram';
import CanvasFilter from '@/pages/VideoPlay/CanvasFilter';
import getMediaUrl from '@/utils/getMediaUrl';
import { useEffect, useRef, useState } from 'react';
import Player, { Events } from 'xgplayer';
import 'xgplayer/dist/index.min.css';
import type { File } from '../interface';
import styles from './AudioVideoPreview.module.less';
// 播放器实例
let player: Player;
interface Props {
  open: boolean;
  MulitiModalClassName: string;
  file?: File;
}
const AudioVideoPreview = (props: Props) => {
  const [isFull, setIsFull] = useState<boolean>(false);
  const [isMove, setIsMove] = useState<boolean>(true);
  const videoRef = useRef(null);
  const [dragBoxTranslate, setdragBoxTranslate] = useState<string>('');
  const { init } = usePlayer();
  // 播放器变量相关参数
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [volume, setVolume] = useState<number>(0);
  const [playbackRate, setPlaybackRate] = useState<number>(0);
  const [isPlay, setIsPlay] = useState<boolean>(false);
  const [isPause, setIsPause] = useState<boolean>(false);
  const [VideoElement, setVideoElement] = useState<HTMLVideoElement | null>(null);
  const CustomPlayerProps: simpleProps = {
    setFull: (value: boolean) => {
      const $dragBox = document
        .querySelector(`.${props.MulitiModalClassName}`)
        ?.querySelector('.react-draggable') as HTMLDivElement;
      if (value && $dragBox) {
        const style = window.getComputedStyle($dragBox);
        setdragBoxTranslate(style.transform);
        $dragBox.style.transform = 'none';
      } else {
        $dragBox.style.transform = dragBoxTranslate;
      }
      setIsFull(value);
    },
    isAudio: false,
    player: {
      isPlay,
      play: () => {
        player?.play();
      }, // 播放
      isPause,
      pause: () => {
        player?.pause();
      }, // 暂停
      destory: () => {}, // 播放器销毁
      reload: () => {
        player?.reload();
      }, // 重新播放
      seek: (value: number) => {
        player?.seek(value);
      }, // 切换时间播放
      playbackRate, // 播放速度
      setPlaybackRate: (value: number) => {
        const media = player.media as HTMLVideoElement;
        media.playbackRate = value;
      }, // 设置播放速度
      volume, // 读取播放声音
      setVolume: (value: number) => {
        const media = player.media as HTMLVideoElement;
        media.volume = value;
      }, // 读取播放声音
      duration, // 读取音视频总长度
      currentTime, // 读取播放声音
    },
  };
  // 播放初始化
  const playerInit = () => {
    const $div: any = videoRef.current;
    player = init(
      'normalPlayer',
      getMediaUrl(props.file?.visitPath || '', props.file?.secretKey),
      $div,
    ) as Player;
    // 渲染 音波图
    const media = player.media as HTMLVideoElement;
    media.crossOrigin = 'anonymous';
    setVideoElement(media);
    setCurrentTime(player.currentTime || 0);
    setDuration(player.duration);
    setVolume(player.volume);
    // 播放失败前
    player.usePluginHooks('error', 'showError', (_plugin, ..._args) => {
      return true;
    });
    //监听播放
    player.on(Events.PLAY, () => {
      setIsPlay(true);
      setIsPause(false);
    });
    //监听暂停
    player.on(Events.PAUSE, () => {
      setIsPlay(false);
      setIsPause(true);
    });
    // 播放完成
    player.on(Events.ENDED, () => {
      player.reload();
    });
    //监听报错
    player.on(Events.ERROR, () => {});
    //播放时间改变
    player.on(Events.TIME_UPDATE, () => {
      setCurrentTime(player.currentTime || 0);
      setDuration(player.duration);
    });
    //播放声音改变
    player.on(Events.VOLUME_CHANGE, () => {
      setVolume(player.volume);
    });
    //播放路径改变
    player.on(Events.URL_CHANGE, () => {
      setDuration(player.duration);
    });
    //seeking播放
    player.on(Events.SEEKING, () => {});
    //seek播放完成
    player.on(Events.SEEKED, () => {});
    //视频时长发生变化
    player.on(Events.DEFINITION_CHANGE, () => {
      setDuration(player.duration);
    });
    //等待加载数据中
    player.on(Events.WAITING, () => {});
    //视频缓冲足够可播放
    player.on(Events.CANPLAY, () => {});
  };
  useEffect(() => {
    if (props.open) {
      playerInit();
    } else if (player) {
      player.destroy();
      setVideoElement(null);
    }
    console.log(props.file);
    return () => {
      if (player) {
        player.destroy();
      }
    };
  }, [props.open]);
  return (
    <div
      className={`${styles.AudioVideoPreviewContent} ${isFull ? styles.AudioVideoPreviewContentFull : ''}`}
    >
      <div className={styles.AudioVideoBox}>
        <div style={{ position: 'absolute', width: '100%', height: '100%' }}>
          {props.file?.fileFormatType === 'library_file_type_video' && VideoElement ? (
            <CanvasFilter video={VideoElement} />
          ) : null}
        </div>
        {props.file?.fileFormatType === 'library_file_type_audio' && VideoElement ? (
          <div className={styles.SonicDiagramBox}>
            <SonicDiagram video={VideoElement} />
          </div>
        ) : null}
        <div
          className={styles.videoBoxRander}
          hidden={props.file?.fileFormatType === 'library_file_type_audio'}
          ref={videoRef}
        />
      </div>
      <div className={styles.playerBox}>
        <CustomPlayer {...CustomPlayerProps} />
      </div>
    </div>
  );
};

export default AudioVideoPreview;
