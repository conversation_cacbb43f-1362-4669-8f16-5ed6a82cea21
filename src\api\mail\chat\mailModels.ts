type option = {
  value: string;
  label: string;
  styles: string;
};
export interface deepType {
  index: number;
}
export const options: option[] = [
  { value: '0', label: '普通邮件', styles: 'item0' },
  { value: '1', label: '普通转发', styles: 'item1' },
  { value: '2', label: '普通分享', styles: 'item2' },
  { value: '3', label: '只读分享', styles: 'item3' },
  { value: '4', label: '定时分享', styles: 'item4' },
  { value: '5', label: '定时只读分享', styles: 'item5' },
  { value: '6', label: '阅后只读即撤', styles: 'item6' },
  { value: '7', label: '定时阅后只读即撤', styles: 'item7' },
];

export interface personListReq {
  mailType: string;
  mailAddress?: string;
}

export interface mailDataReq {
  senderAddress?: string;
  addressee?: string;
  groupId?: string;
  pageNo: number;
  pageSize: number;
  addresseeStatus?: number;
  mailType?: string;
}
export interface MailBookReq {
  id: string;
  mailTitle: string;
  mailContent: string;
  confidentialityStatus: number; //保密等级
  referenceMailId?: string; //引用邮件id
  senderId: string;
  shareFlag: number; //是否是分享邮件0否 1是
  senderAddress: string;
  senderName: string;
  timedTime?: string; //定时撤回时间
  addressList: AddressList[];
  addressGroupList: AddressGroupList[];
  copyAddressList: AddressList[];
  copyAddressGroupList: AddressGroupList[];
  secretAddressList: AddressList[];
  mailChatAttachVOList: MailChatAttachVOList[];
  jsonContent?: string;
}
export interface MailBookDataType extends MailBookReq {
  mailId?: string;
  sendType?: string;
  sendTime: string;
  readFlag: number; //0未读
  revokeFlag: number; //0未撤回
  contractPersonId: string;
  mobile: string;
  username: string;
  isSystemSend: boolean;
  id: string;
  serverCurrentTime?: number;
  mailStatus?: number;
}

export interface AddressList {
  id?: string;
  mailId?: string;
  readFlag?: number;
  addresseeStatus: number;
  revokeFlag?: number;
  blackFlag?: number;
  addressStatus: number;
  addressee: string;
  shareFlag?: number;
  addresseeName?: string;
  groupId?: string;
  groupFlag?: number;
  contactPersonId?: string;
  userId: string;
  mobile: string;
  realName: string;
  username: string;
  contactType?: number;
  avatar?: string;
}

export interface MailChatAttachVOList {
  id?: string;
  mailId?: string;
  sorting?: string;
  attachmentId: string;
  attachmentUrl: string;
  attachmentName: string;
  attachmentSize?: number;
  createTime?: string;
  readFlag?: number;
  addressee?: string;
  addresseeName?: string;
  fileType: string;
  fileFormatType: string;
  mediaType: number;
  mediaDuration?: number;
  visitPath?: string;
  secretKey?: string; //	加密串（公钥加密后的秘钥）
  decodeKey?: string; //  解密串（私钥解密后的明文密码）
  filePath?: string;
}
export interface AddressGroupList {
  groupName: string;
  groupId: string;
  mailId?: string;
  contactPersonVOS: ContactPersonVO[];
  mailAddress?: string[];
  avatar?: string;
  groupCount: string;
}

export interface ContactPersonVO {
  mobile: string;
  id: string;
  realName: string;
  username: string;
  groupId: string;
}
