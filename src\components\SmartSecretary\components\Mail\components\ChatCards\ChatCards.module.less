.card {
  margin-bottom: 10px;
  font-size: 14px;

  &.send {
    .card-header {
      .itemPar {
        .titleSpan {
          color: #3d5afe;
        }
      }
    }
  }
  .card-header {
    border: 0.65px solid #8c9eff;
    border-bottom: none;
    border-radius: 8px 8px 0px 0px;
    padding: 12px 10px;
    background: #f7f7f7;
    span {
      display: inline-block;
      line-height: 24px;
      height: 24px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.6);
    }

    .itemPar {
      padding-bottom: 10px;
      .coll {
        max-width: 220px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }
      .edit-span {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        position: relative;
        min-width: 56px;
        .text-span {
          text-align-last: justify;
          min-width: 50px;
        }
      }
      .titleSpan {
        font-size: 18px;
        font-weight: 600;
        text-align: right;
        color: #ff7043;
        min-width: 100px;
      }
    }

    .headerContent {
      color: rgba(0, 0, 0, 0.6);
    }

    .detailSpan {
      min-width: 40px;
      border-radius: 4px;
      font-size: 12px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      color: #ffffff;
      background: #ff8a65;
      cursor: pointer;
      margin-right: 3px;
    }
    .detailSpanBtn {
      background: #ccc !important;
    }
  }

  .card-content {
    border: 0.65px solid #8c9eff;
    border-top: none;
    border-radius: 0px 0px 8px 8px;
    padding: 16px 10px;
    background: #ffffff;
    span {
      cursor: pointer;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    .imgStyles {
      width: 40px;
      height: 22px;
      margin-right: 16px;
    }
    .itemPar {
      padding-bottom: 10px;
      .redeSpan {
        height: 26px;
        border-radius: 4px;
        padding: 2px 8px;
        border: 0.5px solid rgba(0, 0, 0, 0.15);
        font-size: 16px;
        text-align: center;
        letter-spacing: 1px;
        color: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
      }
    }

    .item-parc {
      height: 40px;
      border-radius: 4px;
      padding: 6px 8px;
      margin: 0px 5px 10px 55px;
      background: #8092ff;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .icon {
        // width: 16px;
        height: 16px;
      }
      .title {
        height: 20px;
        opacity: 1;
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        z-index: 1;
      }
      .duration {
        height: 17px;
        font-size: 12px;
        font-weight: 500;
        color: #ffffff;
      }
      button {
        width: 60px;
        height: 28px;
        border-radius: 4px;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 4px 16px;
        gap: 8px;
        background: #ffffff;
        line-height: 20px;
        font-size: 14px;
      }
      .okButton {
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .titleBox {
      width: 100%;
      text-align: center;
      font-size: 18px;
      font-weight: 600;
      color: #252525;
    }
    .contentBox {
      width: 100%;
      word-break: break-word;
      .symbolSpan {
        display: inline-block;
        width: 40px;
        height: 18px;
        border-radius: 4px;
        background: #d3d3d3;
        cursor: pointer;
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        color: #4d4d4d;
        margin-left: 3px;
      }
      .paragraph {
        font-size: 14px !important;
        word-break: break-word;
        padding-right: 5px;
        overflow: hidden; //超出隐藏
        text-overflow: ellipsis; //超出文本设置为...
        display: -webkit-box; //将div1转换为盒子模型
        -webkit-line-clamp: 2; //设置div1的文本为2行
        -webkit-box-orient: vertical; //从顶部向底部垂直布置子元素
      }
      .videoSpan {
        display: inline-block;
        width: 100%;
        margin: 3px;
        cursor: pointer;
      }
      .videoSpan:hover {
        color: rgb(238, 73, 73);
      }
    }
    .annexBox {
      width: 100%;
    }

    .annexSpan {
      span {
        height: 26px;
        border-radius: 4px;
        opacity: 1;
        padding: 2px 8px;
        background: #3d5afe;
        /* 自动布局子元素 */
        font-size: 16px;
        font-weight: 500;
        text-align: center;
        color: #ffffff;
      }
    }
  }
}
