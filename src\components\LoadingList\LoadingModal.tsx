import DesktopFileUploadShare from '@/components/DesktopFileUploadShare';
import { Download, DownloadAll } from '@/components/Download';
import  DesktopFileReclassification from '@/components/reclassificationSave'
import Upload from '@/components/Upload';
import useCtxStore from './useCtxStore';
const Component = () => {
  const [loadingList] = useCtxStore((state) => [state.loadingList]);

  return (
    <>
      {loadingList.map((item: any) => {
        switch (item.type) {
          case 'upload':
            return <Upload key={`${item.module}_${item.type}`} config={item} />;
          case 'shareUpload':
            return <Upload key={`${item.module}_${item.type}`} config={item} />;
          case 'download':
            return <Download key={`${item.module}_${item.type}`} config={item} />;
          case 'shareDownload':
            return <Download key={`${item.module}_${item.type}`} config={item} />;
          case 'downloadAll':
            return <DownloadAll key={`${item.module}_${item.type}`} config={item} />;
          case 'DesktopFileUploadShare':
            return <DesktopFileUploadShare key={`${item.module}_${item.type}`} config={item} />;
          case 'DesktopFileReclassification' : 
          return <DesktopFileReclassification key={`${item.module}_${item.type}`} config={item} />;
          case 'desktopSoftwareDownloadInstall' : 
          return  <DesktopFileReclassification key={`${item.module}_${item.type}`} config={item} />;
        }
      })}
    </>
  );
};

export default Component;
