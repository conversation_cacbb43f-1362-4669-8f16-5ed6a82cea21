/**
 * 选择文件组件
 */

import { searchConditionAll } from '@/api/library';
import AudioType from '@/assets/images/todolist/audioType.png';
import OtherType from '@/assets/images/todolist/otherType.png';
import PicType from '@/assets/images/todolist/picType.png';
import VideoType from '@/assets/images/todolist/videoType.png';
import YbWordType from '@/assets/images/todolist/ybwordType.png';
import Drawer from '@/components/Drawer';
import FilePreview, { FilePreviewAPI } from '@/components/FliePreview';
import useAppStore from '@/store/useAppStore';
import { formatFileSize, getWidth } from '@/utils/common';
import { Avatar, Button, Flex, Input, List, Space, Tag } from 'antd';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { FC, useCallback, useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import Header from '../../publicComponents/Header';
import styles from './index.module.less';
interface Props {
  title?: string; // 头部标题
  open: boolean; // 开关标识
  onClose: () => void; // 关闭窗口
  onSubmit: (list: any[]) => void; // 提交表单
  mode: 'single' | 'multiple'; // 单选/多选
  type: 1 | 2 | null;
  defaultValue?: string | string[] | any[]; // 默认选中的 传id。格式如： '5802666698' | ['5802666698', '5802666698'] | [{ id: '5802666698' }]
  dataSource?: any[]; // 数据源
  disabledList?: any[]; // 禁用列表[{ id: '5802666698',... }]
}
const SelectFile: FC<Props> = ({
  title = '添加附件',
  open,
  onClose,
  onSubmit,
  mode,
  type,
  defaultValue,
  dataSource,
  disabledList,
}) => {
  const [selectListIds, setSelectListIds] = useState<any[]>([]);
  const [renderList, setRenderList] = useState<any[]>([]); // 用于控制渲染的列表
  const [libraryImgPageNo, setLibraryImgPageNo] = useState({ current: 1 });
  const [hasMore, setHasMore] = useState(true);
  const { channel } = useAppStore((state: any) => state);
  const getLibraryDictData = (title?: string) => {
    const params = {
      fileFormatType: [],
      pageNo: libraryImgPageNo.current,
      pageSize: 50,
      title: searchTitleList.current ? [...searchTitleList.current] : [],
    };

    title && params.title.push(title);
    searchConditionAll(params).then(({ data }: any) => {
      libraryImgPageNo.current === 1
        ? setRenderList(data.list)
        : setRenderList([...renderList, ...data.list]);
      console.log(renderList.length + data.list.length >= data.total);
      if (renderList.length + data.list.length >= data.total) {
        setHasMore(false);
      }
    });
  };
  const fetchListNext = () => {
    console.log('下拉加载了');
    setLibraryImgPageNo({ current: libraryImgPageNo.current + 1 });
  };
  useEffect(() => {
    getLibraryDictData();
  }, [libraryImgPageNo]);

  useEffect(() => {
    setSelectListIds([]);
    let list: any[] = [];
    if (!defaultValue) return;
    if (typeof defaultValue === 'string') {
      list = [defaultValue];
    }
    if (typeof defaultValue === 'object') {
      if (typeof defaultValue[0] === 'string') {
        list = [...defaultValue];
      } else {
        list = defaultValue.map((item) => item.id);
      }
    }
    setSelectListIds(list);
  }, [defaultValue]);

  // 提交选中
  const handlderSubmit = () => {
    onSubmit(renderList.filter((item: any) => selectListIds.includes(item.id)));
  };

  // 渲染头部功能按钮
  const renderActionList = () => {
    const btnList = [
      <Button key="goBack" onClick={onClose}>
        返回
      </Button>,
    ];

    return btnList;
  };
  const SecretaryLibraryListItem: FC<{
    data: any;
    index: number;
    filePreview: (item: any) => void;
  }> = ({ data, index, filePreview }) => {
    const getAvatarAndClass: any = (fileFormatType: string) => {
      switch (fileFormatType) {
        case 'library_file_type_pic':
          return {
            avatar: PicType,
            titleClass: styles.tag_green,
          };
        case 'library_file_type_audio':
          return {
            avatar: AudioType,
            titleClass: styles.tag_purple1,
          };
        case 'library_file_type_video':
          return {
            avatar: VideoType,
            titleClass: styles.tag_purple2,
          };
        case 'library_file_type_doc':
        case 'library_file_type_yb':
          return {
            avatar: YbWordType,
            titleClass: styles.tag_blue,
          };
        default:
          return {
            avatar: OtherType,
            titleClass: styles.tag_yellow,
          };
      }
    };

    return (
      <List.Item key={data.id}>
        <List.Item.Meta
          avatar={
            <div className={styles.leftContent}>
              <Avatar
                shape="square"
                size={getWidth(48)}
                src={getAvatarAndClass(data.fileFormatType).avatar}
              />
              <div
                key={`img-${data.id}`}
                className={`${styles.typeTitle} ${getAvatarAndClass(data.fileFormatType).titleClass}`}
              >
                {data.fileFormatTypeName}
              </div>
            </div>
          }
          title={
            <div
              className={styles.titleLine}
              onClick={() => {
                filePreview(data);
              }}
            >
              <div className={styles.title}>{data.title}</div>
              <div className={styles.rightActions}>
                <Button
                  className={classNames(
                    styles.btnDefault,
                    selectListIds.includes(data.id) ? styles.fileSelected : undefined,
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (selectListIds.includes(data.id)) {
                      setSelectListIds(selectListIds.filter((id: any) => data.id !== id));
                    } else {
                      setSelectListIds([...selectListIds, data.id]);
                    }
                  }}
                  type="primary"
                  size="small"
                >
                  选择
                </Button>
              </div>
            </div>
          }
          description={
            <div
              className={styles.contentLine}
              onClick={() => {
                filePreview(data);
              }}
            >
              <div className={styles.content}>
                <div>
                  {data.shareRealName
                    ? `${data.shareRealName} (${data.shareUserName})`
                    : `${data.realName} (${data.userName})`}
                </div>
                <div>
                  <Tag color="geekblue" className={styles.tag}>
                    {formatFileSize(data.fileSize)}
                  </Tag>
                </div>
              </div>
              <div className={styles.createTime}> {data.createTime || '-'}</div>
            </div>
          }
        />
      </List.Item>
    );
  };
  const filePreviewRef = useRef<FilePreviewAPI>();
  const preview = (row: any) => {
    filePreviewRef.current?.open({ ...row, fileSource: 13 });
  };
  // 输入框查询
  const [searchTitle, setSearchTitle] = useState('');
  const searchTitleList: any = useRef(null);
  const searchAll = () => {
    searchTitleList.current = [];
    if (searchTitle) {
      setSearchTitle('');
    }
    getLibraryDictData();
  };
  const searchResult = () => {
    if (searchTitleList.current) {
      searchTitleList.current.push(searchTitle);
    } else {
      searchTitleList.current = [searchTitle];
    }
    setSearchTitle('');
  };
  const debounceQueryList = useCallback(
    debounce((str) => {
      getLibraryDictData(str);
    }, 300),
    [],
  );

  useEffect(() => {
    return () => {
      setRenderList([]);
    };
  }, []);
  return (
    <Drawer
      className={styles.selectFileDrawer}
      title={<Header title={title} onCancel={onClose} actionList={renderActionList()} />}
      onClose={onClose}
      open={open}
      footer={
        <div className={styles.footer}>
          <div>已选{selectListIds.length}个文件</div>
          <div>
            <Button
              size="large"
              type="primary"
              disabled={selectListIds.length === 0}
              onClick={handlderSubmit}
            >
              确定选择
            </Button>
          </div>
        </div>
      }
    >
      <div className={styles.fileListContainer}>
        <Flex className="mb-2 mt-2">
          <Input
            placeholder="请输入"
            value={searchTitle}
            className={styles.fileSearch}
            onChange={(e) => {
              setSearchTitle(e.target.value);
              debounceQueryList(e.target.value);
            }}
          ></Input>
          <Space>
            <Button
              type="primary"
              onClick={() => {
                searchAll();
              }}
            >
              全部查询
            </Button>
            <Button
              type="primary"
              onClick={() => {
                searchResult();
              }}
            >
              结果查询
            </Button>
          </Space>
        </Flex>
        <div className={styles.fileListContainerInner}>
          <div
            id="SmartSecretarySelectFileListId"
            className={styles.scrollableDiv}
            style={{
              height: window.innerHeight - getWidth(channel === 'web' ? 370 : 310),
            }}
          >
            <InfiniteScroll
              dataLength={renderList.length} // 已加载的数据长度
              next={fetchListNext} // 加载更多数据的函数
              hasMore={hasMore} // 是否还有更多数据
              loader={false}
              scrollableTarget="SmartSecretarySelectFileListId"
              scrollThreshold="200px"
            >
              <List
                className={styles.libraryList}
                itemLayout="horizontal"
                dataSource={renderList}
                renderItem={(item, index) => (
                  <SecretaryLibraryListItem data={item} index={index} filePreview={preview} />
                )}
              ></List>
            </InfiniteScroll>
          </div>
        </div>
        <FilePreview ref={filePreviewRef} />
      </div>
    </Drawer>
  );
};

export default SelectFile;
