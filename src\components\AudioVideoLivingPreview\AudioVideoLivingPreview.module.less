.AudioVideoLivingPreview {
  width: 100%;
  height: 100%;
  position: relative;
  :global {
    .xgplayer-controls,
    .xgplayer-error {
      display: none !important;
    }
    .xgplayer.xgplayer-inactive {
      cursor: pointer;
    }
  }
  .mainBox {
    width: 100%;
    height: 100%;
    justify-content: center;
    position: relative;
    .filterBg {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-size: cover;
      pointer-events: none;
      z-index: 0;
      .canvasFilter {
        width: 100%;
        height: 100%;
      }
    }
  }
  .videoBox {
    flex: none;
    width: 100%;
    height: 100%;
    background-size: cover;
    cursor: pointer;
    position: relative;
    .videoBoxRander {
      width: 100%;
      height: 100%;
      margin: 0 auto;
    }
    &:hover {
      div.player_tb {
        display: block;
      }
    }
  }
}
.spinning {
  text-align: center;
  color: #fff;
  font-size: 14px;
  img {
    width: 40px;
    height: 40px;
    animation: 0ms;
    margin: 10px auto;
    -webkit-animation: rotateImg 3s linear infinite;
  }
}
@keyframes rotateImg {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
