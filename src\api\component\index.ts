import request, { Extra } from '../index';

//  文件名联想查询
export const realTimeMatchTitle = (data: any, extra?: Extra) => {
  return request.post(
    {
      url: '/web-api/library/main/realTimeMatchTitle',
      data,
    },
    extra,
  );
};
//  通讯录联想查询
export const fuzzySearchContactInfo = (data: any, extra?: Extra) => {
  return request.post(
    {
      url: '/web-api/contacts/contact/info/fuzzySearchContactInfo',
      data,
    },
    extra,
  );
};
