import { useRef, useState } from 'react';
import { message, Popover, Button } from 'antd';
import { libraryDataBankDeleteAll, libraryRecycleRemoveOrCancel } from '@/api/library';
import RemoveModal from './RemoveModal';
import { RemoveTime, formatTimeFilterValue } from '@/components/Filters';
import Context, { createUseMainPanelCtxStore } from './Context';
import useUserStore from '@/store/useUserStore';
import type { Config } from './Context';

interface Props {
  config: Config;
  onClose?: () => void;
}

const Component = ({ config, onClose }: Props) => {
  const removeModalRef = useRef<{ open: (value?: any) => void }>();
  const [useMainPanelCtxStore] = useState(() => createUseMainPanelCtxStore({ ...config }));
  const [userInfo] = useUserStore((state) => [state.userInfo]);
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [rangeValue, setRangeValue] = useState({
    startTime: '',
    endTime: '',
  });
  const getFileFormatType = (config: Config) => {
    switch (config.module) {
      case 'audioPlay':
        return 'library_file_type_audio';
      case 'videoPlay':
        return 'library_file_type_video';
    }
  };
  const removeOk = (next: () => void) => {
    const { startTime, endTime } = rangeValue;
    if (config.removeType === 'dataBank-range-remove') {
      return libraryDataBankDeleteAll({
        userId: userInfo?.id,
        userType: userInfo?.userType,
        startTime: startTime,
        endTime: endTime,
        fileFormatType: getFileFormatType(config),
        tenantType: userInfo?.tenantType,
        deleted: 0,
      }).then((res: any) => {
        if (res.code === 0) {
          message.info('删除成功').then(() => {
            next();
            if (onClose) {
              onClose();
            }
          });
        }
      });
    }
    if (config.removeType === 'recycle-range-remove') {
      return libraryRecycleRemoveOrCancel({
        idList: undefined,
        operateType: 2,
        startTime: startTime,
        endTime: endTime,
        fileFormatType: getFileFormatType(config),
      }).then((res: any) => {
        if (res.code === 0) {
          message.info('删除成功').then(() => {
            next();
            if (onClose) {
              onClose();
            }
          });
        }
      });
    }
    if (config.removeType === 'recycle-range-cancel') {
      return libraryRecycleRemoveOrCancel({
        idList: undefined,
        operateType: 0,
        startTime: startTime,
        endTime: endTime,
        fileFormatType: getFileFormatType(config),
      }).then((res: any) => {
        if (res.code === 0) {
          message.info('取消成功').then(() => {
            next();
            if (onClose) {
              onClose();
            }
          });
        }
      });
    }
  };

  return (
    <Context.Provider
      value={{
        useMainPanelCtxStore,
        config,
      }}
    >
      <Popover
        destroyTooltipOnHide
        open={popoverOpen}
        placement="bottomRight"
        trigger="click"
        onOpenChange={(value) => {
          setPopoverOpen(value);
        }}
        content={
          <RemoveTime
            onOk={(value) => {
              const rangeValue = formatTimeFilterValue(value);
              setRangeValue(rangeValue);
              setPopoverOpen(false);
              removeModalRef.current?.open(rangeValue);
            }}
          />
        }
      >
        <Button type="primary">{config.buttonText}</Button>
      </Popover>
      <RemoveModal ref={removeModalRef} onOk={removeOk} />
    </Context.Provider>
  );
};

export default Component;
