import { Button, InputNumber, Space } from 'antd';
import { useLocation } from 'react-router-dom';
import styles from './index.module.less';

interface Value {
  rangeMin?: number;
  rangeMax?: number;
  min?: number;
  max?: number;
}
export interface Props {
  value: Value;
  onChange: (value: Value) => void;
  onSubmit: (type: 'reset' | 'ok' | 'close') => void;
}

const Component = ({ value, onChange, onSubmit }: Props) => {
  const location = useLocation();
  const changeRangeMin = (rangeMin: any) => {
    const next: Value = {
      rangeMax: value.rangeMax,
      rangeMin,
    };
    if (!next.rangeMax || rangeMin > next.rangeMax) {
      next.rangeMax = rangeMin;
    }
    onChange(next);
  };
  const changeRangeMax = (rangeMax: any) => {
    const next: Value = {
      rangeMin: value.rangeMin,
      rangeMax,
    };
    if (!next.rangeMin || rangeMax < next.rangeMin) {
      next.rangeMin = rangeMax;
    }
    onChange(next);
  };
  const changeMin = (min: any) => {
    const next: Value = {
      min,
    };
    onChange(next);
  };
  const changeMax = (max: any) => {
    const next: Value = {
      max,
    };
    onChange(next);
  };
  return (
    <div className={styles.fileSizeFilter}>
      <div className={styles.item}>
        <Space>
          <InputNumber
            rootClassName={styles.inputNumberClass}
            value={value.rangeMin}
            min={0.01}
            precision={2}
            onChange={changeRangeMin}
          />
          <span>-</span>
          <InputNumber
            rootClassName={styles.inputNumberClass}
            value={value.rangeMax}
            min={0.01}
            precision={2}
            onChange={changeRangeMax}
          />
          <span>兆</span>
        </Space>
      </div>
      <div className={styles.item}>
        <Space>
          <InputNumber
            rootClassName={styles.inputNumberClassSingle}
            value={value.min}
            min={0.01}
            precision={2}
            onChange={changeMin}
          />
          <span>兆以上</span>
        </Space>
      </div>
      <div className={styles.item}>
        <Space>
          <InputNumber
            rootClassName={styles.inputNumberClassSingle}
            value={value.max}
            min={0.01}
            precision={2}
            onChange={changeMax}
          />
          <span>兆以下</span>
        </Space>
      </div>
      <div className={styles.footer}>
        <Button type="primary" ghost size="small" onClick={() => onSubmit('reset')}>
          重置
        </Button>
        <Button type="primary" ghost size="small" onClick={() => onSubmit('close')}>
          {location.pathname === '/library' ||
          location.pathname === '/file' ||
          location.pathname === '/soft'
            ? '关闭'
            : '确认'}
        </Button>
      </div>
    </div>
  );
};

export default Component;
