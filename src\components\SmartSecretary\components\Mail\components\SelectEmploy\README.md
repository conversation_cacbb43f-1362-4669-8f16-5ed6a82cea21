### 选择通讯录公共组件

使用方式如下：

```jsx

interface Props {
  title?: string; // 头部标题
  open: boolean; // 开关标识
  onClose: () => void; // 关闭窗口
  onSubmit: (list: any[]) => void; // 提交表单
  mode: 'single' | 'multiple'; // 单选/多选
  type: 1 | 2 | null; // 通讯录类型（1：联系人，2：群）
  defaultValue?: string | string[] | any[]; // 默认选中的好友 传username。格式兼容三种： '5802666698-43' | ['5802666698-43', '5802666698-207'] | [{ username: '5802666698-43' }]
  dataSource?: any[]; // 数据源（可能只是通讯录中的一部分人，从其他接口获取）
  disabledList?: any[]; // 禁用的联系人列表[{ username: '5802666698-43',... }]
}
<SelectEmployee
  open={openAddressBook}
  title="下级审批人"
  mode="single"
  type={1}
  defaultValue={[{ username: '5802666698-43' }, { username: '5802666698-207' }]}
  disabledList={[{ username: '5802666698-43' }, { username: '5802666698-207' }]}
  onClose={() => setOpenAddressBook(false)}
  onSubmit={(list) => {
    console.log('选择的人', list);
    setOpenAddressBook(false);
  }}
/>
```
