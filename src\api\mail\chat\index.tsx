import request from '@/api/index';
import { mailDataReq, personListReq } from './mailModels';

//  邮箱接口一 根据分类查询联系人数据
export const getPersonList = (params: personListReq) => {
  return request.get({
    url: `/web-api/mail/chat/personList`,
    params,
  });
};

// 邮箱接口二 根据联系人查询邮件数据
export const getMailData = (params: mailDataReq) => {
  return request.get({
    url: '/web-api/mail/chat/page',
    params,
  });
};

//  邮箱接口三 根据分类查询群数据
export const getGroupList = (params: personListReq) => {
  return request.get({
    url: `/web-api/mail/chat/groupList`,
    params,
  });
};

// 邮箱接口四 根据群查询邮件数据
export const getGroupData = (params: mailDataReq) => {
  return request.get({
    url: '/web-api/mail/listQuery/chatPage',
    params,
  });
};

//邮箱接口五  发送邮件内部
export const sendEmailForInside = (data: any) => {
  return request.post({
    url: '/web-api/mail/sendEmail/sendEmailForInside',
    data,
  });
};

//邮箱接口六 发送邮件外部
export const sendEmailForOuter = (data: any) => {
  return request.post({
    url: '/web-api/mail/sendEmail/sendEmailForOuter',
    data,
  });
};

//邮箱接口七 获取红点数
export const getRedDot = (data: any) => {
  return request.post({
    url: '/web-api/mail/redDot/getRedDot',
    data,
  });
};

//邮箱接口八 修改邮件已读未读状态
export const updateReadFlag = (data: any) => {
  return request.post({
    url: '/web-api/mail/status/updateReadFlag',
    data,
  });
};

//邮箱接口九 浏览单封邮件数据
export const chatSingle = (data: any) => {
  return request.post({
    url: '/web-api/mail/browse/chatSingle',
    data,
  });
};

//邮箱接口十 修改邮件撤回状态
export const updateRevokeflag = (data: any) => {
  return request.post({
    url: '/web-api/mail/status/updateRevokeflag',
    data,
  });
};

//邮箱接口十一 修改邮件读取和撤回状态
export const updateReadFlagAndRevokeflag = (data: any) => {
  return request.post({
    url: '/web-api/mail/status/updateReadFlagAndRevokeflag',
    data,
  });
};
//邮箱接口十二 更新邮件数据
export const getMail = () => {
  return request.get({
    url: '/web-api/mail/james/getMail',
  });
};

//邮箱接口十三 获取邮件分类数据
export const getMailTypeEnum = (data: any) => {
  return request.post({
    url: '/web-api/mail/redDot/getMailTypeEnum',
    data,
  });
};

//邮箱接口十四  获取邮件对应页数据
export const chatList = (data: any) => {
  return request.post({
    url: '/web-api/mail/browse/chatList',
    data,
  });
};

export const contactsMailsPage = (data: any) => {
  return request.post({
    url: '/web-api/mail/homePage/contactsMailsPage',
    data,
  });
};

/** 小秘书邮箱接口 */

/**
 *
 * @param pageNo
 * @param pageSize
 * @returns
 */
export const getLatestContacts = (data: any) => {
  return request.post({
    url: '/web-api/mail/homePage/getLatestContacts',
    data,
  });
};

export const getChatLatestContacts = (params: any) => {
  return request.get({
    url: '/web-api/mail/chat/getLatestContacts',
    params,
  });
};
export const getChatLatestGroups = (params: any) => {
  return request.get({
    url: '/web-api/mail/chat/getLatestGroups',
    params,
  });
};
