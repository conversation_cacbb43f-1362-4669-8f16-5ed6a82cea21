import { useLoadingListStore } from '@/components/LoadingList';
import { useEffect, useState } from 'react';
import Context, { createUseMainPanelCtxStore, createUseSelectFilesCtxStore } from './Context';
import MainPanel from './MainPanel';
import SelectFiles from './SelectFiles';
import type { LoadingItem } from '@/components/LoadingList';

interface Props {
  config: LoadingItem;
}

const Component = ({ config }: Props) => {
  const [useMainPanelCtxStore] = useState(() => createUseMainPanelCtxStore({ ...config }));
  const [useSelectFilesCtxStore] = useState(() => createUseSelectFilesCtxStore({ ...config }));
  const [setCurrentLoadingItem, setRemovedLoadingItem] = useLoadingListStore((state) => [
    state.setCurrentLoadingItem,
    state.setRemovedLoadingItem,
  ]);
  const [setSelectFilesOpen] = useSelectFilesCtxStore((state) => [state.setSelectFilesOpen]);
  const [setTimestamp, setMainPanelOpen] = useMainPanelCtxStore((state) => [
    state.setTimestamp,
    state.setMainPanelOpen,
  ]);
  const minimize = () => {
    setSelectFilesOpen(false);
    setMainPanelOpen(false);
    setCurrentLoadingItem({
      module: config.module,
      type: config.type,
      modalVisible: false,
      itemVisible: true,
    });
  };
  const cancel = () => {
    setSelectFilesOpen(false);
    setMainPanelOpen(false);
    setRemovedLoadingItem({ ...config });
    useMainPanelCtxStore.reset();
  };
  useEffect(() => {
    if (config.modalVisible) {
      setTimestamp(Date.now());
      setMainPanelOpen(true);
    } else {
      setSelectFilesOpen(false);
      setMainPanelOpen(false);
    }
  }, [config]);

  return (
    <Context.Provider value={{ useMainPanelCtxStore, useSelectFilesCtxStore, config }}>
      <MainPanel onMinimize={minimize} onCancel={cancel} />
      <SelectFiles />
    </Context.Provider>
  );
};

export default Component;
