const getXMLRequest = (url: string) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.onload = () => {
      if (xhr.readyState === 4) {
        if (xhr.status === 200) {
          resolve(JSON.parse(xhr.responseText));
        } else {
          reject(xhr.statusText);
        }
      }
    };
    xhr.onerror = () => {
      reject(xhr.statusText);
    };
    xhr.send(null);
  });
};
const protocal = process.env.NODE_ENV === 'development' ? 'http://' : 'https://';
// 获取ip
export const fetchIp = () => {
  // TODO: 生产环境隐藏key
  // const concatKey =
  //   process.env.NODE_ENV === 'development' ? `?key=${import.meta.env.VITE_AMAP_KEY}` : '?';
  const concatKey = `?key=${import.meta.env.VITE_AMAP_KEY}`;

  return getXMLRequest(`${protocal}restapi.amap.com/v3/ip${concatKey}`);
};

// 获取天气
export const fetchWeather = (cityCode: string) => {
  // TODO: 生产环境隐藏key
  // const concatKey =
  //   process.env.NODE_ENV === 'development' ? `&key=${import.meta.env.VITE_AMAP_KEY}` : '';
  const concatKey = `&key=${import.meta.env.VITE_AMAP_KEY}`;

  return getXMLRequest(
    `${protocal}restapi.amap.com/v3/weather/weatherInfo?city=${cityCode}${concatKey}&extensions=all`,
  );
};
