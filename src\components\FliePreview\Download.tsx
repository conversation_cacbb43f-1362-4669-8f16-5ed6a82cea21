import type { File } from './interface';
import { useLoadingListStore } from '@/components/LoadingList';
import { DownloadButton } from '@/components/Download';

interface Props {
  file: File;
}

export default ({ file }: Props) => {
  const [setCurrentLoadingItem] = useLoadingListStore((state) => [state.setCurrentLoadingItem]);

  return (
    <DownloadButton
      type="primary"
      onClick={() => {
        setCurrentLoadingItem({
          module: 'preview',
          type: 'downloadAll',
          modalVisible: true,
          itemVisible: false,
          fileList: [file],
        });
      }}
    >
      下载文件
    </DownloadButton>
  );
};
