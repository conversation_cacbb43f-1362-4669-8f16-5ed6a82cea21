import { cppbRequest } from '@/store/useCppBridgeStore';
import { getRandom } from '@/utils/common';
import { shareError } from '@/utils/modal';
import { Button, Space, Table } from 'antd';
import { useContext, useEffect, useMemo } from 'react';
import Context from '../Context';
import { getColumns } from '../SelectFiles/columns';
import styles from './index.module.less';

const Component = () => {
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [selectedFileList, setSelectedFileList, selectedFileMap, setSelectedFileMap] =
    useMainPanelCtxStore!((state) => [
      state.selectedFileList,
      state.setSelectedFileList,
      state.selectedFileMap,
      state.setSelectedFileMap,
    ]);
  //浏览
  const scanBtn = async (row: any) => {
    const response = await cppbRequest({
      module: 'desktopFile',
      id: getRandom(6), // 消息id 随机数
      method: 'openfile',
      data: {
        filePath: row.filePath,
      },
    });
    console.log('cppResponse', response);
    if (response.code !== 0) {
      shareError(response.data.status, response.data.status);
    }
  };
  const columns = useMemo(() => {
    const hasInitFiles = selectedFileList.filter((item: any) => item.status === 'init').length > 0;
    return getColumns(false, config).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              {!hasInitFiles && <span>进度</span>}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          let element;
          if (row._status === 'pause') {
            element = <span className={styles.pause}>暂停上传</span>;
          } else if (row.status === 'abort') {
            element = <span className={styles.abort}>终止分享</span>;
          } else if (row.status === 'success') {
            element = <span className={styles.success}>已完成</span>;
          } else if (row.status === 'loading') {
            element = <span className={styles.uploading}>正在分享</span>;
          } else if (row.status === 'waiting') {
            element = <span className={styles.waiting}>等待中</span>;
          } else if (row.status === 'error') {
            element = <span className={styles.error}>分享错误</span>;
          } else {
            element = (
              <Space>
                <Button
                  style={{ paddingLeft: 0, paddingRight: 0 }}
                  type="link"
                  size="small"
                  onClick={() => {
                    scanBtn(row);
                  }}
                >
                  浏览
                </Button>
              </Space>
            );
          }
          return element;
        };
      }
      return item;
    });
  }, [selectedFileList, selectedFileMap]);
  return (
    <div className={styles.list}>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={selectedFileList}
        rowKey={'id'}
        scroll={{ y: 491 }}
        pagination={false}
      />
    </div>
  );
};

export default Component;
