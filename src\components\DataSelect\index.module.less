.data-wrapper {
  &.single {
    width: 325px;
    .footer {
      justify-content: flex-end;
    }
  }
  width: 650px;
  height: 443px;
  border-radius: 8px;
  opacity: 1;
  background: #ffffff;
  box-sizing: border-box;
  border: 1px solid #8c9eff;
  box-shadow: 0px 20px 40px 0px rgba(0, 0, 0, 0.1);
  .header {
    height: 62px;
    display: flex;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    .col {
      display: flex;
      justify-content: space-between;
      position: relative;
      flex: 1;
      padding: 16px;
      h3 {
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        color: #4d70fe;
      }
      + .col {
        &::before {
          position: absolute;
          content: ' ';
          left: 0;
          top: 0;
          width: 1px;
          height: 100%;
          background: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }
  .content {
    display: flex;
    .col {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      .filter {
        height: 65px;
        padding: 16px 0 16px 16px;
        :global {
          .ant-form-item {
            margin-inline-end: 8px;
            button {
              width: 72px;
            }
          }
        }
      }
      .list {
        height: 252px;
        overflow-y: auto;
        .item {
          padding: 8px 16px;
          &:hover {
            background: #e8eaf6;
          }
          &.active {
            button {
              background: #92a2ff;
              color: #ffffff;
            }
            color: var(--yb-primary-color);
          }
        }
      }
      + .col {
        &::before {
          position: absolute;
          content: ' ';
          left: 0;
          top: 0;
          width: 1px;
          height: 100%;
          background: rgba(0, 0, 0, 0.05);
          transform: scaleX(0.5);
        }
      }
    }
  }
  .footer {
    height: 64px;
    display: flex;
    padding: 0 16px;
    align-items: center;
    justify-content: center;
    position: relative;
    &::before {
      position: absolute;
      content: ' ';
      left: 0;
      top: 0;
      right: 0;
      height: 1px;
      transform: scaleY(0.5);
      background: rgba(0, 0, 0, 0.05);
    }
  }
}
