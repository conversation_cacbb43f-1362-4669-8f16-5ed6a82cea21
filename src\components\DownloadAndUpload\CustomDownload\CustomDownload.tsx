import { FC, useState, useEffect } from 'react';
import styles from './CustomDownload.module.less';
import FlieDownload from '../FlieDownload/FlieDownload';
import FileChoose from '../FileChoose/FileChoose';
interface ModalProps {
  onClose?: () => void; // 关闭弹框回调方法
}
const CustomDownload = (ModalProps: ModalProps) => {
  const [isShowFileChoose, setIsShowFileChoose] = useState<boolean>(false);
  const [Data, setData] = useState<any>([]);
  useEffect(() => {}, []);
  return (
    <>
      <div className={styles.CustomDownload}>
        <FlieDownload
          data={Data}
          onCloseModal={ModalProps.onClose}
          showFileChoose={() => {
            setIsShowFileChoose(true);
          }}
        />
        {isShowFileChoose ? (
          <FileChoose
            setData={setData}
            isOpen={isShowFileChoose}
            onClose={() => {
              setIsShowFileChoose(false);
            }}                                          
          />
        ) : null}
      </div>
    </>
  );
};

export default CustomDownload;
