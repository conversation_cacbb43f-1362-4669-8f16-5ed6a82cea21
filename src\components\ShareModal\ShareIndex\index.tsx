import MultiModal from '@/components/MultiModal';
import { Button } from 'antd';
import { useEffect, useState } from 'react';
import SharedData from '../SharedData';
import SharedGroup from '../SharedGroup';
import SharedUser from '../SharedUser';
import ToolBar from '../ToolBar';
import { ModalType } from './typing';

interface Props {
  open: boolean;
  onCancel?: () => void;
  onToolBarClick?: (type: ModalType) => void;
  onShare: (data: any) => void;
  sharedData?: any[];
  userDataSource?: any[];
  groupDataSource?: any[];
  columns?: any[];
  dataTitle: string; // 业务标题
  disableGroup?: boolean; // 禁用群
  secure?: boolean; // 是否有保密等级
  minSecureLevel?: number; // 最小保密登记
  onSetSecure?: (data: number) => void;
  loading?: boolean; // 加载中
  confirmText?: string;
  destroyOnClose?: boolean;
}

const Component = ({
  userDataSource,
  groupDataSource,
  sharedData,
  open,
  onShare,
  onCancel,
  columns,
  dataTitle,
  onToolBarClick,
  loading,
  disableGroup,
  secure,
  onSetSecure,
  confirmText = '确认分享',
}: Props) => {
  const ok = () => {
    onShare({
      user: userDataSource,
      group: groupDataSource,
      data: sharedData,
    });
  };

  const enableShare = (
    userDataSource: any[] | undefined,
    groupDataSource: any[] | undefined,
    shareData: any[] | undefined,
  ) => {
    return (
      ((columns && shareData && shareData?.length > 0) || !columns) &&
      ((groupDataSource && groupDataSource.length > 0) ||
        (userDataSource && userDataSource.length > 0))
    );
  };

  const [minSecureLevel, setMinSecureLevel] = useState(0);

  useEffect(() => {
    if (secure) {
      if (sharedData && sharedData.length) {
        setMinSecureLevel(
          Math.max(...sharedData.map((item: any) => item.confidentialityStatus)) || 0,
        );
      } else {
        setMinSecureLevel(0);
      }
    }
  }, [sharedData]);

  return (
    <MultiModal
      layoutClassName="modalRight"
      title={dataTitle}
      open={open}
      onCancel={onCancel}
      footer={[
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={ok}
          className={'footer-btn'}
          disabled={!enableShare(userDataSource, groupDataSource, sharedData)}
        >
          {confirmText}
        </Button>,
      ]}
    >
      <ToolBar
        onClick={onToolBarClick}
        secure={secure}
        minSecureLevel={minSecureLevel}
        dataTitle={dataTitle}
        onSetSecure={onSetSecure}
        disableGroup={disableGroup}
        hasColumns={columns && columns.length > 0}
      />
      <SharedUser dataSource={userDataSource} />
      {!disableGroup && <SharedGroup dataSource={groupDataSource} />}
      {columns && <SharedData dataSource={sharedData} columns={columns} dataTitle={dataTitle} />}
    </MultiModal>
  );
};
export default Component;
