export interface ListItem {
  albumsTemplateId: number;
  albumsTemplateName: string;
  createTime: string;
  albumsTemplateCoverUrl: any;
  albumsTemplateData: string;
  albumsTemplateCription: string;
  songId: number;
  templateTag?: boolean;
  senderUserId?: string;
  albumsName: any;
  albumsId: string;
}

export interface AlbumListItem {
  albumsTemplateName: any;
  albumsName: any;
  albumsId: string;
  userId: string;
  albumsTemplateId: number;
  name: string;
  description: string;
  createTime: string;
  coverPhotoUrl: string;
  albumsTemplateCoverUrl: string;
  templateTag: boolean;
  senderUserId?: string;
  shareTime: string;
  albumsCategorySingle: any;
  shareId?: string;
}
