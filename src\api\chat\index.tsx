import request from '../index';
//  获取历史消息
export const getHistoryMsg = (data: any) => {
  return request.get({
    url: '/app-api/chat/message/private/history',
    params: data,
  });
};

// 发送私聊消息
export const sendSingleMsg = (data: any) => {
  return request.post({
    url: '/app-api/chat/message/private/send',
    data,
  });
};

// 分页查询会话的历史消息（单聊）
export const getSingleMsgList = (data: any) => {
  return request.post({
    url: '/app-api/chat/message/private/getHistoryMessageByPage',
    data,
  });
};
