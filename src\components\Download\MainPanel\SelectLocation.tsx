import MultiModal from '@/components/MultiModal';
import { cwsRequest } from '@/store/useCppWebSocketStore';
import { Button, Space, Tree } from 'antd';
import { forwardRef, useContext, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import Context from '../Context';
import styles from './index.module.less';

interface DataNode {
  label: string;
  value: string;
  isLeaf?: boolean;
  children?: DataNode[];
  [prop: string]: any;
}

interface Props {
  onCancel?: () => void;
  onOk?: (path: any) => void;
  onCreateFolder?: () => void;
}

const updateTreeData = (data: DataNode[], value: string, children: DataNode[]): any => {
  return data.map((item) => {
    if (item.value === value) {
      return {
        ...item,
        children,
      };
    }
    if (item.children) {
      return {
        ...item,
        children: updateTreeData(item.children, value, children),
      };
    }
    return item;
  });
};

const Component = forwardRef(({ onCancel, onOk, onCreateFolder }: Props, ref) => {
  useImperativeHandle(ref, () => ({
    getCreatedFolder: () => {
      loadData({
        value: location,
      });
    },
  }));
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [location, setLocation] = useMainPanelCtxStore!((state) => [
    state.location,
    state.setLocation,
  ]);
  const [treeData, setTreeData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<any[]>([]);
  const [createFolderDisabled, setCreateFolderDisabled] = useState(true);
  const okDisabled = useMemo(() => {
    return location === '';
  }, [location]);
  const loadData = ({ value, children }: any) => {
    return new Promise<void>((resolve) => {
      if (children) {
        return resolve();
      }
      cwsRequest({
        module: 'desktopFile',
        method: 'getSubDirectories',
        data: {
          path: value,
        },
      }).then((res: any) => {
        if (res.code === 0) {
          const children = JSON.parse(res.data);
          setExpandedKeys((prev) => [...prev, value]);
          setTreeData((prev) => {
            return updateTreeData(prev, value, children);
          });
          resolve();
        }
      });
    });
  };
  const expand = (expandedKeys: any) => {
    setExpandedKeys(expandedKeys);
  };
  const select = (selectedKeys: any) => {
    setCreateFolderDisabled(!selectedKeys.length);
    setCheckedKeys(selectedKeys);
    setLocation(selectedKeys[0] || '');
  };
  const createFolder = () => {
    if (onCreateFolder) {
      onCreateFolder();
    }
  };
  const cancel = () => {
    setLocation('');
    if (onCancel) {
      onCancel();
    }
  };
  const ok = () => {
    if (onOk) {
      onOk(location);
    }
  };
  useEffect(() => {
    cwsRequest({
      module: 'desktopFile',
      method: 'getRootDirectories',
      data: null,
    }).then((res: any) => {
      if (res.code === 0) {
        const data = JSON.parse(res.data) || [];
        setTreeData(data);
      }
    });
  }, []);

  return (
    <MultiModal
      layoutGroup={`download_${config?.module}_${config?.type}`}
      layoutClassName="normal"
      destroyOnClose={true}
      top={185}
      width={550}
      title="选择一键下载位置"
      open={true}
      onCancel={cancel}
      zIndex={1900}
      footer={
        <div className={styles.selectLocationModalFooter}>
          <div></div>
          {/* <Button type="primary" ghost disabled={createFolderDisabled} onClick={createFolder}>
            新建文件夹
          </Button> */}
          <Space>
            <Button type="primary" ghost onClick={cancel}>
              取消
            </Button>
            <Button type="primary" disabled={okDisabled} onClick={ok}>
              确认
            </Button>
          </Space>
        </div>
      }
    >
      <div className={styles.selectLocation}>
        <Tree
          expandedKeys={expandedKeys}
          checkedKeys={checkedKeys}
          fieldNames={{ title: 'label', key: 'value', children: 'children' }}
          treeData={treeData}
          loadData={loadData}
          onExpand={expand}
          onSelect={select}
          blockNode
          titleRender={(nodeData: any) => {
            console.log('nodeData', nodeData);

            return (
              <div className={styles.treeLine}>
                <div className={styles.titleBox}>
                  <div className={styles.text}>{nodeData.label}</div>
                  {checkedKeys.length > 0 && checkedKeys[0] === nodeData.value ? (
                    <span className={styles.actived}>已选</span>
                  ) : (
                    <span className={styles.normal}>选择</span>
                  )}
                </div>
                {checkedKeys.length > 0 && checkedKeys[0] === nodeData.value && (
                  <div className={styles.rightBtn}>
                    <Button
                      size="small"
                      onClick={(event) => {
                        event.stopPropagation(); 
                        createFolder();
                      }}
                    >
                      新建文件夹
                    </Button>
                    <Button
                      size="small"
                      onClick={(event) => {
                        event.stopPropagation();
                        ok();
                      }}
                    >
                      直接保存
                    </Button>
                  </div>
                )}
              </div>
            );
          }}
        />
      </div>
    </MultiModal>
  );
});

export default Component;
