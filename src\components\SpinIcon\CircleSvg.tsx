import { useEffect } from 'react';
import styles from './index.module.less';
import svgType from './typing';
const Component = ({ fontSize = '24px', fill = '#fff' }: svgType) => {
  useEffect(() => {}, []);

  return (
    <>
      <div className={styles.spinning} style={{ fontSize: fontSize }}>
        <svg fill="none" version="1.1" width={fontSize} height={fontSize} viewBox="0 0 20 20">
          <g>
            <path
              d="M10,0C10.5523,0,11,0.44772,11,1L11,4C11,4.55228,10.5523,5,10,5C9.4477,5,9,4.55228,9,4L9,1C9,0.44772,9.4477,0,10,0ZM10,15C10.5523,15,11,15.4477,11,16L11,19C11,19.5523,10.5523,20,10,20C9.4477,20,9,19.5523,9,19L9,16C9,15.4477,9.4477,15,10,15ZM20,10C20,10.5523,19.5523,11,19,11L16,11C15.4477,11,15,10.5523,15,10C15,9.4477,15.4477,9,16,9L19,9C19.5523,9,20,9.4477,20,10ZM5,10C5,10.5523,4.55228,11,4,11L1,11C0.44772,11,0,10.5523,0,10C0,9.4477,0.44772,9,1,9L4,9C4.55228,9,5,9.4477,5,10ZM17.0711,17.0711C16.6805,17.4616,16.0474,17.4616,15.6569,17.0711L13.5355,14.9497C13.145,14.5592,13.145,13.9261,13.5355,13.5355C13.9261,13.145,14.5592,13.145,14.9497,13.5355L17.0711,15.6569C17.4616,16.0474,17.4616,16.6805,17.0711,17.0711ZM6.46447,6.46447C6.07394,6.85499,5.44078,6.85499,5.05025,6.46447L2.92893,4.34315C2.53841,3.95262,2.53841,3.31946,2.92893,2.92893C3.31946,2.53841,3.95262,2.53841,4.34315,2.92893L6.46447,5.05025C6.85499,5.44078,6.85499,6.07394,6.46447,6.46447ZM2.92893,17.0711C2.53841,16.6805,2.53841,16.0474,2.92893,15.6569L5.05025,13.5355C5.44078,13.145,6.07394,13.145,6.46447,13.5355C6.85499,13.9261,6.85499,14.5592,6.46447,14.9497L4.34315,17.0711C3.95262,17.4616,3.31946,17.4616,2.92893,17.0711ZM13.5355,6.46447C13.145,6.07394,13.145,5.44078,13.5355,5.05025L15.6569,2.92893C16.0474,2.53841,16.6805,2.53841,17.0711,2.92893C17.4616,3.31946,17.4616,3.95262,17.0711,4.34315L14.9497,6.46447C14.5592,6.85499,13.9261,6.85499,13.5355,6.46447Z"
              fill={fill}
              fillOpacity="1"
            />
          </g>
        </svg>
      </div>
    </>
  );
};

export default Component;
