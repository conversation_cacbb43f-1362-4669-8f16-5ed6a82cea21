export const titleConfigMap: any = {
  '1': {
    leftTitle: '选择工作手机配置记录',
    rightTitle: '已选工作手机配置记录',
  },
  '2': {
    leftTitle: '选择备用手机配置记录',
    rightTitle: '已选备用手机配置记录',
  },
  '3': {
    leftTitle: '选择支付方式配置记录',
    rightTitle: '已选支付方式配置记录',
  },
  '4': {
    leftTitle: '选择子账户配置',
    rightTitle: '已选子账户配置',
  },
};

// 账户管理的所有模块信息
export const tipsConfigAll = (activeIndex: string, selectDataLength: number) => {
  // 手机配置
  const mapKeyInfo: object = {
    delSelect: {
      secondConfirmTitle: '确认删除吗',
      secondConfirmOkText: '确认删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条记录将放入<span className="text-[#3D5AFE]">回收站</span>！你可以在回收站
          <span className="text-[#F4511E]">取消删除</span>
          或者<span className="text-[#F4511E]">永久删除</span>
        </>
      ),
      tipTitle: '数据已全部删除成功',
      tipContent: (
        <>
          你可以在回收站选择<span className="text-[#F4511E]">取消删除</span>
          或者<span className="text-[#F4511E]">永久删除</span>
        </>
      ),
    }, //选择删除【数据银行】
    delForverSelect: {
      //选择永久删除【回收站】
      secondConfirmTitle: '确认永久删除吗',
      secondConfirmOkText: '确认永久删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条工作手机配置记录将<span className="text-[#F4511E]">永久删除,无法恢复</span>！ 请谨慎选择
        </>
      ),
      tipTitle: '删除成功',
      tipContent: (
        <>
          编辑器的选择数据已<span className="text-[#F4511E]">彻底删除</span>
        </>
      ),
    },
    delCancelSelect: {
      //取消删除【回收站】
      secondConfirmTitle: '确认取消删除吗',
      secondConfirmOkText: '确认取消删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          个条记录将移回原来的位置
        </>
      ),
      tipTitle: '取消删除成功',
      tipContent: (
        <>
          你选择的数据已移回<span className="text-[#F4511E]">原来的位置</span>
        </>
      ),
    },
    delAll: {
      //全部删除【数据银行】
      secondConfirmTitle: '确认全部删除吗',
      secondConfirmOkText: '确认删除',
      delSelectSpan: (
        <>
          确认后，你的<span className="text-[#F4511E]">全部数据</span>将放入
          <span className="text-[#F4511E]">回收站</span>！你可以在回收站
          <span className="text-[#F4511E]">取消删除</span>或者
          <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
      tipTitle: '数据已全部删除成功',
      tipContent: (
        <>
          你可以在回收站选择<span className="text-[#F4511E]">取消删除</span>
          或者 <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
    },
    delCancelAll: {
      //全部取消删除【回收站】
      secondConfirmTitle: '确认全部取消删除吗',
      secondConfirmOkText: '确认全部取消删除',
      delSelectSpan: <>确认后，全部文件将全部移回原来的位置</>,
      tipTitle: '取消删除成功',
      tipContent: (
        <>
          你的全部数据已移回<span className="text-[#F4511E]">原来的位置</span>
        </>
      ),
    },
    delForverAll: {
      //全部永久删除【回收站】
      secondConfirmTitle: '确认全部永久删除吗',
      secondConfirmOkText: '确认永久删除',
      delSelectSpan: (
        <>
          确认后，手机配置记录的<span className="text-[#3D5AFE]">全部数据</span>将
          <span className="text-[#F4511E]">永久删除</span>！ 请谨慎选择
        </>
      ),
      tipTitle: '删除成功',
      tipContent: (
        <>
          手机配置的全部数据已<span className="text-[#F4511E]">彻底删除</span>
        </>
      ),
    },
  };
  // 备用手机配置
  const mapKeyInfo2: object = {
    delSelect: {
      secondConfirmTitle: '确认删除吗',
      secondConfirmOkText: '确认删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条记录将放入<span className="text-[#3D5AFE]">回收站</span>！你可以在回收站
          <span className="text-[#F4511E]">取消删除</span>
          或者 <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
      tipTitle: '数据已全部删除成功',
      tipContent: (
        <>
          你可以在回收站选择<span className="text-[#F4511E]">取消删除</span>
          或者 <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
    }, //选择删除【数据银行】
    delForverSelect: {
      //选择永久删除【回收站】
      secondConfirmTitle: '确认永久删除吗',
      secondConfirmOkText: '确认永久删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条记录将<span className="text-[#F4511E]">永久删除,无法恢复</span>！ 请谨慎选择
        </>
      ),
      tipTitle: '删除成功',
      tipContent: (
        <>
          备用手机配置的选择数据已<span className="text-[#F4511E]">彻底删除</span>
        </>
      ),
    },
    delCancelSelect: {
      //取消删除【回收站】
      secondConfirmTitle: '确认取消删除吗',
      secondConfirmOkText: '确认取消删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条记录将移回原来的位置
        </>
      ),
      tipTitle: '取消删除成功',
      tipContent: (
        <>
          你选择的数据已移回<span className="text-[#F4511E]">原来的位置</span>
        </>
      ),
    },
    delAll: {
      //全部删除【数据银行】
      secondConfirmTitle: '确认全部删除吗',
      secondConfirmOkText: '确认删除',
      delSelectSpan: (
        <>
          确认后，你的<span className="text-[#F4511E]">全部数据</span>将放入
          <span className="text-[#F4511E]">回收站</span>！你可以在回收站
          <span className="text-[#F4511E]">取消删除</span>或者
          <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
      tipTitle: '数据已全部删除成功',
      tipContent: (
        <>
          你可以在回收站选择<span className="text-[#F4511E]">取消删除</span>
          或者 <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
    },
    delCancelAll: {
      //全部取消删除【回收站】
      secondConfirmTitle: '确认全部取消删除吗',
      secondConfirmOkText: '确认全部取消删除',
      delSelectSpan: <>确认后，全部文件将全部移回原来的位置</>,
      tipTitle: '取消删除成功',
      tipContent: (
        <>
          你的全部数据已移回<span className="text-[#F4511E]">原来的位置</span>
        </>
      ),
    },
    delForverAll: {
      //全部永久删除【回收站】
      secondConfirmTitle: '确认永久删除吗',
      secondConfirmOkText: '确认永久删除',
      delSelectSpan: (
        <>
          确认后，备用手机配置的<span className="text-[#3D5AFE]">全部数据</span>将
          <span className="text-[#F4511E]">永久删除,无法恢复</span>！ 请谨慎选择
        </>
      ),
      tipTitle: '删除成功',
      tipContent: (
        <>
          备用手机配置的全部数据已<span className="text-[#F4511E]">彻底删除</span>
        </>
      ),
    },
  };
  // 支付历史记录
  const mapKeyInfo3: object = {
    delSelect: {
      secondConfirmTitle: '确认删除吗',
      secondConfirmOkText: '确认删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条记录将放入<span className="text-[#3D5AFE]">回收站</span>！你可以在回收站
          <span className="text-[#F4511E]">取消删除</span>
          或者 <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
      tipTitle: '数据已全部删除成功',
      tipContent: (
        <>
          你可以在回收站选择<span className="text-[#F4511E]">取消删除</span>
          或者 <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
    }, //选择删除【数据银行】
    delForverSelect: {
      //选择永久删除【回收站】
      secondConfirmTitle: '确认永久删除吗',
      secondConfirmOkText: '确认永久删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条记录将<span className="text-[#F4511E]">永久删除,无法恢复</span>！ 请谨慎选择
        </>
      ),
      tipTitle: '删除成功',
      tipContent: (
        <>
          支付历史记录的选择数据已<span className="text-[#F4511E]">彻底删除</span>
        </>
      ),
    },
    delCancelSelect: {
      //取消删除【回收站】
      secondConfirmTitle: '确认取消删除吗',
      secondConfirmOkText: '确认取消删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条记录将移回原来的位置
        </>
      ),
      tipTitle: '取消删除成功',
      tipContent: (
        <>
          你选择的数据已移回<span className="text-[#F4511E]">原来的位置</span>
        </>
      ),
    },
    delAll: {
      //全部删除【数据银行】
      secondConfirmTitle: '确认全部删除吗',
      secondConfirmOkText: '确认删除',
      delSelectSpan: (
        <>
          确认后，你的<span className="text-[#F4511E]">全部数据</span>将放入
          <span className="text-[#F4511E]">回收站</span>！你可以在回收站
          <span className="text-[#F4511E]">取消删除</span>或者
          <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
      tipTitle: '数据已全部删除成功',
      tipContent: (
        <>
          你可以在回收站选择<span className="text-[#F4511E]">取消删除</span>
          或者 <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
    },
    delCancelAll: {
      //全部取消删除【回收站】
      secondConfirmTitle: '确认全部取消删除吗',
      secondConfirmOkText: '确认全部取消删除',
      delSelectSpan: <>确认后，全部文件将全部移回原来的位置</>,
      tipTitle: '取消删除成功',
      tipContent: (
        <>
          你的全部数据已移回<span className="text-[#F4511E]">原来的位置</span>
        </>
      ),
    },
    delForverAll: {
      //全部永久删除【回收站】
      secondConfirmTitle: '确认永久删除吗',
      secondConfirmOkText: '确认永久删除',
      delSelectSpan: (
        <>
          确认后，支付历史记录的<span className="text-[#3D5AFE]">全部数据</span>将
          <span className="text-[#F4511E]">永久删除,无法恢复</span>！ 请谨慎选择
        </>
      ),
      tipTitle: '删除成功',
      tipContent: (
        <>
          支付历史记录的全部数据已<span className="text-[#F4511E]">彻底删除</span>
        </>
      ),
    },
  };
  // 子账户配置
  const mapKeyInfo4: object = {
    delSelect: {
      secondConfirmTitle: '确认删除吗',
      secondConfirmOkText: '确认删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条记录将放入<span className="text-[#3D5AFE]">回收站</span>！你可以在回收站
          <span className="text-[#F4511E]">取消删除</span>
          或者 <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
      tipTitle: '数据已全部删除成功',
      tipContent: (
        <>
          你可以在回收站选择<span className="text-[#F4511E]">取消删除</span>
          或者 <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
    }, //选择删除【数据银行】
    delForverSelect: {
      //选择永久删除【回收站】
      secondConfirmTitle: '确认永久删除吗',
      secondConfirmOkText: '确认永久删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条记录将<span className="text-[#F4511E]">永久删除,无法恢复</span>！ 请谨慎选择
        </>
      ),
      tipTitle: '删除成功',
      tipContent: (
        <>
          子账户配置的选择数据已<span className="text-[#F4511E]">彻底删除</span>
        </>
      ),
    },
    delCancelSelect: {
      //取消删除【回收站】
      secondConfirmTitle: '确认取消删除吗',
      secondConfirmOkText: '确认取消删除',
      delSelectSpan: (
        <>
          确认后，已选择的 <span className="text-[#3D5AFE]">{selectDataLength}</span>
          条记录将移回原来的位置
        </>
      ),
      tipTitle: '取消删除成功',
      tipContent: (
        <>
          你选择的数据已移回<span className="text-[#F4511E]">原来的位置</span>
        </>
      ),
    },
    delAll: {
      //全部删除【数据银行】
      secondConfirmTitle: '确认全部删除吗',
      secondConfirmOkText: '确认删除',
      delSelectSpan: (
        <>
          确认后，你的<span className="text-[#F4511E]">全部数据</span>将放入
          <span className="text-[#F4511E]">回收站</span>！你可以在回收站
          <span className="text-[#F4511E]">取消删除</span>或者
          <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
      tipTitle: '数据已全部删除成功',
      tipContent: (
        <>
          你可以在回收站选择<span className="text-[#F4511E]">取消删除</span>
          或者 <span className="text-[#F4511E]">永久删除</span>
        </>
      ),
    },
    delCancelAll: {
      //全部取消删除【回收站】
      secondConfirmTitle: '确认全部取消删除吗',
      secondConfirmOkText: '确认全部取消删除',
      delSelectSpan: <>确认后，全部文件将全部移回原来的位置</>,
      tipTitle: '取消删除成功',
      tipContent: (
        <>
          你的全部数据已移回<span className="text-[#F4511E]">原来的位置</span>
        </>
      ),
    },
    delForverAll: {
      //全部永久删除【回收站】
      secondConfirmTitle: '确认永久删除吗',
      secondConfirmOkText: '确认永久删除',
      delSelectSpan: (
        <>
          确认后，子账户配置记录的<span className="text-[#3D5AFE]">全部数据</span>将
          <span className="text-[#F4511E]">永久删除,无法恢复</span>！ 请谨慎选择
        </>
      ),
      tipTitle: '删除成功',
      tipContent: (
        <>
          子账户配置的全部数据已<span className="text-[#F4511E]">彻底删除</span>
        </>
      ),
    },
  };

  switch (activeIndex) {
    case '1':
      return mapKeyInfo;
    case '2':
      return mapKeyInfo2;
    case '3':
      return mapKeyInfo3;
    case '4':
      return mapKeyInfo4;
  }
};
