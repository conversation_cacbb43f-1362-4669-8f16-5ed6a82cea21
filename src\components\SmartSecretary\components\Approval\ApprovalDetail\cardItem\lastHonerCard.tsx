import { approvalRes } from '@/api/Approval/module';
import PreviewAlbum from '@/pages/MagicAlbum/components/PreviewAlbum';
import { parserHtmlToString } from '@/utils/parser';
import { Card, Descriptions, Typography } from 'antd';
import { FC, useEffect, useState } from 'react';
import styles from './index.module.less';
const { Paragraph } = Typography;

interface Props {
  approvalRes: approvalRes;
}

const Component: FC<Props> = ({ approvalRes }) => {
  const [expanded, setExpanded] = useState<boolean>(false);
  const [expanded1, setExpanded1] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [viewOpen, setViewOpen] = useState(false);
  const [viewAlbumData, setViewAlbumData] = useState<any>({
    albumsId: '',
    albumsName: '',
    userName: '',
    createTime: 0,
    totalSize: 0,
    approvalData: {},
  });
  const openPreviewAlbum = async (approvalData: any) => {
    setViewAlbumData({
      albumsId: approvalData.bizId,
      albumsName: approvalData.approvalFullName,
      userName: '',
      createTime: 0,
      totalSize: 0,
      approvalData,
    });
    setViewOpen(true);
  };
  const viewClose = () => {
    setViewAlbumData(null);
    setViewOpen(false);
  };
  useEffect(() => {
    if (loading) {
      setTimeout(() => {
        setLoading(false);
      }, 500);
    }
  }, [loading]);
  useEffect(() => {
    setLoading(true);
  }, [approvalRes]);
  return (
    <Card key={approvalRes.id} hoverable={true} className={styles.lastCard} loading={loading}>
      <span className={styles.rbt}>审批类别：{approvalRes.approvalFullName}</span>
      <Descriptions column={1} className={styles.descriptions} colon={false}>
        <Descriptions.Item label="发起人：">
          {approvalRes.initUserRealName + ` (${approvalRes.initUserName})`}
        </Descriptions.Item>
        <Descriptions.Item label="审批事项：">
          {expanded ? (
            <div dangerouslySetInnerHTML={{ __html: approvalRes.approvalName }}></div>
          ) : (
            <Paragraph
              rootClassName="paragraphBox"
              ellipsis={{
                rows: 1,
                expandable: 'collapsible',
                expanded: expanded,
                onExpand: (_, info) => setExpanded(info.expanded),
                symbol: expanded ? (
                  <span className={styles.symbolSpan}>收起</span>
                ) : (
                  <span className={styles.symbolSpan}>展开</span>
                ),
              }}
            >
              {parserHtmlToString(approvalRes.approvalName)}
            </Paragraph>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="审批事实及理由：">
          {expanded1 ? (
            <div dangerouslySetInnerHTML={{ __html: approvalRes.content }}></div>
          ) : (
            <Paragraph
              rootClassName="paragraphBox"
              ellipsis={{
                rows: 1,
                expandable: 'collapsible',
                expanded: expanded1,
                onExpand: (_, info) => setExpanded1(info.expanded),
                symbol: expanded1 ? (
                  <span className={styles.symbolSpan}>收起</span>
                ) : (
                  <span className={styles.symbolSpan}>展开</span>
                ),
              }}
            >
              {parserHtmlToString(approvalRes.content)}
            </Paragraph>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="审批附件：">
          <span
            onClick={() => {
              openPreviewAlbum(approvalRes);
            }}
          >
            {approvalRes.bizExt}
          </span>
        </Descriptions.Item>
        <Descriptions.Item label="申请时间：">{approvalRes.createTime}</Descriptions.Item>
      </Descriptions>
      {viewOpen && viewAlbumData && (
        <PreviewAlbum
          albumsType={'approval'}
          approvalData={viewAlbumData.approvalData}
          albumsName={viewAlbumData.albumsName}
          albumsId={viewAlbumData.albumsId}
          albumsCategorySingle={viewAlbumData.albumsCategorySingle}
          onClose={viewClose}
        />
      )}
    </Card>
  );
};

export default Component;
