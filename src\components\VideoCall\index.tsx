import { sendEmailForInside } from '@/api/mail/chat';
import { AddressList } from '@/api/mail/chat/mailModels';
import callOffTb from '@/assets/images/mail/callOff.png';
import cameraOff from '@/assets/images/mail/cameraOff.png';
import cameraOn from '@/assets/images/mail/cameraOn.png';
import microphoneOff from '@/assets/images/mail/microphoneOff.png';
import microphoneOn from '@/assets/images/mail/microphoneOn.png';
import speakerOff from '@/assets/images/mail/speakerOff.png';
import speakerOn from '@/assets/images/mail/speakerOn.png';
import useUserStore from '@/store/useUserStore';
import { getWidth } from '@/utils/common';
import getMediaUrl from '@/utils/getMediaUrl';
import useWebRTC from '@/webRTC/useWebRTC';
import { Timeout } from 'ahooks/lib/useRequest/src/types';
import { Flex, message, Modal } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useAudioVideoCallGlobalStore from '../AudioVideoCallGlobal/useAudioVideoCallGlobalStore';
import styles from './index.module.less';
interface Props {
  open: boolean;
  title: string;
  onCancel: () => void;
  addressData?:
    | AddressList[]
    | {
        addressee: string;
        mobile: string;
        realName: string;
        addresseeStatus: number;
        addresseeName: string;
        userId: string;
        avatar: string;
      }[];
  isCalled?: boolean;
  roomId?: string;
  sourceType: 1 | 2;
  CallBackParams?: {
    callRoomId: string;
    bizMsgId: string;
  }; // 是否回拨
}
const Component = ({ open, title, onCancel, addressData, isCalled, roomId, sourceType,CallBackParams }: Props) => {
  const [setIsBusyline] = useAudioVideoCallGlobalStore((state) => [state.setIsBusyline]);
  const userInfo = useUserStore((state) => state.userInfo);
  const [duration, setDuration] = useState<number>(0);
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const intervalRef = useRef<Timeout>();
  const timers = useRef<Timeout | null>(null); // 30秒倒计时挂断
  const localVideoElement = useRef<HTMLVideoElement>(null);
  const remoteVideoElement = useRef<HTMLVideoElement>(null);
  const {
    callRoomId,
    call,
    answer,
    switchCamera,
    switchMicrophone,
    switchLoudspeaker,
    Mic,
    camera,
    muted,
    isCallOff,
    callOff,
    connectionType,
  } = useWebRTC({
    callType: 1, //呼叫类型
    localVideo: localVideoElement.current,
    remoteVideo: remoteVideoElement.current,
    sourceType,
  });
  useEffect(() => {
    if (connectionType === 'Connectioning') {
      startTimer();
    }
  }, [connectionType]);
  useEffect(() => {
    if (isCallOff) {
      onCancel();
    }
  }, [isCallOff]);
  const startTimer = () => {
    intervalRef.current = setInterval(() => {
      setDuration((prev) => prev + 1);
    }, 1000);
  };
  const stopTimer = () => {
    clearInterval(intervalRef.current);
    setIsRunning(false);
  };
  const formatTime = () => {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const secs = duration % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  // 设置弹框标题
  const callTitle = useMemo(() => {
    console.log(addressData);
    let tempTitle: string = '';
    if (sourceType === 1) {
      tempTitle = '视频通话正文输入';
    } else if (addressData) {
      tempTitle = '视频通话' + ' ' + addressData[0].realName || addressData[0].addressee;
    }
    return tempTitle;
  }, [addressData, sourceType]);
  // 设置显示邮箱还是用户名
  const contentTitle = useMemo(() => {
    let tempTitle: string = '';
    if (sourceType === 1 && addressData) {
      tempTitle = addressData[0].addressee;
    } else if (sourceType === 2 && addressData) {
      tempTitle = addressData[0].realName || addressData[0].addressee;
    }
    return tempTitle;
  }, [addressData, sourceType]);
  const handleSubmit = useCallback(() => {
    console.log(callRoomId)
    const obj = {
      addressList: addressData as AddressList[],
      senderId: userInfo?.id as string,
      senderAddress: userInfo?.email as string,
      senderName: userInfo?.realName as string,
      mailChatAttachVOList: [],
      id: '',
      mailTitle: '视频通话邮件',
      mailContent: '视频未接通或已拒绝，30秒未接听时挂断',
      confidentialityStatus: 0,
      shareFlag: 0,
      mediaFlag: '2',
      contentType: 6,
      addressGroupList: [],
      copyAddressList: [],
      copyAddressGroupList: [],
      secretAddressList: [],
      roomId:callRoomId.current
    };
    sendEmailForInside(obj).then((res) => {
      console.log('res', res);
    });
  }, [callRoomId.current]);
  useEffect(() => {
    console.log(addressData);
    setIsBusyline(true);
    if (!isCalled && addressData) {
      call(addressData[0].userId || '',CallBackParams);
      timers.current = setTimeout(() => {
        message.info('对方暂无人接听！');
        callOff();
        onCancel();
        if (sourceType === 1) {
          handleSubmit();
        }
      }, 25_000);
    } else {
      answer(roomId || '');
    }
    return () => {
      setIsBusyline(false);
      stopTimer();
      if (timers.current) {
        clearTimeout(timers.current);
        timers.current = null;
      }
    };
  }, []);
  useEffect(() => {
    console.log(addressData);
    if (timers.current && connectionType !== 'wait') {
      clearTimeout(timers.current);
      timers.current = null;
    }
  }, [connectionType]);
  return (
    <Modal
      title={null}
      open={open}
      centered
      closable={false}
      footer={null}
      keyboard={false}
      maskClosable={false}
      width={getWidth(462)}
      mask={false}
      className={styles.recordContentBox}
    >
      <Flex justify={'space-between'} align={'center'} className={styles.header}>
        <span className={styles.headerSpan}>{callTitle}</span>
      </Flex>
      <Flex vertical justify={'space-between'} className={styles.content}>
        <div className={styles.videoContainer}>
          <span className={styles.duration} hidden={connectionType !== 'Connectioning'}>
            {formatTime()}
          </span>
          <div className={styles.minVideoElement} hidden={!camera}>
            <video ref={localVideoElement} autoPlay disablePictureInPicture controls={false} />
          </div>
          <video
            className={styles.fullVideoElement}
            hidden={connectionType !== 'Connectioning'}
            ref={remoteVideoElement}
            autoPlay
            disablePictureInPicture
            muted={false}
            controls={false}
          />
        </div>
        {connectionType === 'Connectioning' ? null : (
          <div className={styles.top}>
            <img
              src={
                addressData && addressData[0]?.avatar
                  ? getMediaUrl(addressData && addressData[0]?.avatar)
                  : `${window.location.origin}/avatar.png`
              }
              className={styles.addressImg}
            />
            <span className={styles.address}>{contentTitle}</span>
          </div>
        )}

        <div className={styles.bot}>
          <div className={styles.waitAccepted} hidden={connectionType === 'Connectioning'}>
            {connectionType === 'accept' ? '连接中...' : '等待对方接受邀请...'}
          </div>
          <div>
            <div onClick={switchMicrophone}>
              <img src={Mic ? microphoneOn : microphoneOff} className={styles.float} />
              <span className={styles.flsp}>麦克风已{Mic ? '开' : '关'}</span>
            </div>
            <div onClick={switchLoudspeaker}>
              <img src={muted ? speakerOn : speakerOff} className={styles.float} />
              <span className={styles.flsp}>扬声器已{muted ? '开' : '关'}</span>
            </div>
            <div onClick={switchCamera}>
              <img src={camera ? cameraOn : cameraOff} className={styles.float} />
              <span className={styles.flsp}>摄像头已{camera ? '开' : '关'}</span>
            </div>
          </div>
          <div>
            <div onClick={callOff}>
              <img src={callOffTb} className={styles.float} />
            </div>
          </div>
        </div>
      </Flex>
    </Modal>
  );
};

export default Component;
