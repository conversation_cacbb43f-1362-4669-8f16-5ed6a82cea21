import { Cascader } from 'antd';
import { CalendarOutlined } from '@ant-design/icons';
import { useState, forwardRef, useImperativeHandle } from 'react';
import { years, months, getDays, hours, minutes } from '@/components/Filters/TimeFilter/utils';
import styles from './index.module.less';
import dayjs from 'dayjs';

interface Props {
  onChange?: (type: string) => void;
}

const isNotBefore = (next: (string | number)[], prev: (string | number)[]) => {
  for (let i = 0; i < next.length; i++) {
    if (Number(next[i]) < Number(prev[i])) {
      return false;
    } else if (Number(next[i]) > Number(prev[i])) {
      return true;
    }
  }
  return true;
};

const Component = forwardRef(({ onChange }: Props, ref) => {
  useImperativeHandle(ref, () => ({
    validate: () => {
      if (!value.length) {
        setStatus('error');
      }
    },
  }));

  const [options, setOptions] = useState<any[]>(() => {
    const year = dayjs().year();
    return [
      ...years
        .filter((item) => {
          return isNotBefore([item.value], [year]);
        })
        .map((item) => {
          return { ...item, isLeaf: false };
        }),
    ];
  });
  const [value, setValue] = useState<any[]>([]);
  const [status, setStatus] = useState<undefined | 'error'>();
  const loadData = (selectedOptions: any[]) => {
    const [y, m, d, h] = selectedOptions;
    const [cy, cm, cd, ch, cf] = dayjs().format('YYYY-MM-DD-HH-mm').split('-');
    const targetOption = selectedOptions[selectedOptions.length - 1];
    if (selectedOptions.length === 1) {
      targetOption.children = [
        ...months
          .filter((item) => {
            return isNotBefore([y.value, item.value], [cy, cm]);
          })
          .map((item) => {
            return { ...item, isLeaf: false };
          }),
      ];
    } else if (selectedOptions.length === 2) {
      targetOption.children = [
        ...getDays(y.value, m.value)
          .filter((item) => {
            return isNotBefore([y.value, m.value, item.value], [cy, cm, cd]);
          })
          .map((item) => {
            return { ...item, isLeaf: false };
          }),
      ];
    } else if (selectedOptions.length === 3) {
      targetOption.children = [
        ...hours
          .filter((item) => {
            return isNotBefore([y.value, m.value, d.value, item.value], [cy, cm, cd, ch]);
          })
          .map((item) => {
            return { ...item, isLeaf: false };
          }),
      ];
    } else if (selectedOptions.length === 4) {
      targetOption.children = [
        ...minutes
          .filter((item) => {
            return isNotBefore(
              [y.value, m.value, d.value, h.value, item.value],
              [cy, cm, cd, ch, cf],
            );
          })
          .map((item) => {
            return { ...item, isLeaf: true };
          }),
      ];
    }
    setOptions([...options]);
  };

  return (
    <Cascader
      status={status}
      popupClassName={styles.cascaderPopup}
      placeholder="选择时间"
      options={options}
      suffixIcon={<CalendarOutlined />}
      placement="bottomRight"
      value={value}
      loadData={loadData}
      displayRender={(label, selectedOptions: any) => {
        const [y, m, d, h, f] = selectedOptions;
        return `${y.value}-${m.value}-${d.value} ${h.value}:${f.value}`;
      }}
      onChange={(value: any) => {
        let formatValue = '';
        if (value) {
          setValue(value);
          const [y, m, d, h, f] = value;
          formatValue = `${y}-${m}-${d} ${h}:${f}:00`;
        } else {
          setValue([]);
        }
        if (onChange) {
          onChange(formatValue);
        }
      }}
    />
  );
});

export default Component;
