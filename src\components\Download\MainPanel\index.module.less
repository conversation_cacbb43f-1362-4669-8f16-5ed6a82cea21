.title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: start;
  .left {
    width: 45%;
    display: flex;
    height: 32px;
    line-height: 32px;
    align-items: baseline;

    :global {
      span:last-child {
        color: initial;
        margin-left: 10px;
        font-size: 14px;
        b {
          color: var(--ant-color-error);
          font-weight: normal;
        }
      }
    }
  }
  .right {
    display: flex;
    align-items: start;
    :global {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }
}

.toolBar {
  padding: 16px;
}

.list {
  margin: 0 8px;

  :global {
    .ant-table-tbody-virtual .ant-table-tbody-virtual-scrollbar-horizontal {
      display: none;
    }
  }
}

.selectLocalRoot {
  padding: 32px 16px;
  display: flex;
  justify-content: space-between;

  .item {
    display: flex;
    align-items: center;
  }
}

.createFolder {
  padding: 32px 16px;

  .item {
    display: flex;
    justify-content: center;
    align-items: center;

    &:first-child {
      margin-bottom: 24px;
    }
  }
}

.selectLocationModalFooter {
  display: flex;
  justify-content: space-between;
  padding: 0 16px;
}

.selectLocation {
  margin: 8px;
  // border: 1px solid var(--ant-color-primary);
  height: 380px;
  overflow: auto;
  :global {
    .ant-tree-switcher {
      margin-top: 3px;
    }
    :where(.css-dev-only-do-not-override-j10akn).ant-tree
      .ant-tree-switcher:not(.ant-tree-switcher-noop):hover {
      background-color: rgba(0, 0, 0, 0);
    }
    .ant-tree-treenode {
      align-items: center;
    }
  }

  .treeLine {
    display: flex;
    align-items: center;
    height: 32px;
    > div {
      flex: 1;
    }
    .titleBox {
      display: flex;
      align-items: center;
      justify-content: start;
      .text {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      span {
        width: 32px;
        height: 18px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 4px;
        border-radius: 4px;
      }
      .normal {
        border: 1px solid #3d5afe;
        color: #3d5afe;
      }
      .actived {
        background-color: #7986cb;
        color: #fff;
      }
    }
    .rightBtn {
      text-align: right;
      button {
        margin: 0 4px;
        font-size: 12px;
        height: auto;
        padding: 1px 3px;
      }
    }
    button {
      color: #3d5afe;
      border: 1px #3d5afe solid;
    }
  }
}

.success {
  color: var(--ant-color-success);
}

.uploading {
  color: var(--ant-color-primary);
}

.waiting,
.pause {
}

.error,
.abort {
  color: var(--ant-color-error);
}

.progress {
  border-top: 1px solid #efefef;
  text-align: center;
  padding: 8px;
}
