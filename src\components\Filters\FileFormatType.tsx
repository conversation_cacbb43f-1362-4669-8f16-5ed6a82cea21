import useLibraryStore from '@/store/useLibraryStore';
import MenuFilter from './MenuFilter';
// 人事管理
export const employeeFileType = [
  'library_file_type_doc',
  'library_file_type_pic',
  'library_file_type_ppt',
  'library_file_type_table',
];
// 重新分类
export const resetClassifyFileType = [
  'library_file_type_doc',
  'library_file_type_pic',
  'library_file_type_ppt',
  'library_file_type_table',
  'library_file_type_audio',
  'library_file_type_video',
];

const Component = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  close,
  clearFilters,
  config,
}: any) => {
  const [fileFormatTypeList] = useLibraryStore((state) => [state.fileFormatTypeList]);
  const toggleItem = (key: string, isSubmit: boolean) => {
    if (selectedKeys.includes(key)) {
      setSelectedKeys(selectedKeys.filter((k: string) => k !== key));
    } else {
      setSelectedKeys([...selectedKeys, key]);
    }
    isSubmit && submit('ok');
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
      submit('ok');
    } else if (type === 'close') {
      close();
      config?.setFileFormatTypeOpen(false);
    } else if (type === 'ok') {
      confirm({ closeDropdown: false });
      // close();
    }
  };

  const typeProps = {
    employee: employeeFileType,
    resetClassifyFile: resetClassifyFileType,
  };

  return (
    <MenuFilter
      items={fileFormatTypeList}
      selectedKeys={selectedKeys}
      onSelect={toggleItem}
      onSubmit={submit}
    />
  );
};

export default Component;
