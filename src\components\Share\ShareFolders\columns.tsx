import { getWidth } from '@/utils/common';

export const getColumns = () => {
  return [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: getWidth(4.5),
      render: (value: any, row: any, index: number) => index + 1,
    },
    {
      title: '共享群名称',
      dataIndex: 'groupName',
      key: 'groupName',
      ellipsis: true,
      width: getWidth(19.5),
      render: (value: any, row: any) => `${row.groupName}(${row.countNum})`,
    },
    {
      title: '共享群成员',
      dataIndex: 'memberList',
      key: 'memberList',
      ellipsis: true,
      width: getWidth(30.0),
      render: (value: any, row: any) => {
        return row.memberList.map((item: any) => item.contactName).join('，');
      },
    },
    {
      title: '操作',
      dataIndex: 'actions',
      key: 'actions',
      width: getWidth(11.0),
    },
  ];
};
