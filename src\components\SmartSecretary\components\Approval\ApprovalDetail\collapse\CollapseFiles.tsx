import type { FilePreviewAPI } from '@/components/FliePreview';
import FliePreview from '@/components/FliePreview';
import useUserStore from '@/store/useUserStore';
import { Flex, Tooltip } from 'antd';
import { FC, useEffect, useRef, useState } from 'react';
import styles from './Collapse.module.less';
interface CollapseProps {
  items: any[];
  expand?: boolean;
  safeLevel?: number;
  revokeFlag?: number;
  type?: string;
}

const CollapseFiles: FC<CollapseProps> = ({
  items = [],
  safeLevel = 0,
  expand = false,
  type = 'other',
}) => {
  const filePreviewRef = useRef<FilePreviewAPI>();
  const [expanded, setExpanded] = useState<boolean>(false);
  const userInfo = useUserStore((state) => state.userInfo);

  useEffect(() => {
    setExpanded(expand);
  }, [expand]);

  const preview = (index: number) => {
    filePreviewRef.current?.open({
      id: items[index].libraryId,
      title: items[index].fileName,
      fileSize: items[index].fileSize,
      visitPath: type === 'edit' ? items[index].visitPath : items[index].fileUrl,
      fileType: items[index].fileType,
      fileFormatType: items[index].fileFormatType,
      safeLevel: safeLevel,
      filePath: items[index].fileUrl,
      userId: userInfo?.id || '',
      source: 0,
    });
  };

  const rederItems = () => {
    if (items.length <= 1) {
      return items.map((item, index) => (
        <div key={index} className={styles.conDiv}>
          <Tooltip title={item.fileName}>
            <span onClick={() => preview(index)} className={`${styles.hoverSpan} ${styles.voSpan}`}>
              附件{index + 1}：{item.fileName}
            </span>
          </Tooltip>
        </div>
      ));
    }
    if (expanded) {
      return (
        <>
          <Flex vertical>
            {items.map((item, index) => (
              <div key={index} className={styles.conDiv}>
                <Tooltip title={item.fileName}>
                  <span
                    onClick={() => preview(index)}
                    className={`${styles.hoverSpan} ${styles.voSpan}`}
                  >
                    附件{index + 1}：{item.fileName}
                  </span>
                </Tooltip>
                {index < items.length - 1 ? <br /> : renderButtons()}
              </div>
            ))}
          </Flex>
        </>
      );
    }
    return (
      <div key={0} className={styles.conDiv}>
        <Tooltip title={items[0].fileName}>
          <span onClick={() => preview(0)} className={`${styles.hoverSpan} ${styles.voSpan}`}>
            附件1：{items[0].fileName}
          </span>
        </Tooltip>
        {renderButtons()}
      </div>
    );
  };

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  const renderButtons = () => {
    return (
      <>
        {expanded ? (
          <span className={styles.cosSpan} onClick={toggleExpanded}>
            折叠
          </span>
        ) : (
          <span className={styles.cosSpan} onClick={toggleExpanded}>
            展开{items.length - 1}
          </span>
        )}
      </>
    );
  };

  return (
    <>
      {rederItems()}
      <FliePreview ref={filePreviewRef} zIndex={1000} />
    </>
  );
};

export default CollapseFiles;
