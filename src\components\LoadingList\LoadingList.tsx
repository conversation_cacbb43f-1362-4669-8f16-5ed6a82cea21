import { useMemo, useState } from 'react';
import { createPortal } from 'react-dom';
import LoadingModal from './LoadingModal';
import styles from './index.module.less';
import type { LoadingItem } from './useCtxStore';
import useCtxStore from './useCtxStore';

const Component = () => {
  const [setCurrentLoadingItem, loadingList] = useCtxStore((state) => [
    state.setCurrentLoadingItem,
    state.loadingList,
  ]);
  const [isExpend, setIsExpend] = useState(false);
  const classes = useMemo(() => {
    return isExpend ? `${styles.loadingList} ${styles.expend}` : styles.loadingList;
  }, [isExpend]);
  const height = useMemo(() => {
    const count = loadingList.filter((item: LoadingItem) => item.itemVisible);
    return isExpend ? `${3.25 * count.length}rem` : '3.25rem';
  }, [isExpend, loadingList]);
  const VisibleList = useMemo(() => {
    const list = loadingList.filter((item: LoadingItem) => item.itemVisible);
    const height = isExpend ? `${3.25 * list.length}rem` : '3.25rem';
    return { list, height };
  }, [isExpend, loadingList]);
  const expend = () => {
    setIsExpend((prev) => !prev);
  };
  const openLoadingModal = (item: any) => {
    setCurrentLoadingItem({
      module: item.module,
      type: item.type,
      modalVisible: true,
      itemVisible: true,
    });
  };

  return (
    <>
      {loadingList.length > 0 &&
        createPortal(
          <div className={classes} style={{ height: VisibleList.height }}>
            {VisibleList.list.map((item: LoadingItem, index: number) => {
              return (
                <div
                  key={`${item.module}_${item.type}`}
                  className={item.removed ? `${styles.item} ${styles.removed}` : styles.item}
                >
                  <span className={styles.icon}>
                    <img src={item.icon} />
                  </span>
                  <span
                    className={styles.text}
                    title={`${item.typeText}(${item.moduleText})`}
                    onClick={() => openLoadingModal(item)}
                  >
                    {item.typeText}({item.moduleText})
                  </span>
                  <span className={styles.status}>{item.status}</span>
                  {index === 0 && VisibleList.list.length > 1 && (
                    <span className={styles.dropdown} onClick={expend}>
                      展开
                    </span>
                  )}
                </div>
              );
            })}
          </div>,
          document.body,
        )}
      <LoadingModal />
    </>
  );
};

export default Component;
