import MultiModal from '@/components/MultiModal';
import { Button } from 'antd';
import { useContext, useEffect, useMemo } from 'react';
import Context from '../Context';
import FilesList from './FilesList';
import Filter from './Filter';
import styles from './index.module.less';

const Component = () => {
  const { useMainPanelCtxStore, useSelectFilesCtxStore, config } = useContext(Context);
  const [selectFilesOpen, setSelectFilesOpen, list, selectedList] = useSelectFilesCtxStore!(
    (state) => [state.selectFilesOpen, state.setSelectFilesOpen, state.list, state.selectedList],
  );
  const [setSelectedFileList, selectedFileMap, setSelectedFileMap] = useMainPanelCtxStore!(
    (state) => [state.setSelectedFileList, state.selectedFileMap, state.setSelectedFileMap],
  );
  const okEnabled = useMemo(() => {
    return selectedList.some((item: any) => !selectedFileMap[item.id]);
  }, [selectedList, selectedFileMap]);
  const cancel = () => {
    setSelectFilesOpen(false);
  };
  const ok = () => {
    const map = { ...selectedFileMap };
    selectedList.forEach((item: any) => {
      if (!map[item.id]) {
        map[item.id] = item;
      }
    });
    setSelectedFileMap(map);
    setSelectedFileList(Object.values(map));
  };
  useEffect(() => {
    if (selectFilesOpen === false) {
      useSelectFilesCtxStore!.reset();
    }
  }, [selectFilesOpen]);
  const title = (
    <div className={styles.title}>
      <span>选择{config?.typeName || '文件'}</span>
      <span>
        共计 <b>{list.length}</b> 个，已选择 <b>{selectedList.length}</b> 个
      </span>
    </div>
  );

  return (
    <MultiModal
      layoutGroup={`share_${config?.module}`}
      layoutClassName="modalLeft"
      className={styles.modalSelectFiles}
      destroyOnClose={true}
      title={title}
      open={selectFilesOpen}
      onCancel={cancel}
      footer={[
        <Button key="submit" type="primary" disabled={!okEnabled} onClick={ok}>
          确认选择
        </Button>,
      ]}
    >
      <Filter />
      <FilesList />
    </MultiModal>
  );
};

export default Component;
