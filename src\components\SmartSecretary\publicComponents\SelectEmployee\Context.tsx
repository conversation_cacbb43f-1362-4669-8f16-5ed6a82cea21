import { createContext, FC, ReactNode, useContext, useState } from 'react';

interface SelectEmployeeContextType {
  selectList: any[];
  setSelectList: React.Dispatch<React.SetStateAction<any[]>>;
}

const SelectEmployeeContext = createContext<SelectEmployeeContextType | undefined>(undefined);
interface DetailPageProviderProps extends SelectEmployeeContextType {
  children: ReactNode;
}
export const SelectEmployeeProvider: FC<DetailPageProviderProps> = ({ children }) => {
  const [selectList, setSelectList] = useState<any[]>([]);
  return (
    <SelectEmployeeContext.Provider value={{ selectList, setSelectList }}>
      {children}
    </SelectEmployeeContext.Provider>
  );
};

export const useSelectEmployeeContext = () => {
  const context = useContext(SelectEmployeeContext);
  if (context === undefined) {
    throw new Error('error');
  }
  return context;
};
