import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create, createJSONStorage, devtools } from '@/store';

interface State {
  location: string;
}
interface SetState {
  setLocation: Setter;
}
const useCtxStore = autoUseShallow<State, SetState>(
  create(
    devtools(
      autoCreateSetters<State>({
        location: '',
      }),
      { name: 'from-module', storage: createJSONStorage(() => sessionStorage) },
    ),
  ),
);

export default useCtxStore;
