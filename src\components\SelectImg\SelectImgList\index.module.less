.albumContainOut {
  position: relative;
  width: 100%;
  min-height: 100%;
  background-color: #cdcdcd;
  font-family: PingFang SC;
}
.net-album-contain {
  justify-content: center;
  align-items: center;
  min-height: 560px;
}
.albumContain {
  display: flex;
  flex-wrap: wrap;
  width: 1120px;
  min-height: 626px;
  margin: 0 auto;
  justify-content: center;
  .albumItem {
    position: relative;
    width: 330px;
    height: 289px;
    margin: 24px 12px;
    cursor: pointer;
    background: #ffffff;
    box-shadow: 0px 0px 20px 12px rgba(0, 0, 0, 0.1);
    .albumName {
      font-family: Helvetica;
      font-size: 32px;
      line-height: 32px;
      font-weight: 600;
      color: #3e3b23;
      text-align: center;
      margin: 18px 0 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 0 40px;
    }

    .imageWrap {
      position: absolute;
      bottom: 0;
      height: 200px;
      text-align: center;
      .image {
        width: 370px; // 136
        height: 200px; // 120
        object-fit: cover;
        border-top: 1px solid #eeeeeed1;
      }
    }
    .imageRight {
      position: absolute;
      left: 0;
      top: 0;
      width: 40px;
      height: 100%;
      background-image: url('@/assets/images/magicAlbum/bg_right.png');
      background-repeat: no-repeat;
      background-size: cover;
    }
    .timeRow {
      text-align: center;
      font-size: 16px;
      color: #9c9564;
    }
    .shareTag {
      position: absolute;
      right: 0;
      top: 0;
      width: 64px;
      height: 35px;
      line-height: 35px;
      text-align: center;
      background: #fff3e0;
      color: #ff9100;
      font-size: 22px;
    }
  }
}

.pager {
  width: 100%;
  font-size: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px 24px;
  .shareBtnBottom {
    margin-right: 16px;
  }
  .blueFont {
    color: #3d5afe;
  }
}
