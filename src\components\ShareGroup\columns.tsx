import { COLUMN } from '@/const';
import CollapseRow from '@/pages/Mail/components/Collapse/CollapseRow';
import { getWidth } from '@/utils/common';

export const groupCol = [
  COLUMN.INDEX(),
  {
    title: '共享群名称',
    dataIndex: 'groupName',
    key: 'groupName',
    width: getWidth(150),
    ellipsis: true,
  },
  {
    title: '共享群成员',
    dataIndex: 'member',
    key: 'member',
    render: (_: any, record: any) => {
      return <CollapseRow items={record.member}></CollapseRow>;
    },
  },
];
