import YbAvatar from '@/components/Avatar';
import useAppStore from '@/store/useAppStore';
import { getWidth } from '@/utils/common';
import getMediaUrl from '@/utils/getMediaUrl';
import { Avatar, Button, List, Skeleton } from 'antd';
import { FC, useCallback } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import styles from './index.module.less';

interface EmployeeListProps {
  employees: any[]; // 通讯录数组
  selectList: any[]; // 当前选中的数据
  setSelectList: React.Dispatch<React.SetStateAction<any[]>>;
  searchValue?: string; // 搜索关键词
  mode: 'single' | 'multiple';
  disabledList?: any[]; // 禁用的联系人列表[{ username: '5802666698-43',... }]
}
const EmployeeList: FC<EmployeeListProps> = ({
  employees,
  selectList,
  setSelectList,
  searchValue,
  mode,
  disabledList = [],
}) => {
  const { channel } = useAppStore((state: any) => state);

  // 获得头像
  const getAvatar = useCallback(
    (data: any) => (
      <YbAvatar
        userName={data.contactName || '母账户'}
        Avatar={data.avatar ? getMediaUrl(data.avatar) : ''}
        isUser={data.type === 1}
      />
    ),
    [],
  );
  /**
   * 该数据是否被选中
   * @param key
   * @param value
   * @returns true为被选中
   */
  const isSelected = useCallback(
    (id: string) => selectList.some((item) => item.id === id),
    [selectList],
  );
  // 选择事件
  const onChoice = useCallback(
    (item: any) => () => {
      const isSel = isSelected(item.id);
      // 单选
      if (mode === 'single') {
        setSelectList(isSel ? [] : [item]);
      } else {
        // 多选
        setSelectList((prev) =>
          isSel ? prev.filter((prevItem) => prevItem.id !== item.id) : [...prev, item],
        );
      }
    },
    [setSelectList, isSelected],
  );

  // 判断该联系人是否禁用选择
  const isDisabled = (username: string) => {
    const ids = disabledList.map((item: any) => item.username);
    return ids.includes(username);
  };

  const renderButton = (username: string, selected: boolean, onClick: () => void) =>
    selected ? (
      <Button type="primary" onClick={onClick} disabled={isDisabled(username)}>
        选定
      </Button>
    ) : (
      <Button className={styles.defaultBtn} onClick={onClick} disabled={isDisabled(username)}>
        {mode === 'single' ? '单选' : '多选'}
      </Button>
    );

  return (
    <div className={styles.emplopeeList}>
      <div
        id="emplopeeList"
        className={styles.scrollableDiv}
        style={{ height: window.innerHeight - getWidth(channel === 'web' ? 428 : 360) }}
      >
        <InfiniteScroll
          dataLength={employees.length} // 已加载的数据长度
          next={() => {}} // 加载更多数据的函数
          hasMore={false} // 是否还有更多数据
          loader={false}
          scrollableTarget="emplopeeList"
          scrollThreshold={100}
        >
          <List
            dataSource={employees}
            renderItem={(item: any) =>
              item.contactName.includes(searchValue) && (
                <List.Item key={item.id}>
                  <Skeleton avatar title={false} loading={item.loading} active>
                    <List.Item.Meta
                      avatar={<Avatar src={getAvatar(item)} shape="square" size={getWidth(50)} />}
                      title={<span className={styles.title}>{item.contactName || '母账户'}</span>}
                      description={
                        <span className={styles.username}>
                          {item.username || item.groupLeaderUserName}
                        </span>
                      }
                    />
                    {renderButton(item.username, isSelected(item.id), onChoice(item))}
                  </Skeleton>
                </List.Item>
              )
            }
          />
        </InfiniteScroll>
      </div>
    </div>
  );
};
export default EmployeeList;
