.modalOtherWrap {
  :global {
    .ant-modal-content {
      padding: 0 12px;
      // height: 219px;
    }
    .ant-modal-body {
      width: 100%;
      // height: 219px;
      padding-bottom: 30px;
    }
    .ant-divider {
      margin-top: 0;
      margin-bottom: 12px;
    }
  }
  .headWrap {
    padding: 17px 0;
    .leftHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        color: #4d70fe;
        margin-right: 12px;
      }
      .desc {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        .num {
          color: #e53935;
        }
      }
    }
  }
  .close {
    border-radius: 4px;
    /* 自动布局 */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    background: #fff;
    color: #3d5afe;
    border: 1px solid #3d5afe;
    cursor: pointer;
  }
  .modalContent {
    margin-bottom: 24px;
    text-align: center;
  }
  .btnsWrap {
    display: flex;
    justify-content: center;
    .btnAdjust {
      margin-right: 10px;
    }
  }
}
