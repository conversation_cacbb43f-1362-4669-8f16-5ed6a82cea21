import MyAvatar from '@/components/Avatar';
import { getWidth } from '@/utils/common';
import getMediaUrl from '@/utils/getMediaUrl';
import { Avatar, List } from 'antd';
import { FC } from 'react';
import styles from './index.module.less';

const ChatItem: FC<any> = ({ data, goChatDetail }) => {
  // 获得头像
  const getAvatar = () => {
    const apiAvatar = '';
    const myAvatar = (
      <MyAvatar userName={''} Avatar={apiAvatar !== '' ? getMediaUrl(apiAvatar) : ''} isUser />
    );
    return myAvatar;
  };
  return (
    <List.Item
      onClick={() => {
        goChatDetail(data);
      }}
    >
      <List.Item.Meta
        avatar={
          <div className={styles.leftContent}>
            <Avatar shape="square" size={getWidth(48)} src={getAvatar()} />
          </div>
        }
        title={
          <div className={styles.titleLine}>
            <div className={styles.title}>测试5802666698-98</div>
            <div className={styles.time}>19:40</div>
          </div>
        }
        description={
          <div className={styles.contentLine}>
            <div className={styles.content}>
              <div>这是假数据呢</div>
            </div>
            <div className={styles.cirleUnread}>1</div>
          </div>
        }
      />
    </List.Item>
  );
};

export default ChatItem;
