import { libraryCallbackDownload } from '@/api/library';
import { useLoadingListStore } from '@/components/LoadingList';
import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create } from '@/store';
import { cwsEmitter } from '@/store/useCppWebSocketStore';
import useFromModuleStore from '@/store/useFromModuleStore';
import useUserStore from '@/store/useUserStore';
import { add, divide, subtract } from 'lodash';

export interface State {
  mainPanelOpen: boolean;
  selectedFileList: Record<string, any>[];
  selectedDataBankUrl: any;
  selectedFileMap: Record<string, any>;
  isDownloading: boolean;
  downloadFileList: Record<string, any>[];
  downloadFileMap: Record<string, any>;
  errorFiles: number;
  successFiles: number;
  abortFiles: number;
  filesSize: number;
  downloadedFilesSize: number;
  loadingStatus: 'init' | 'waiting' | 'loading' | 'pause' | 'complete';
  location: string;
}
export interface SetState {
  setMainPanelOpen: Setter;
  setSelectedFileList: Setter;
  setSelectedFileMap: Setter;
  setSelectedDataBankUrl: Setter;
  setIsDownloading: Setter;
  setDownloadFileList: Setter;
  setDownloadFileMap: Setter;
  setErrorFiles: Setter;
  setSuccessFiles: Setter;
  setAbortFiles: Setter;
  setFilesSize: Setter;
  setDownloadedFilesSize: Setter;
  setLoadingStatus: Setter;
  setLocation: Setter;
}
interface Config {
  module: string;
  type: string;
  [prop: string]: any;
}

export const getDownloadType = ({ module, type }: Config) => {
  if (module === 'contextMenu' && type === 'download') {
    return 1; // 1-右键一键下载
  }
  if (module === 'library' && type === 'download') {
    return 2; // 2-文库大全-一键下载
  }
  if (module === 'library' && type === 'downloadAll') {
    return 3; // 3-文库大全-一键全部下载
  }
  if (module === 'dataBank' && type === 'downloadAll') {
    return 4; // 4-数据银行-一键全部下载
  }
  if (module === 'audioPlay' && type === 'download') {
    return 5; // 5-音频播放器-一键下载
  }
  if (module === 'videoPlay' && type === 'download') {
    return 6; // 6-视频播放器-一键下载
  }
};

export const setPercent = (loadingSize: number, totalSize: number, loadingStatus: string) => {
  if (totalSize === 0) {
    return loadingStatus === 'complete' ? 100 : 0;
  } else {
    return Math.floor(divide(loadingSize, totalSize) * 100);
  }
};

export const formatQueryData = (queryData: any, { pageNumber, pageSize, config }: any) => {
  const { userInfo } = useUserStore.getState();
  const { fromModule, fromModuleQuery } = useFromModuleStore.getState();
  const result: any = {};
  const add = (key: any, value: any) => {
    result[key] = result[key] || [];
    if (value) {
      result[key].push(value);
    }
  };
  
  queryData.forEach((item: any) => {
    result.pageNo = pageNumber.current;
    result.pageSize = pageSize;
    result.userId = userInfo?.id;
    result.tenantId = '';
    result.sortField = 'createTime';
    result.sortMethod = 'desc';
    result.groupId = fromModuleQuery?.groupId;
    result.deleted = fromModule === 'recycle' ? 1 : 0;
    result.dataBankFlag = config.module === 'dataBank' ? 'dataBankAllDownload' : undefined;
    result.shareFlag=1;
    add('realName', item.realName);
    add('userName', item.userName);
    add('title', item.title);
    add('content', item.content);
    add('startTime', item.startTime);
    add('endTime', item.endTime);
    add('fileFormatType', item?.fileFormatTypeList?.join(','));
    add('source', item?.sourceList?.join(','));
    add('safeLevel', item?.saveLevelList?.join(','));
    add('querySizeList', { minFileSize: item.minFileSize, maxFileSize: item.maxFileSize });    
  });
  return result;
};

const createUseCtxStore = function (config: Config) {
  const { clientType } = useUserStore.getState();
  const useCtxStore = autoUseShallow<State, SetState>(
    create(
      autoCreateSetters<State>({
        mainPanelOpen: false,
        selectedDataBankUrl: [],
        selectedFileList: [],
        selectedFileMap: {},
        isDownloading: false,
        downloadFileList: [],
        downloadFileMap: {},
        errorFiles: 0,
        successFiles: 0,
        abortFiles: 0,
        filesSize: 0,
        downloadedFilesSize: 0,
        loadingStatus: 'init',
        location: '',
      }),
    ),
  );

  useCtxStore.subscribe((state, prev) => {
    if (
      (state.abortFiles > prev.abortFiles ||
        state.errorFiles > prev.errorFiles ||
        state.successFiles > prev.successFiles) &&
      state.abortFiles + state.errorFiles + state.successFiles === state.downloadFileList.length
    ) {
      state.setLoadingStatus('complete');
      state.setDownloadedFilesSize(state.filesSize);
      const data = Object.values(state.downloadFileMap)
        .filter((file: any) => file.status === 'success')
        .map((file: any) => {
          return {
            userName: useUserStore.getState().userInfo?.username,
            source: 10,
            filePath: file.filePath,
            fileType: file.fileType,
            fileSize: file.fileSize,
            downloadType: getDownloadType(config),
            safeLevel: file.safeLevel,
            fileName: file.title,
            clientId: clientType,
          };
        });
      if (data.length) {
        let i = 0;
        let j = 500;
        let sliceData = data.slice(i, j);
        while (sliceData.length) {
          libraryCallbackDownload(sliceData);
          i = j;
          j += 500;
          sliceData = data.slice(i, j);
        }
      }
    }
  });

  useCtxStore.subscribe((state, prev) => {
    if (
      state.downloadedFilesSize > prev.downloadedFilesSize ||
      state.loadingStatus !== prev.loadingStatus
    ) {
      const { setLoadingList } = useLoadingListStore.getState();
      let status: string;
      switch (state.loadingStatus) {
        case 'init':
        case 'waiting':
          status = '等待中';
          break;
        case 'loading':
          status = `${setPercent(state.downloadedFilesSize, state.filesSize, state.loadingStatus)}%`;
          break;
        case 'pause':
          status = '暂停下载';
          break;
        case 'complete':
          status = '已完成';
          break;
      }
      setLoadingList((prev: any) => {
        return [
          ...prev.map((item: any) => {
            if (item.module === config.module && item.type === config.type) {
              item.status = status;
            }
            return { ...item };
          }),
        ];
      });
    }
  });

  cwsEmitter.on(`${config.module}-${config.type}.confirmDownload`, (response: any) => {
    const {
      downloadFileMap,
      setErrorFiles,
      setSuccessFiles,
      setDownloadedFilesSize,
      setDownloadFileList,
      setSelectedFileList,
    } = useCtxStore.getState();
    const items = response.data;
    let errorFiles = 0;
    let successFiles = 0;
    let filesSize = 0;
    items.forEach((item: any) => {
      const { nowsize, totalsize, remoteFilepath, status ,fileId} = JSON.parse(item);
      const file = downloadFileMap[fileId];
      if (!file || file.status === 'error' || file.status === 'success') {
        return;
      }
      if (status === 'error') {
        file.status = 'error';
        file._status = '';
        errorFiles += 1;
      } else {
        if (status === 'success') {
          filesSize += subtract(file.fileSize, file.downloadedSize);
          file.status = 'success';
          file._status = '';
          file.downloadedSize = file.fileSize;
          successFiles += 1;
        } else if (status === 'loading') {
          filesSize += subtract(nowsize, file.downloadedSize);
          file.status = 'loading';
          file.downloadedSize = Math.min(nowsize, file.fileSize);
        }
      }
    });
    setErrorFiles((prev: any) => prev + errorFiles);
    setSuccessFiles((prev: any) => prev + successFiles);
    setDownloadedFilesSize((prev: number) => add(prev, filesSize));
    setDownloadFileList((prev: any) => [...prev]);
    setSelectedFileList((prev: any) => [...prev]);
  });

  return useCtxStore;
};

export default createUseCtxStore;
