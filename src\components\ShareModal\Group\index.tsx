import MultiModal from '@/components/MultiModal';
import ShareGroup, { SharedGroupModel } from '@/components/ShareGroup';
import { useEffect, useRef } from 'react';

interface Props {
  open: boolean;
  onCancel: () => void;
  onAdd: (val: SharedGroupModel[]) => void; // 数据回传方法
  selectDataList?: any[];
}

const Component = ({ open, onCancel, onAdd, selectDataList }: Props) => {
  const groupRef = useRef<any>(null);

  useEffect(() => {
    if (open) {
      groupRef.current?.setSelectData(selectDataList);
    } else {
      groupRef.current?.resetSelectData();
    }
  }, [open]);

  return (
    <MultiModal
      layoutClassName="modalLeft"
      title={null}
      open={open}
      onCancel={onCancel}
      footer={null}
      closable={false}
    >
      <ShareGroup
        ref={groupRef}
        onClose={onCancel}
        notAbsolute
        onAdd={onAdd}
        selectDataList={[]}
      ></ShareGroup>
      ;
    </MultiModal>
  );
};

export default Component;
