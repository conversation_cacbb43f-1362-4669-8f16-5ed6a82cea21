import { Button } from 'antd';
import { FC, useContext, useEffect } from 'react';
import RootContext from './context';
import styles from './index.module.less';
interface ModalTitleProps {
  closeModal: (isLeft: boolean) => void;
  isLeft?: boolean;
}

const ModalTitle: FC<ModalTitleProps> = ({ isLeft = true, closeModal }) => {
  const { data, titleConfig, total } = useContext(RootContext);
  const selectedLength = () => {
    return data.filter((itm: any) => itm.isSelected).length;
  };
  useEffect(() => {}, []);
  return (
    <>
      <div className={styles.headWrap}>
        {isLeft && (
          <div className={styles.leftHeader}>
            <div className={styles.title}>{titleConfig.leftTitle}</div>
            <div className={styles.desc}>
              共计<span className={styles.num}> {total || data.length} </span>个，已选择
              <span className={styles.num}> {selectedLength()} </span>个
            </div>
          </div>
        )}
        {!isLeft && (
          <div className={styles.leftHeader}>
            <div className={styles.title}>{titleConfig.rightTitle}</div>
          </div>
        )}
        <Button type="primary" className={styles.close} onClick={() => closeModal(isLeft)}>
          关闭
        </Button>
      </div>
    </>
  );
};

export default ModalTitle;
