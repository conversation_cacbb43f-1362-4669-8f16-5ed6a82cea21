import AudioType from '@/assets/images/todolist/audioType.png';
import OtherType from '@/assets/images/todolist/otherType.png';
import PicType from '@/assets/images/todolist/picType.png';
import VideoType from '@/assets/images/todolist/videoType.png';
import YbWordType from '@/assets/images/todolist/ybwordType.png';

import FilePreview, { FilePreviewAPI } from '@/components/FliePreview';
import useAppStore from '@/store/useAppStore';
import { formatFileSize, getWidth } from '@/utils/common';
import { Avatar, Button, List, Tag } from 'antd';
import { FC, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import styles from './index.module.less';

const SecretaryLibraryListItem: FC<{
  data: any;
  index: number;
  filePreview: (item: any) => void;
}> = ({ data, index, filePreview }) => {
  const [loading, setLoading] = useState(false);

  const getAvatarAndClass: any = (fileFormatType: string) => {
    switch (fileFormatType) {
      case 'library_file_type_pic':
        return {
          avatar: PicType,
          titleClass: styles.tag_green,
        };
      case 'library_file_type_audio':
        return {
          avatar: AudioType,
          titleClass: styles.tag_purple1,
        };
      case 'library_file_type_video':
        return {
          avatar: VideoType,
          titleClass: styles.tag_purple2,
        };
      case 'library_file_type_doc':
      case 'library_file_type_yb':
        return {
          avatar: YbWordType,
          titleClass: styles.tag_blue,
        };
      default:
        return {
          avatar: OtherType,
          titleClass: styles.tag_yellow,
        };
    }
  };

  return (
    <List.Item>
      <List.Item.Meta
        avatar={
          <div className={styles.leftContent}>
            <Avatar
              shape="square"
              size={getWidth(48)}
              src={getAvatarAndClass(data.fileFormatType).avatar}
            />
            <div
              className={`${styles.typeTitle} ${getAvatarAndClass(data.fileFormatType).titleClass}`}
            >
              {data.fileFormatTypeName}
            </div>
          </div>
        }
        title={
          <div className={styles.titleLine}>
            <div className={styles.title}>{data.title}</div>
            <div className={styles.rightActions}>
              <Button
                className={styles.btnPrimary}
                onClick={() => {
                  filePreview(data);
                }}
                type="primary"
                size="small"
                loading={loading}
              >
                浏览
              </Button>
            </div>
          </div>
        }
        description={
          <div className={styles.contentLine}>
            <div className={styles.content}>
              <div>
                {data.shareRealName
                  ? `${data.shareRealName} (${data.shareUserName})`
                  : `${data.realName} (${data.userName})`}
              </div>
              <div>
                <Tag color="geekblue" className={styles.tag}>
                  {formatFileSize(data.fileSize)}
                </Tag>
              </div>
            </div>
            <div className={styles.createTime}> {data.createTime || '-'}</div>
          </div>
        }
      />
    </List.Item>
  );
};

const SecretaryLibraryList: FC<any> = ({ list, hasMore, fetchListNext, minusHeigh = 0 }) => {
  const { channel } = useAppStore((state: any) => state);
  const filePreviewRef = useRef<FilePreviewAPI>();
  // const heightConfig = { web : 525, other: 457}
  const preview = (row: any) => {
    filePreviewRef.current?.open(row);
  };
  return (
    <div
      id="SmartSecretaryListId"
      className={styles.scrollableDiv}
      style={{
        height:
          window.innerHeight - getWidth(channel === 'web' ? 525 - minusHeigh : 457 - minusHeigh),
      }}
    >
      <InfiniteScroll
        dataLength={list.length} // 已加载的数据长度
        next={fetchListNext} // 加载更多数据的函数
        hasMore={hasMore} // 是否还有更多数据
        loader={false}
        scrollableTarget="SmartSecretaryListId"
        scrollThreshold="100px"
      >
        <List
          className={styles.libraryList}
          itemLayout="horizontal"
          dataSource={list}
          renderItem={(item, index) => (
            <SecretaryLibraryListItem data={item} index={index} filePreview={preview} />
          )}
        ></List>
      </InfiniteScroll>
      <FilePreview ref={filePreviewRef} />
    </div>
  );
};
export default SecretaryLibraryList;
