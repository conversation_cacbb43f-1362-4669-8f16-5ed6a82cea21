.recordContentBox {
  width: auto;
  .header {
    height: 72px;
    padding: 18.47px 19.7px;
    background: rgba(255, 255, 255, 0.5);
    border-width: 1px 1px 0px 1px;
    border-style: solid;
    border-radius: 10px 10px 0 0;
    border-color: #ffffff;

    .header-span {
      // width: 160px;
      height: 35px;
      font-size: 20px;
      font-weight: 500;
      line-height: 35px;
      opacity: 1;
      word-break: keep-all;
      white-space: nowrap ;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .header-button {
      // width: 52px;
      height: 32px;
      border-radius: 4px;
      opacity: 1;
      padding: 5px 12px;
      background: #ffffff;
      border: 1px solid #3d5afe;
    }
  }
  .content {
    height: 850px;
    width: 462px;
    border-radius: 0 0 10px 10px;
    background-color: rgba(0, 0, 0, 0.6);
    position: relative;
    .videoContainer {
      width: 100%;
      height: 100%;
      .duration {
        position: absolute;
        top: 20px;
        right: 50%;
        width: 108px;
        height: 36px;
        opacity: 1;
        font-size: 26px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0em;
        color: #fff;
        transform: translateX(50%);
      }
      .minVideoElement {
        width: 90px;
        height: 160px;
        border-radius: 10px;
        position: absolute;
        top: 20px;
        right: 10px;
        overflow: hidden;
        video {
          width: 90px;
          height: 160px;
          object-fit: cover;
        }
      }
      .fullVideoElement {
        width: 462px;
        height: 850px;
        object-fit: cover;
      }
    }
    .top {
      width: 100%;
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 127px;
      text-align: center;
      gap: 10px;
      .addressImg {
        width: 150px;
        height: 150px;
      }
      .address {
        width: 80%;
        height: 31px;
        opacity: 1;
        font-size: 22px;
        font-weight: 500;
        line-height: normal;
        text-align: center;
        letter-spacing: 0px;
        color: rgba(255, 255, 255, 0.65);
      }
    }
    .waitAccepted {
      height: 31px;
      opacity: 1;
      font-size: 22px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0px;
      color: rgba(255, 255, 255, 0.65);
      margin-bottom: 20px;
      text-align: center;
    }
    .bot {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 20px;
      padding: 0 89px;
      cursor: pointer;
      text-align: center;
      & > div:nth-of-type(2) {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
      }
      & > div:last-of-type {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .float {
        width: 64px;
        height: 64px;
        margin-bottom: 10px;
      }
      .flsp {
        height: 20px;
        opacity: 1;
        font-size: 14px;
        letter-spacing: 0em;
        color: #ffffff;
      }
    }
  }
  :global {
    .ant-modal-content {
      padding: 0;
      margin: 0;
      opacity: 1;
      border: 1px solid #ffffff;
    }
  }
}
