export interface queryPageParamsType {
  userId?: string;
  pageNo: number;
  pageSize: number;
}

export interface createAlbumParamsType {
  albumsId?: string;
  albumsTemplateId: number;
  albumsName: string;
  photosReqVOList: Array<any>;
}

export interface getAlbumDetailParamsType {
  albumId: any;
  albumsCategorySingle: any;
  tag?: number;
  deleted?: number | string;
  timestamp?: number | string;
}

export interface getTemplateParamsType {
  albumsTemplateId: number;
}

export interface backgroundTextParamsType {
  wordNo?: number;
  backgroundId?: number;
  albumsId: number | string;
  backTitle: string;
  content: string;
  ybContent: string | undefined;
  voiceType: string;
  speed: string;
}

export interface photosdeleteParamsType {
  albumsId: string;
  photoId: string;
  userId: string;
  userName: string;
}

export interface photosdeleteBatchType {
  photosBakSaveReqVOList: Array<photosdeleteParamsType>;
}
/**
 * 分享列表参数
 */
export interface SharePageParams {
  pageSize: number;
  pageNo: number;
  userNames?: string[];
  albumsNames?: string[];
  userId?: string;
  albumsIds?: any[];
}
/**
 * 分享参数
 */
export interface ShareParams {
  albumIds: string[]; // 分享相册ID集合
  senderUserId: string; // 	分享者的用户ID
  recipientUserIds?: string[]; // 被分享用户ID集合
  recipientGroupIds?: string[]; // 被分享群ID集合,示例值([ 1234, 1235 ])
  shareId?: string; // 分享ID
}
