.FilePreview {
  .title {
    width: calc(100% - 70px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      width: 45%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .middle {
      width: 30%;
      display: flex;
      align-items: center;
    }
    .right {
      display: flex;
      align-items: start;
      :global {
        .ant-btn {
          margin-left: 8px;
        }
      }
    }
  }
}

.content {
  height: 740px;
  overflow: hidden;
}

.popover {
  :global {
    .ant-popover-title {
      font-size: 16px;
    }
    .ant-popover-inner-content {
      & > div {
        width: 190px;
        p:first-child {
          b {
            color: var(--ant-color-error);
            font-weight: normal;
          }
        }
        p:last-child {
          margin-top: 8px;
          display: flex;
          justify-content: space-between;
          .ant-btn.ant-btn-link {
            padding-left: 0;
            padding-right: 0;
          }
        }
      }
    }
  }
}
.safeLevel {
  white-space: nowrap;
  font-size: 12px;
  margin-left: 8px;
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
  border-radius: 0px 4px 0px 4px;
  color: var(--ant-color-error);
  border: 1px solid var(--ant-color-error);
  cursor: pointer;
}
.expireTime {
  white-space: nowrap;
  font-size: 14px;
  margin-left: 8px;
  b {
    color: var(--ant-color-error);
  }
}
.spin {
  width: 100%;
  height: 100%;
  :global {
    .ant-spin.ant-spin-spinning {
      max-height: initial;
    }
  }
}
.imgOcrBox {
  height: 70vh;
  .imgOcrItem {
    flex: 1;
    overflow: auto;
  }
  .hiddenContent {
    display: none;
    white-space: pre-line;
  }
}
