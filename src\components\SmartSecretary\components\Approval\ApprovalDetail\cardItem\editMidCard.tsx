import { creatProval } from '@/api/Approval/module';
import { parserHtmlToString } from '@/utils/parser';
import { Button, Card, Descriptions, Typography } from 'antd';
import { FC, useEffect, useState } from 'react';
import { getApprovalName } from '..';
import CollapseFiles from '../collapse/CollapseFiles';
import styles from './index.module.less';
const { Paragraph } = Typography;

interface Props {
  approvalData: creatProval;
  labelName: string;
}

const Component: FC<Props> = ({ approvalData, labelName }) => {
  const [expanded, setExpanded] = useState<boolean>(false);
  const [expanded1, setExpanded1] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  useEffect(() => {
    if (loading) {
      setTimeout(() => {
        setLoading(false);
      }, 500);
    }
  }, [loading]);
  return (
    <Card key={9999} hoverable={true} className={styles.editMidCard} loading={loading}>
      <Descriptions column={1} className={styles.descriptions} colon={false}>
        <Descriptions.Item label={`${labelName}:`}>
          {approvalData.approvalUserRealName || approvalData.approvalUserName}
        </Descriptions.Item>
        <Descriptions.Item label="审批事项：">
          <div className="flex-1">
            {expanded ? (
              <div dangerouslySetInnerHTML={{ __html: getApprovalName(approvalData) }}></div>
            ) : (
              <Paragraph
                rootClassName="paragraphBox"
                ellipsis={{
                  rows: 1,
                  expandable: 'collapsible',
                  expanded: expanded,
                  onExpand: (_, info) => setExpanded(info.expanded),
                  symbol: expanded ? (
                    <span className={styles.symbolSpan}>收起</span>
                  ) : (
                    <span className={styles.symbolSpan}>展开</span>
                  ),
                }}
              >
                {parserHtmlToString(getApprovalName(approvalData))}
              </Paragraph>
            )}
          </div>
          <Button type="primary" ghost size="small">
            修改
          </Button>
        </Descriptions.Item>
        <Descriptions.Item label="中间审批内容：">
          {expanded1 ? (
            <div dangerouslySetInnerHTML={{ __html: approvalData.middleContent }}></div>
          ) : (
            <Paragraph
              rootClassName="paragraphBox"
              ellipsis={{
                rows: 1,
                expandable: 'collapsible',
                expanded: expanded1,
                onExpand: (_, info) => setExpanded1(info.expanded),
                symbol: expanded1 ? (
                  <span className={styles.symbolSpan}>收起</span>
                ) : (
                  <span className={styles.symbolSpan}>展开</span>
                ),
              }}
            >
              {parserHtmlToString(approvalData.middleContent)}
            </Paragraph>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="下级审批人：">
          <div className="flex-1">{approvalData.toUserRealName || approvalData.toUserName}</div>
          <Button type="primary" ghost size="small">
            选择
          </Button>
        </Descriptions.Item>
        <Descriptions.Item label="添加附件：">
          <div className="flex-1">
            <CollapseFiles items={approvalData.fileReqVOList} type="edit"></CollapseFiles>
          </div>
          <Button type="primary" ghost size="small">
            添加
          </Button>
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default Component;
