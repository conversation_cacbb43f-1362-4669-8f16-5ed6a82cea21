import request from '../index';

// 用户真实姓名认证
export const updateRealName = (data: any) => {
  return request.post({
    url: '/web-api/account/manage/updateRealName',
    data,
  });
};
// 修改用户工作手机号
export const updateMobile = (data: any) => {
  return request.post({
    url: '/web-api/account/manage/updateMobile',
    data,
  });
};
// 修改子账户
export const updateMember = (data: any) => {
  return request.post({
    url: '/web-api/account/manage/updateMember',
    data,
  });
};
// 修改用户备用手机号
export const updateBackupMobile = (data: any) => {
  return request.post({
    url: '/web-api/account/manage/updateBackupMobile',
    data,
  });
};
// 获得用户子账号分页列表
export const getMemberPage = (data: any) => {
  return request.get({
    url: '/web-api/account/manage/getMemberPage',
    params: data,
  });
};
// 获得新增子账户的账户和密码
export const getMemberNameAndPassword = (data: any) => {
  return request.get({
    url: '/web-api/account/manage/getMemberNameAndPassword',
    params: data,
  });
};
// 新增子账户
export const createMember = (data: any) => {
  return request.post({
    url: '/web-api/account/manage/createMember',
    data,
  });
};
// 获得用户配置手机历史
export const getMobileConfig = (data: any) => {
  return request.get({
    url: '/web-api/account/manage/getMobileConfig',
    params: data,
  });
};

//子账户记录删除
export const deleteMember = (data: any) => {
  return request.put({
    url: '/web-api/account/manage/deleteMember',
    data,
  });
};
// 用户配置手机历史删除
export const deleteMobileConfig = (data: any) => {
  return request.put({
    url: '/web-api/account/manage/deleteMobileConfig',
    data,
  });
};

// 获得用户账单分页
export const getBillPage = (data: any) => {
  return request.get({
    url: '/web-api/bill/page',
    params: data,
  });
};

// 获得指定应用的开启的支付渠道编码列表
export const getPayMethodList = (data: any) => {
  return request.get({
    url: '/web-api/pay/channel/get-enable-code-list',
    params: data,
  });
};

// 获得支付订单信息-轮询
export const getPayOrder = (data: any) => {
  return request.get({
    url: '/web-api/pay/order/get',
    params: data,
  });
};

// 提交支付订单-生成二维码
export const submitOrder = (data: any) => {
  return request.post({
    url: '/web-api/pay/order/submit',
    data,
  });
};

// 获得字典类型的分页列表
export const getDictByType = (data: any) => {
  return request.get({
    url: '/admin-api/infra/dict-data/page',
    params: data,
  });
};

//  获取用户配置手机历史记录-结果查询
export const getMobileConfigNew = (data: any) => {
  return request.post({
    url: '/web-api/account/manage/getMobileConfigNew',
    data,
  });
};

//  获取用户子账户分页列表-结果查询
export const getNewMemberPageNew = (data: any) => {
  return request.post({
    url: '/web-api/account/manage/getNewMemberPageNew',
    data,
  });
};

// 删除账单
export const deletedBill = (data: any) => {
  return request.delete({
    url: '/web-api/bill/deleted',
    data,
  });
};

// 获得用户收费模式
export const getChargeMode = (data: any) => {
  return request.get({
    url: '/web-api/bill/user/chargeMode/get',
    params: data,
  });
};

// 修改用户收费模式
export const editChargeMode = (data: any) => {
  return request.put({
    url: '/web-api/bill/user/chargeMode',
    data,
  });
};

// 获得用户存储信息分页
export const getStoragePage = (data: any) => {
  return request.get({
    url: '/web-api/bill/user/storage/page',
    params: data,
  });
};

// 获得用户当前未支付账单
export const getUnpayBill = (data: any) => {
  return request.get({
    url: '/web-api/bill/unpaid',
    params: data,
  });
};

// 获得用户当前未支付账单
export const listByDictType = (data: any) => {
  return request.get({
    url: '/admin-api/infra/dict-data/listByDictType',
    params: data,
  });
};

// 验证用户能否使用增值服务
export const verifyArrearage = (data: any) => {
  return request.get({
    url: '/web-api/bill/user/chargeMode/verifyArrearage',
    params: data,
  });
};
