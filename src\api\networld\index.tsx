import request from '../index';
import { CheckReq, DeleteReq, PageListReq, ShareReq } from './netModels';

// 获取网络世界收藏总数
export const getNetWorldTotal = () => {
  return request.post({
    url: '/web-api/networld/website/getNetWorldTotal',
  });
};

// 网站分页查询
export const getWebsiteData = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/website/getPage',
    data,
  });
};

// 网站分页查询（共享天地）
export const queryNetWorldByPage = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/website/getShareWebsitePage',
    data,
  });
};

// 网站分页查询（数据银行）
export const getDataBankWebsiteData = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/website/getDataPage',
    data,
  });
};

// 网站分页查询（回收站）
export const getRecycleWebsiteData = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/website/getRecyclePage',
    data,
  });
};

// 网站推荐分页查询
export const getWebsiteConfigList = (params: PageListReq) => {
  return request.get({
    url: `/web-api/networld/website/config/getPage`,
    params,
  });
};

// 网页分页查询
export const getWebpageData = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/webpage/getPage',
    data,
  });
};

// 网页分页查询（数据银行）
export const getDataBankWebpageData = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/webpage/getDataPage',
    data,
  });
};

// 网页分页查询（回收站）
export const getRecycleWebpageData = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/webpage/getRecyclePage',
    data,
  });
};

// 网页分页查询（共享天地）
export const getShareWebpageData = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/webpage/getShareWebPage',
    data,
  });
};

// 网页推荐分页查询
export const getWebpageConfigList = (params: PageListReq) => {
  return request.get({
    url: '/web-api/networld/webpage/config/getPage',
    params,
  });
};

/**
 * 获取网页分享列表
 */
export const getSharePageList = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/webpage/getSharePageList',
    data,
  });
};

/**
 * 获取网站分享列表
 */
export const getShareWebsitePage = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/website/getShareWebsitePage',
    data,
  });
};

/**
 * 分享网站
 */
export const shareWebsite = (data: ShareReq) => {
  return request.post({
    url: '/web-api/networld/website/shareWebsite',
    data,
  });
};

/**
 * 分享网页
 */
export const shareWebpage = (data: ShareReq) => {
  return request.post({
    url: '/web-api/networld/webpage/shareWebpage',
    data,
  });
};

// 新增网站点击率
export const checkWebsite = (data: CheckReq) => {
  return request.post({
    url: `/web-api/networld/website/checkWebsite?websiteId=${data.websiteId}`,
  });
};

// 新增网页点击率
export const checkWebpage = (data: CheckReq) => {
  return request.post({
    url: `/web-api/networld/webpage/checkWebpage?webpageId=${data.webpageId}`,
  });
};

/**
 * 获得白名单分页
 */
export const getWhiteWebsiteList = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/white-website/list',
    data,
  });
};
// 删除网站
export const delWebsite = (data: any) => {
  return request.put({
    url: '/web-api/networld/website/delWebsite',
    data,
  });
};
// 清空网站未读数
export const checkWebsiteRead = () => {
  return request.get({
    url: '/web-api/networld/website/checkWebsiteRead',
  });
};

// 数据银行删除、回收站取消删除、永久删除网站
export const operateWebsite = (data: any) => {
  return request.post({
    url: '/web-api/networld/website/operateWebsite',
    data,
  });
};

/**
 * 获得黑名单分页(包括初始，黑名单，白名单，分类外黑名单)
 */
export const getBlackWebsiteAll = (data: PageListReq) => {
  return request.post({
    url: '/web-api/networld/black-website/all',
    data,
  });
};
// 删除网页
export const delWebpage = (data: any) => {
  return request.put({
    url: '/web-api/networld/webpage/delWebpage',
    data,
  });
};
// 数据银行删除、回收站取消删除、永久删除网页
export const operateWebpage = (data: any) => {
  return request.post({
    url: '/web-api/networld/webpage/operateWebpage',
    data,
  });
};

// 黑名单删除
export const deleteBlack = (data: DeleteReq[]) => {
  return request.post({
    url: '/web-api/networld/black-website/create',
    data,
  });
};

// 获得黑名单分类列表
export const getBlackCate = (data: any) => {
  return request.post({
    url: '/web-api/networld/black-website/categories',
    data,
  });
};

// 更新黑名单分类
export const updateBlackCate = (data: any) => {
  return request.post({
    url: '/web-api/networld/black-website/update/category',
    data,
  });
};
