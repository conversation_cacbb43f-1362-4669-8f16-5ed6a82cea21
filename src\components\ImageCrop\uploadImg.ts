// 用公共路径上传头像
import { uploadByPublic } from '@/api/library';

// 将base64字符串转换为Blob对象
function dataURLtoBlob(dataurl: any) {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}
// 上传图片到文件服务器
const uploadImgApi = async (base64Image: any, bizType: string) => {
  // 创建Blob对象
  const blob = dataURLtoBlob(base64Image);
  // console.log(777777777, base64Image, blob);
  const fileSize = blob.size / 1000 + '';
  // 创建FormData并附加文件
  const formData = new FormData();
  formData.append('file', blob, `avater${+new Date()}.png`); // 'image.png'是文件名
  formData.append('bizType', bizType); // 2-魔术相册，11-通讯录，18-账户管理
  // formData.append('title', '头像');
  // formData.append('fileType', 'png');
  // formData.append('fileFormatType', 'library_file_type_pic');
  // formData.append('fileSize', fileSize);

  const res: any = await uploadByPublic({
    data: formData,
    onUploadProgress: (event: any) => {},
  }).catch(() => {
    console.log('失败了！');
    return '';
  });
  console.log('成功了！', res.data);
  return res?.data;
};

export { uploadImgApi };
