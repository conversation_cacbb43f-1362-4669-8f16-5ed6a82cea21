import {
  getSourceFilterProps,
  getFileSizeFilterProps,
  getFileFormatTypeFilterProps,
} from '@/components/Filters';
import { formatFileSize, getWidth } from '@/utils/common';

export const getColumns = (hasFilters: boolean, config: any) => {
  return [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: getWidth(4.5),
      render: (value: any, row: any, index: number) => index + 1,
    },
    {
      title: '姓名',
      dataIndex: 'shareRealName',
      key: 'shareRealName',
      width: getWidth(7.5),
      ellipsis: true,
    },
    {
      title: '文件名称',
      dataIndex: 'title',
      key: 'title',
      width: getWidth(12.0),
      ellipsis: true,
    },
    {
      title: '文件来源',
      dataIndex: 'source',
      key: 'source',
      width: getWidth(10.0),
      ...(hasFilters ? getSourceFilterProps() : {}),
      render: (value: any, row: any) => {
        return row.sourceName;
      },
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: getWidth(10.0),
      ...(hasFilters ? getFileSizeFilterProps() : {}),
      render: (value: any, row: any) => {
        return formatFileSize(row.fileSize);
      },
    },
    {
      title: '文件格式',
      dataIndex: 'fileFormatType',
      key: 'fileFormatType',
      width: getWidth(10.0),
      ellipsis: true,
      ...(hasFilters && !(config.module === 'audioPlay' || config.module === 'videoPlay')
        ? getFileFormatTypeFilterProps()
        : {}),
      render: (value: any, row: any) => {
        return row.fileFormatTypeName;
      },
    },
    {
      title: '操作',
      dataIndex: 'actions',
      key: 'actions',
      width: getWidth(11.0),
    },
  ];
};
