import MultiModal from '@/components/MultiModal';
import useCppWebSocketStore from '@/store/useCppWebSocketStore';
import { Button } from 'antd';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import Context from '../Context';
import SaveModal from '../SaveModal';
import styles from './index.module.less';
import Progress from './Progress';
import SelectFiles from './SelectFiles';
import ToolBar from './ToolBar';

interface Props {
  onMinimize?: () => void;
  onCancel?: () => void;
}

const Component = ({ onMinimize, onCancel }: Props) => {
  const progressRef = useRef<{
    upload: () => void;
    pause: () => void;
    resume: () => void;
    abort: () => void;
  }>();
  const [response] = useCppWebSocketStore((state) => [state.response]);
  const [saveIsVisible, setSaveIsVisible] = useState(false);
  const [cppsocketstatedie, setCppsocketstatedie] = useState(false);
  const { useMainPanelCtxStore, useSelectFilesCtxStore, config } = useContext(Context);
  const [mainPanelOpen, selectedFileList, setSelectedFileList, loadingStatus, setLoadingStatus] =
    useMainPanelCtxStore!((state) => [
      state.mainPanelOpen,
      state.selectedFileList,
      state.setSelectedFileList,
      state.loadingStatus,
      state.setLoadingStatus,
    ]);
  const [setSelectFilesOpen] = useSelectFilesCtxStore!((state) => [state.setSelectFilesOpen]);
  const okEnabled = useMemo(() => {
    return selectedFileList.length > 0 && loadingStatus === 'init';
  }, [selectedFileList, loadingStatus]);
  const pauseEnabled = useMemo(() => {
    return loadingStatus === 'loading';
  }, [loadingStatus]);
  const resumeEnabled = useMemo(() => {
    return loadingStatus === 'pause';
  }, [loadingStatus]);
  const abortEnabled = useMemo(() => {
    return loadingStatus === 'loading' || loadingStatus === 'pause';
  }, [loadingStatus]);
  const minimizeEnabled = useMemo(() => {
    return loadingStatus === 'loading' || loadingStatus === 'pause';
  }, [loadingStatus]);
  const closeEnabled = useMemo(() => {
    console.log(loadingStatus);
    return loadingStatus === 'init' || loadingStatus === 'complete' || cppsocketstatedie;
  }, [loadingStatus, cppsocketstatedie]);
  const toolBarClick = (type: string) => {
    if (type === 'selectFiles') {
      setSelectFilesOpen(true);
    }
  };
  const minimize = () => {
    if (onMinimize) {
      onMinimize();
    }
  };
  const cancel = () => {
    if (onCancel) {
      useMainPanelCtxStore!.reset();
      onCancel();
    }
  };
  const upload = () => {
    setSelectFilesOpen(false);
    progressRef.current?.upload();
  };
  const pause = () => {
    progressRef.current?.pause();
  };
  const resume = () => {
    progressRef.current?.resume();
  };
  const abort = () => {
    progressRef.current?.abort();
  };
  useEffect(() => {
    if (response.method === 'checkcppsocket' && response.data === 'cppsocketdie') {
      setCppsocketstatedie(true);
      setLoadingStatus('complete');
      setSelectedFileList((selectedFileList: any) => {
        console.log(selectedFileList);
        return selectedFileList.map((item: any) => {
          if (item.status !== 'success') {
            item.status = 'error';
          }
          return item;
        });
      });
    }
  }, [response, setSelectedFileList, setLoadingStatus]);
  useEffect(() => {
    return () => {
      cancel();
      abort();
    };
  }, []);
  const title = (
    <div className={styles.title}>
      <div className={styles.left}>
        <span>重新分类保存</span>
        <span>
          已选择 <b>{selectedFileList.length}</b> 个
        </span>
      </div>
      <div className={styles.right}>
        <Button type="primary" ghost disabled={!closeEnabled} onClick={cancel}>
          关闭
        </Button>
      </div>
    </div>
  );

  return (
    <MultiModal
      layoutGroup={`upload_${config?.module}_${config?.type}`}
      layoutClassName="modalRight"
      destroyOnClose={true}
      closable={false}
      title={title}
      open={mainPanelOpen}
      footer={[
        <Button
          key="upload"
          type="primary"
          loading={loadingStatus === 'waiting'}
          disabled={!okEnabled}
          onClick={() => setSaveIsVisible(true)}
        >
          确认重新分类保存
        </Button>,
        // <Button key="pause" type="primary" ghost disabled={!pauseEnabled} onClick={pause}>
        //   暂停上传
        // </Button>,
        // <Button key="resume" type="primary" ghost disabled={!resumeEnabled} onClick={resume}>
        //   恢复上传
        // </Button>,
        // <Button key="abort" type="primary" ghost danger disabled={!abortEnabled} onClick={abort}>
        //   终止上传
        // </Button>,
      ]}
    >
      <ToolBar onClick={toolBarClick} />
      <SelectFiles />
      <Progress ref={progressRef} />
      <SaveModal
        cancel={onCancel}
        openModal={saveIsVisible}
        onClose={() => setSaveIsVisible(false)}
      ></SaveModal>
    </MultiModal>
  );
};

export default Component;
