import type { TableProps } from 'antd';
import { Table, message } from 'antd';
import { useEffect, useState } from 'react';
import styles from './ReclassifySave.module.less';
import SaveModal from './components/SaveModal/index';
interface DataType {
  key?: string;
  name?: string;
  title?: string;
  acount?: string;
  flieFormat?: string;
  fileSource?: string;
  level?: string;
  fileSize?: string;
  time?: string;
}
interface ModalProps {
  onCloseModal?: () => void; // 关闭弹框回调方法
  showFileChoose?: () => void; // 显示选择文件
  data?: DataType[];
}
const FileUpload = (ModalProps: ModalProps) => {
  const [list, setList] = useState<DataType[]>([]);
  const [value, setValue] = useState('');
  const [saveIsVisible, setSaveIsVisible] = useState<boolean>(false);
  const [messageApi, contextHolder] = message.useMessage();
  // 文库大全文件上传列表表头
  const FileChooseColumns: TableProps<DataType>['columns'] = [
    {
      title: '序号',
      dataIndex: 'key',
      key: 'key',
      width: '46px',
      align: 'center',
      render: (_: any, record: DataType) => <span>{record.key}</span>,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: '48px',
      align: 'center',
      render: (_: any, record: DataType) => <span>{record.name}</span>,
    },

    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: '267px',
      align: 'left',
      ellipsis: true,
      render: (_: any, record: DataType) => <span>{record.title}</span>,
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: '93px',
      align: 'left',
      render: (_: any, record: DataType) => <span>{record.fileSource}</span>,
    },

    {
      title: '操作',
      key: 'action',
      width: '134px',
      align: 'left',
      render: (_: any, record: DataType, index: number) => (
        <div className={styles.btnsAll}>
          <span>浏览</span>
          <span
            className={styles.nomalBut}
            onClick={() => {
              list.splice(index, 1);
              setList([...list]);
            }}
          >
            取消
          </span>
        </div>
      ),
    },
  ];
  const reclassifySve = () => {
    if (ModalProps.data && ModalProps.data.length > 0) {
      setSaveIsVisible(true);
    } else {
      messageApi.warning('请选择重新分类保存文件');
    }
  };
  useEffect(() => {
    setList(list.concat(ModalProps.data || []));
  }, [ModalProps.data]);
  return (
    <>
      {contextHolder}
      <div className={styles.FlieDownload + ' ' + styles.FileUpload}>
        <div className={styles.header}>
          <div>
            <span>重新分类保存</span>
            共计<span>{list.length}</span>个
          </div>
          <div
            className={styles.closeBut}
            onClick={() => {
              ModalProps.onCloseModal && ModalProps.onCloseModal();
            }}
          >
            关闭
          </div>
        </div>
        <div className={styles.saveFileBtn}>
          <div
            onClick={() => {
              ModalProps.showFileChoose && ModalProps.showFileChoose();
            }}
          >
            选择重新分类保存文件
          </div>
        </div>
        <div className={styles.TableBox}>
          <Table
            columns={FileChooseColumns}
            scroll={{ y: 420 }}
            rowKey={'key'}
            pagination={false}
            dataSource={list}
          />
        </div>
        <div className={styles.OperateButList}>
          <div className={styles.ConfirmBtn} onClick={reclassifySve}>
            确认重新分类保存
          </div>
        </div>
        <SaveModal openModal={saveIsVisible} onClose={() => setSaveIsVisible(false)}></SaveModal>
      </div>
    </>
  );
};

export default FileUpload;
