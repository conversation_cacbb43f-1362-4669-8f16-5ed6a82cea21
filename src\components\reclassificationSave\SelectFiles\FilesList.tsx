import { formatFileSizeFilterValue } from '@/components/Filters';
import { employeeFileType } from '@/components/Filters/FileFormatType';
import { useLoadingListStore } from '@/components/LoadingList';
import Pagination from '@/components/Pagination';
import { cppbRequest } from '@/store/useCppBridgeStore';
import { cwsRequest } from '@/store/useCppWebSocketStore';
import useLibraryStore from '@/store/useLibraryStore';
import useUserStore from '@/store/useUserStore';
import { getRandom } from '@/utils/common';
import { shareError } from '@/utils/modal';
import { Button, Space, Table } from 'antd';
import CryptoJS from 'crypto-js';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import Context from '../Context';
import { getColumns } from './columns';
import styles from './index.module.less';
import { formatQueryData, formatTemporaryData } from './useCtxStore';

const Component = () => {
  const [fileFormatTypeMap, localSourceMap] = useLibraryStore((state) => [
    state.fileFormatTypeMap,
    state.localSourceMap,
  ]);
  const [userInfo] = useUserStore((state) => [state.userInfo]);
  const { useSelectFilesCtxStore, useMainPanelCtxStore, config } = useContext(Context);
  const [
    selectFilesOpen,
    setColumnsFilterData,
    queryData,
    list,
    setList,
    selectedList,
    setSelectedList,
    selectedMap,
    setSelectedMap,
    setTotal,
    total,
    queryType,
    setQueryType,
    temporaryData,
  ] = useSelectFilesCtxStore!((state) => [
    state.selectFilesOpen,
    state.setColumnsFilterData,
    state.queryData,
    state.list,
    state.setList,
    state.selectedList,
    state.setSelectedList,
    state.selectedMap,
    state.setSelectedMap,
    state.setTotal,
    state.total,
    state.queryType,
    state.setQueryType,
    state.temporaryData,
  ]);
  const [timestamp, selectedFileMap] = useMainPanelCtxStore!((state) => [
    state.timestamp,
    state.selectedFileMap,
  ]);
  const [loading, setLoading] = useState(false);
  const [pageNumber, setPageNumber] = useState({ current: 1 });
  const [pageSize, setPageSize] = useState(1000);

  const [loadedList, setLoadedList] = useState<any[]>([]);
  const didMountRef = useRef(false);
  const [currentLoadingItem] = useLoadingListStore((state) => [state.currentLoadingItem]);
  const isSelectedAll = useMemo(() => {
    for (const item of list) {
      if (!selectedMap[item.id]) {
        return true;
      }
    }
    return false;
  }, [list, selectedMap]);
  const columns = useMemo(() => {
    return getColumns(true, config!).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              {list.length === 0 && <span>操作</span>}
              {list.length > 0 && (
                <Button
                  type="primary"
                  size="small"
                  ghost={isSelectedAll}
                  onClick={() => {
                    const map: any = {};
                    list.forEach((item: any) => {
                      map[item.id] = item;
                    });
                    setSelectedMap(map);
                    setSelectedList(Object.values(map));
                  }}
                >
                  全部选择
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          let element = null;
          if (selectedMap[row.id]) {
            element = (
              <Button
                type="primary"
                size="small"
                disabled={Boolean(selectedFileMap[row.id])}
                onClick={() => {
                  const map = { ...selectedMap };
                  delete map[row.id];
                  setSelectedMap(map);
                  setSelectedList(Object.values(map));
                }}
              >
                已选
              </Button>
            );
          } else {
            element = (
              <Button
                type="primary"
                size="small"
                ghost
                onClick={() => {
                  const map = { ...selectedMap };
                  map[row.id] = row;
                  setSelectedMap(map);
                  setSelectedList(Object.values(map));
                }}
              >
                多选
              </Button>
            );
          }
          return (
            <Space>
              {element}
              <Button
                style={{ paddingLeft: 0, paddingRight: 0 }}
                type="link"
                size="small"
                onClick={() => {
                  scanBtn(row);
                }}
              >
                浏览
              </Button>
            </Space>
          );
        };
      }
      return item;
    });
  }, [list, selectedList, selectedMap, selectedFileMap]);
  //浏览
  const scanBtn = async (row: any) => {
    const response = await cppbRequest({
      module: 'desktopFile',
      id: getRandom(6), // 消息id 随机数
      method: 'openfile',
      data: {
        filePath: row.filePath,
      },
    });
    console.log('cppResponse', response);
    if (response.code !== 0) {
      shareError(response.data.status, response.data.status);
    }
  };
  const getList = () => {
    if (!loadedList.length) {
      setLoading(true);
    }
    if (queryData[0].fileFormatTypeList?.length === 0 && currentLoadingItem.module === 'employee') {
      queryData[0].fileFormatTypeList = employeeFileType;
    }
    cwsRequest({
      module: 'desktopFile',
      method: 'getAllfileList',
      data:
        queryType.current === 'current'
          ? formatTemporaryData(queryData, temporaryData, pageNumber, pageSize, config)
          : formatQueryData(queryData, { pageNumber, pageSize, config }),
    }).then((res: any) => {
      if (!loadedList.length) {
        setLoading(false);
      }
      if (res.code !== 0) {
        return;
      }
      const { contents, count } = JSON.parse(res.data);
      if (contents === null) {
        return;
      }
      const list = contents.map((item: any) => {
        console.log(timestamp);
        return {
          id: CryptoJS.MD5(item.filePath + '_' + timestamp).toString(),
          realName: userInfo?.realName,
          fileName: item.fileName,
          fileSize: item.fileSize,
          fileType: item.fileType,
          fileSource: item.fileSource,
          fileSourceName: item.fileSourceName,
          // fileSourceName: localSourceMap[item.fileSource]?.dictLabel,
          fileFormatType: item.fileFormatType,
          fileFormatName: fileFormatTypeMap[item.fileFormatType]?.dictLabel,
          filePath: item.filePath,
          createTime: item.createTime,
        };
      });
      const nextLoadedList = [...loadedList, ...list];
      setTotal(count);
      setLoadedList(list);
      setList(list);
      if (count > pageSize * pageNumber.current) {
        // setPageNumber((value) => ({ current: value.current + 1 }));
      }
    });
  };
  const change = (pagination: any, filters: any, sorter: any, extra: any) => {
    const fileSize: any = filters.fileSize ? filters.fileSize[0] : {};
    switch (extra.action) {
      case 'filter':
        setColumnsFilterData({
          fileFormatTypeList: filters.fileFormatType,
          fileSourceList: filters.fileSource,
          ...formatFileSizeFilterValue(fileSize),
        });
        setQueryType({ current: 'current' });
        break;
    }
  };
  useEffect(() => {
    if (selectFilesOpen) {
      const map = { ...selectedFileMap };
      setSelectedMap(map);
      setSelectedList(Object.values(map));
    }
  }, [selectFilesOpen, queryData, selectedFileMap]);
  useEffect(() => {
    if (!loadedList.length || loadedList.length < total) {
      getList();
    }
  }, [pageNumber]);
  useEffect(() => {
    if (didMountRef.current) {
      setTotal(0);
      setLoadedList([]);
      setList([]);
      setPageNumber({ current: 1 });
    } else {
      didMountRef.current = true;
    }
  }, [queryData]);
  useEffect(() => {
    if (didMountRef.current) {
      setTotal(0);
      setLoadedList([]);
      setList([]);
      setPageNumber({ current: 1 });
    } else {
      didMountRef.current = true;
    }
  }, [temporaryData]);
  const [forceUpdateKey, setForceUpdateKey] = useState(0);
  useEffect(() => {
    if (queryType.current !== 'current') {
      setForceUpdateKey((prevKey) => prevKey + 1);
    }
  }, [queryType]);
  return (
    <div className={styles.list}>
      <Table
        key={forceUpdateKey}
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={list}
        loading={loading}
        rowKey={'id'}
        scroll={{ y: 491 }}
        pagination={false}
        onChange={change}
      />
      <Pagination
        total={total}
        showLessItems
        showSizeChanger={false}
        current={pageNumber.current}
        customTotal={<div></div>}
        onChange={(page: number, pageSize?: number) => {
          setPageNumber({ current: page });
          setPageSize(pageSize || 10);
        }}
        customJump={false}
        pageSize={pageSize}
      />
    </div>
  );
};

export default Component;
