export interface mailListBasedQuery {
  addresseeOrSenderName?: string; // 收/发件人
  mailContent?: string; // 邮件正文
  mailTitle?: string; // 邮件标题
  attachmentName?: string; // 附件名称
}

// 列表式邮箱查询参数
export interface sendEmailReq {
  pageNo: number; // 页码
  pageSize: number; //	每页条数
  addresseeOrSenderNames?: string[]; // 收/发件人
  mailTitles?: string[]; // 标题
  mailContents?: string[]; // 正文
  attachmentNames?: string[]; // 附件名称
  userId: string; // 用户id
  mailStatus?: number; // 抄送：1 ，密送 ：2，群发：3，共享：4
  revokeFlag?: number; // 撤回状态（0:未撤，1：已撤）
  blackFlag?: number; // 黑名单状态（1:黑名单前，2：黑名单后，3：黑名单前后）
  receive: number; // 邮件性质 1：已收 2：已发 3:已发已收
  mailStatusList?: number[]; // 邮件状态列表 1:已发已读，2:已发未读，3:已收已读，4:已收未读，5:未知
  mailTypeList?: number[]; // 邮件类型列表 1:裕邦邮箱，2:qq邮箱，3:网易邮箱，4:新浪邮箱,5:189邮箱.6：139邮箱
  addresseeList?: string[]; // 收件人列表
  senderAddressList?: string[]; // 发件人列表
  confidentialityStatusList?: number[]; // 保密等级列表
  // attachmentSize?: number; // 邮件大小
  attachSizeLessThan?: number; // 附件区间最大值
  attachSizeGreaterThan?: number; // 附件区间最小值
  startTime?: string; // 发送时间开始时间
  endTime?: string; // 发送时间结束时间
  email?: string;
}

// 邮件分享-选择邮件列表查询参数
export interface sendModalEmailReq {
  pageNo: number; // 页码
  pageSize: number; //	每页条数
  userId: string; // 用户id
  addresseeOrSenderNames?: string[]; // 收/发件人
  mailTitles?: string[]; // 标题
  mailContents?: string[]; // 正文
  email?: string;
}

const fillArray = (value: number, length: number) => {
  const arr = new Array(length);
  for (let i = 0; i < length; i++) {
    arr[i] = value + i;
  }
  return arr;
};

export const routerInfo = {
  copyList: [107, 208], // 抄送
  BBCList: [106, 207], // 密送
  groupList: [108], // 群发
  shareGroup: [109, 209], // 共享

  notWithDrawnList: [115, 117, 121, 123, 124, 126, 215, 217, 221, 223, 224, 226], // 未撤回
  withDrawnList: [114, 116, 120, 122, 125, 127, 128, 214, 216, 220, 222, 225, 227, 228], // 已撤回

  blackBeforeList: [105, 205], // 黑名单前
  blackAfterList: [206], // 黑名单后

  fromList: fillArray(201, 28), // 已收
  sendList: fillArray(101, 28), // 已发

  secret: {
    // 0: [...fillArray(101, 9), ...fillArray(201, 9)], // 普通邮件
    1: [110, 111, 210, 211], // 普通转发
    2: [112, 113, 212, 213], // 普通分享
    3: [118, 119, 218, 219], // 只读分享
    4: [114, 115, 116, 117, 214, 215, 216, 217], // 定时分享
    5: [120, 121, 122, 123, 220, 221, 222, 223], // 定时只读分享
    6: [124, 125, 224, 225], // 阅后只读即撤
    7: [126, 127, 128, 226, 227, 228], // 定时阅后只读即撤
  },

  // secretList: [...fillArray(110, 19), ...fillArray(210, 19)], // 保密

  unreadList: [
    101, 110, 112, 114, 115, 118, 120, 121, 124, 126, 128, 201, 210, 212, 214, 215, 218, 220, 221,
    224, 226, 228,
  ], // 未读
  readList: [
    102, 111, 113, 116, 117, 119, 122, 123, 125, 127, 202, 211, 213, 216, 217, 219, 222, 223, 225,
    227,
  ], // 已读
  unknownList: [103, 203], // 未知
  yuBangList: [104, 204], // 裕邦邮箱
  shareList: [301], // 共享天地
};

export interface ShareEmailReq {
  shareBasicVos: any[]; // 分享邮件基本信息
  confidentialityStatus: number; // 保密等级（0:普通邮件,1:普通转发，2:普通分享，3:定时分享，4:只读分享，5:定时只读，6:阅后只读即撤，7:定时阅后只读即撤）,示例值(1)
  mailChatAttachVOList: any[]; // 附件列表
  addressList: any[]; // 收件人列表
  addressGroupList: any[]; // 收件群列表
}

export interface DeleteMailReq {
  mailIds?: any[];
  userId: string;
  deleteStyle: number;
  operateModule: number;
}
