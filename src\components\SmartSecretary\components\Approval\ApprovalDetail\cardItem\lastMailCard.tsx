import { approvalRes } from '@/api/Approval/module';
import { parserHtmlToString } from '@/utils/parser';
import { Card, Descriptions, Typography } from 'antd';
import { FC, useEffect, useState } from 'react';
// import Browse from '../Browse/Browse';
import styles from './index.module.less';
const { Paragraph } = Typography;

interface Props {
  approvalRes: approvalRes;
}

const Component: FC<Props> = ({ approvalRes }) => {
  const [expanded, setExpanded] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [mailData, setMailDate] = useState<any>(JSON.parse(approvalRes.content));
  const [isShow, setIsShow] = useState<boolean>(false);
  useEffect(() => {
    if (loading) {
      setTimeout(() => {
        setLoading(false);
      }, 500);
    }
  }, [loading]);
  useEffect(() => {
    setLoading(true);
  }, [approvalRes]);

  const preview = () => {
    setIsShow(true);
  };
  return (
    <>
      <Card key={approvalRes.id} hoverable={true} className={styles.lastCard} loading={loading}>
        <span className={styles.rbt}>审批类别：{approvalRes.approvalFullName}</span>
        <Descriptions column={1} className={styles.descriptions} colon={false}>
          <Descriptions.Item label="发起人：">
            {approvalRes.initUserRealName + ` (${approvalRes.initUserName})`}
          </Descriptions.Item>
          <Descriptions.Item label="审批事项：">
            {expanded ? (
              <div dangerouslySetInnerHTML={{ __html: approvalRes.approvalName }}></div>
            ) : (
              <Paragraph
                rootClassName="paragraphBox"
                ellipsis={{
                  rows: 1,
                  expandable: 'collapsible',
                  expanded: expanded,
                  onExpand: (_, info) => setExpanded(info.expanded),
                  symbol: expanded ? (
                    <span className={styles.symbolSpan}>收起</span>
                  ) : (
                    <span className={styles.symbolSpan}>展开</span>
                  ),
                }}
              >
                {parserHtmlToString(approvalRes.approvalName)}
              </Paragraph>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="审批事实及理由：">{''}</Descriptions.Item>
          <Descriptions.Item label="审批附件：">
            <span onClick={preview}> 点击浏览邮件</span>
          </Descriptions.Item>
          <Descriptions.Item label="申请时间：">{approvalRes.createTime}</Descriptions.Item>
        </Descriptions>
      </Card>
      {/* {isShow && <Browse item={mailData} close={() => setIsShow(false)}></Browse>} */}
    </>
  );
};

export default Component;
