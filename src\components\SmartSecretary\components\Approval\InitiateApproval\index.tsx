/**
 * 发起审批页入口
 */
import { getHrPage } from '@/api/account/privilege';
import { create } from '@/api/Approval';
import SelectEmployee from '@/components/SmartSecretary/publicComponents/SelectEmployee';
import SelectFile from '@/components/SmartSecretary/publicComponents/SelectFile';
import { Popup } from '@/components/SmartSecretary/publicComponents/vantComponents';
import useUserStore from '@/store/useUserStore';
import { formatDate } from '@/utils/date';
import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { FC, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Input, Picker, Toast } from 'react-vant';
import AbnormalAttendance from '../ApprovalItem/AbnormalAttendance';
import treeData from './approvalTypeTree';
import styles from './index.module.less';
interface Props {
  onSubmit: () => void;
}
const InitiateApproval: FC<Props> = ({ onSubmit }) => {
  const [AbnormalAttendanceOpen, setAbnormalAttendanceOpen] = useState(false); // 显示审批事项【考勤异常】

  const [openAddressBook, setOpenAddressBook] = useState(false); // 显示下级审批人
  const [visible, setVisible] = useState(false);
  const [sprList, setSprList] = useState([]); // 下级审批人列表

  const location = useLocation();
  const userInfo = useUserStore((state) => state.userInfo);
  const [approvalData, setApprovalData] = useState({
    ...location?.state?.approvalType,
    attendanceApproval: location?.state?.attendanceApproval || {},
  }); // 传递给接口的审批对象，也接收默认数据传入

  const { attendanceApproval } = approvalData;
  const [loading, setLoading] = useState(false);
  const [openSelectFile, setOpenSelectFile] = useState(false);

  // 获取下级审批人列表
  const fetchList = async () => {
    getHrPage({ pageNo: 1, pageSize: 1000 }).then((res: any) => {
      console.log('res', res.data.list);

      setSprList(res.data.list || []);
    });
  };

  useEffect(() => {
    fetchList();
  }, []);

  // 格式化审批事项
  const renderSqsx = () => {
    return attendanceApproval.date
      ? `${formatDate(attendanceApproval.date, 'YYYY年MM月DD日')};${attendanceApproval.timeName};${attendanceApproval.codeName};扣${attendanceApproval.money || 0}元;<br/>${attendanceApproval.userRealName}${attendanceApproval.userName};`
      : '';
  };

  const checkForm = () => {
    if (!approvalData.approvalFullName) {
      Toast.fail('请选择审批类别');
      return;
    }
    console.log('attendanceApproval', attendanceApproval);

    if (
      !attendanceApproval.date ||
      !attendanceApproval.timeName ||
      !attendanceApproval.codeName ||
      attendanceApproval.money < 0 ||
      !attendanceApproval.userName
    ) {
      Toast.fail('请完善审批事项');
      return;
    }
    if (!approvalData.content) {
      Toast.fail('请填写审批事实及理由');
      return;
    }
    if (!approvalData.approvalUserRealName) {
      Toast.fail('请选择下级审批人');
      return;
    }
    return true;
  };

  // 发起审批
  const submitClick = () => {
    if (!checkForm()) return;
    setLoading(true);
    create({
      ...approvalData,
      initUserId: userInfo?.id,
      initUserName: userInfo?.username,
    })
      .then((res) => {
        setLoading(false);
        Toast.success('发起审批成功');
        onSubmit();
      })
      .catch(() => {
        setLoading(false);
      });
  };

  return (
    <div className={styles.InitiateApproval}>
      <div className={styles.InitiateApprovalContent}>
        <div className={styles.InputItem}>
          <label className={styles.requiredTag}>审批类别</label>
          <div>
            <input readOnly value={approvalData.approvalFullName} placeholder="请选择审批类别" />
            <Button size="small" type="primary" ghost onClick={() => setVisible(true)}>
              选择
            </Button>
          </div>
        </div>
        <div className={styles.textareaItem}>
          <div className={styles.textareaTop}>
            <label className={styles.requiredTag}>审批事项</label>
            <div>
              <Button
                size="small"
                type="primary"
                ghost
                onClick={() => {
                  // setAbnormalAttendanceOpen(true);
                  if (!approvalData.configCode) {
                    Toast.info({
                      message: '请选择审批类别!',
                    });
                  } else {
                    setAbnormalAttendanceOpen(true);
                  }
                }}
              >
                选择
              </Button>
            </div>
          </div>
          {/* <Input.TextArea readOnly placeholder="请选择需要的审批事项" value={renderSqsx()} /> */}
          <div
            className={styles.textareaFoot}
            dangerouslySetInnerHTML={{ __html: renderSqsx() }}
          ></div>
        </div>
        <div className={styles.textareaItem}>
          <div className={styles.textareaTop}>
            <label className={styles.requiredTag}>审批事实及理由</label>
          </div>
          <Input.TextArea
            className={styles.TextAreaNox}
            placeholder="审批事实及理由"
            value={approvalData.content}
            onChange={(e) => setApprovalData((prev: any) => ({ ...prev, content: e }))}
          ></Input.TextArea>
        </div>
        <div className={styles.InputItem}>
          <label className={styles.requiredTag}>下级审批人</label>
          <div>
            <input
              readOnly
              value={`${approvalData.approvalUserRealName || ''}${approvalData.approvalUserName || ''}`}
            />
            <Button size="small" type="primary" ghost onClick={() => setOpenAddressBook(true)}>
              选择
            </Button>
          </div>
        </div>
        <div className={styles.InputItem}>
          <label>添加附件</label>
          <div>
            <Button
              size="small"
              type="primary"
              ghost
              icon={<PlusOutlined />}
              onClick={() => setOpenSelectFile(true)}
            >
              选择
            </Button>
          </div>
        </div>
        <Popup visible={visible} position="bottom" round onClose={() => setVisible(false)}>
          <Picker
            title="审批类别"
            columns={treeData.map((item, key) => {
              return { text: item.approvalName, value: item.code, key: item.code };
            })}
            // onChange={(val: any, selectRow: any, index: any) => {
            //   console.log('选中项: ', selectRow);
            //   console.log(`选中值${val}，索引: ${index}`);
            // }}
            onCancel={() => setVisible(false)}
            onConfirm={(val: any, selectRow: any, index: any) => {
              console.log('选中项: ', selectRow);
              console.log(`选中值${val}，索引: ${index}`);
              setVisible(false);
              setApprovalData((prev: any) => ({
                ...prev,
                configCode: selectRow.value, // 审批类别配置编码
                approvalFullName: selectRow.text,
              }));
            }}
          />
        </Popup>
      </div>
      <Button
        className={styles.submitBut}
        size="large"
        type="primary"
        loading={loading}
        onClick={submitClick}
      >
        提交
      </Button>
      <SelectEmployee
        open={openAddressBook}
        title="下级审批人"
        mode="single"
        dataSource={sprList}
        onClose={() => setOpenAddressBook(false)}
        onSubmit={(list) => {
          console.log('选择的人', list);
          const res = list[0];
          setOpenAddressBook(false);
          setApprovalData((prev: any) => ({
            ...prev,
            approvalUser: res.userId,
            approvalUserName: res.username,
            approvalUserRealName: res.realName,
          }));
        }}
      />
      <AbnormalAttendance
        open={AbnormalAttendanceOpen}
        title="审批事项"
        onClose={() => setAbnormalAttendanceOpen(false)}
        approvalData={approvalData}
        onSubmit={(values) => {
          setApprovalData((prev: any) => ({
            ...prev,
            attendanceApproval: {
              ...values,
            },
          }));
          setAbnormalAttendanceOpen(false);
        }}
      />
      <SelectFile
        open={openSelectFile}
        mode="single"
        type={1}
        onClose={() => setOpenSelectFile(false)}
        onSubmit={(list) => {
          console.log('file_list', list);
        }}
      />
    </div>
  );
};
export default InitiateApproval;
