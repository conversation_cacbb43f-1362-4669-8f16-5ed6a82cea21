import useLibraryStore from '@/store/useLibraryStore';
import MenuFilter from './MenuFilter';

const Component = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  close,
  clearFilters,
  config,
}: any) => {
  const [sourceList] = useLibraryStore((state) => [state.sourceList]);
  const toggleItem = (key: string, isSubmit: boolean) => {
    if (selectedKeys.includes(key)) {
      setSelectedKeys(selectedKeys.filter((k: string) => k !== key));
    } else {
      setSelectedKeys([...selectedKeys, key]);
    }
    isSubmit && submit('ok');
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
      submit('ok');
    } else if (type === 'close') {
      close();
      config?.setFileSourceOpen(false);
    } else if (type === 'ok') {
      confirm({ closeDropdown: false });
      // close();
    }
  };
  return (
    <MenuFilter
      items={sourceList.filter((item: any) => {
        return item.label !== '回收站';
      })}
      selectedKeys={selectedKeys}
      onSelect={toggleItem}
      onSubmit={submit}
    />
  );
};

export default Component;
