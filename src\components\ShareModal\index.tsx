import { PropsWithChildren, useContext, useEffect, useState } from 'react';
import Group from './Group';
import ShareContext from './ShareContext';
import ShareIndex from './ShareIndex';
import { ModalType, ShareType } from './ShareIndex/typing';
import User from './User';

interface ShareModalProps {
  onShare: (data: any) => void; // 分享事件
  shareType?: ShareType; // 分享类型
  onCancel: () => void; // 关闭分享
  dataTitle: string; // 业务类型
  confirmText?: string; // 确认按钮文字。默认值：确认分享
  open: boolean; // 是否打开弹窗
  sharedData?: any[]; // 被分享的数据
  resetShareData?: () => void; // 设置分享的数据
  columns?: any[]; // 被分享的列
  onSelectOpen?: (open: boolean) => void;
  selectOpen?: boolean; // 数据选择框是否展示
  disableGroup?: boolean; // 禁用分享群
  secure?: boolean; // 保密等级
  onSetSecure?: (secure: number) => void; // 设置保密等级
  loading?: boolean; // 是否加载中
  destroyOnClose?: boolean; // 关闭时是否销毁
}

const Component = ({
  onShare,
  onCancel,
  open,
  dataTitle,
  children,
  onSelectOpen,
  columns,
  sharedData,
  resetShareData,
  loading,
  selectOpen,
  disableGroup,
  secure,
  confirmText,
  onSetSecure,
}: PropsWithChildren<ShareModalProps>) => {
  const [groupDataSource, setGroupDataSource] = useState([]);
  const [userDataSource, setUserDataSource] = useState([]);

  const [shareUserOpen, setShareUserOpen] = useState(false);

  const [shareGroupOpen, setShareGroupOpen] = useState(false);

  useEffect(() => {
    if (!open) {
      resetState();
    }
  }, [open]);

  const resetState = () => {
    setGroupDataSource([]);
    setUserDataSource([]);
    resetShareData?.();
  };

  const toolBarClick = (type: ModalType) => {
    switch (type) {
      case ModalType.User:
        if (shareGroupOpen) {
          setShareGroupOpen(false);
        }
        if (selectOpen) {
          onSelectOpen?.(false);
        }
        break;
      case ModalType.Group:
        if (shareUserOpen) {
          setShareUserOpen(false);
        }
        if (selectOpen) {
          onSelectOpen?.(false);
        }
        break;
      case ModalType.Data:
        if (shareUserOpen) {
          setShareUserOpen(false);
        }
        if (shareGroupOpen) {
          setShareGroupOpen(false);
        }
        break;
    }
    window.setTimeout(() => {
      showModal(type);
    });
  };

  /**
   * 展示选择框
   */
  const showModal = (modalType: ModalType) => {
    switch (modalType) {
      case ModalType.User:
        setShareUserOpen(true);
        break;
      case ModalType.Group:
        setShareGroupOpen(true);
        break;
      case ModalType.Data:
        onSelectOpen?.(true);
        break;
      default:
        break;
    }
  };

  /**
   * 设置分享用户
   * @param list
   */
  const setSharedUserList = (list: any) => {
    setUserDataSource(list);
    setShareUserOpen(false);
  };

  /**
   * 设置共享群
   */
  const setSharedGroupList = (list: any) => {
    setGroupDataSource(list);
    setShareGroupOpen(false);
  };

  const shareContext = useContext(ShareContext);
  shareContext.user = userDataSource;
  shareContext.setUser = setUserDataSource;
  shareContext.group = groupDataSource;
  shareContext.setGroup = setGroupDataSource;
  shareContext.selectOpen = Boolean(selectOpen);
  shareContext.shareUserOpen = Boolean(shareUserOpen);
  shareContext.shareGroupOpen = Boolean(shareGroupOpen);

  return (
    <>
      <ShareContext.Provider value={shareContext}>
        <ShareIndex
          open={open}
          dataTitle={dataTitle}
          onCancel={() => {
            setShareGroupOpen(false);
            setShareUserOpen(false);
            onCancel();
          }}
          onShare={(shareData) =>
            onShare({
              ...shareData,
            })
          }
          userDataSource={userDataSource}
          groupDataSource={groupDataSource}
          sharedData={sharedData}
          loading={loading}
          disableGroup={disableGroup}
          columns={columns}
          onToolBarClick={toolBarClick}
          secure={secure}
          onSetSecure={onSetSecure}
          confirmText={confirmText}
        />
        {!disableGroup && (
          <Group
            open={shareGroupOpen}
            onCancel={() => setShareGroupOpen(false)}
            onAdd={setSharedGroupList}
            selectDataList={groupDataSource}
          />
        )}
        <User
          open={shareUserOpen}
          onCancel={() => setShareUserOpen(false)}
          onAdd={setSharedUserList}
          selectDataList={userDataSource}
        />
        {children}
      </ShareContext.Provider>
    </>
  );
};
export default Component;
