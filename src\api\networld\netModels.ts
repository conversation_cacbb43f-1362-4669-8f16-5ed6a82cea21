export interface PageListReq {
  title?: string[];
  groupId?: string;
  pageNo: number;
  pageSize: number;
  realNameList?: string[];
  type?: number | null;
}

export interface ShareReq {
  groupIdList?: string[];
  userIdList?: string[];
  websiteIdList: string[];
}

export interface CheckReq {
  websiteId?: string;
  webpageId?: string;
}

export interface DeleteReq {
  type: number; // - 类型(0：删除黑名单 1：创建黑名单 2：删除白名单 3：删除分类黑名单外黑名单 4：右键添加 )
  title: string;
  iconUrl: string;
  websiteUrl: string;
}
