import Countdown from 'antd/es/statistic/Countdown';
import classNames from 'classnames';
import { FC } from 'react';
import styles from './Index.module.less';

interface Props {
  duration: number;
  currentTime: number;
}

const CountDown: FC<Props> = ({ duration, currentTime }: Props) => {
  return (
    <Countdown
      className={classNames(styles.countSpan)}
      value={duration - currentTime + Date.now()}
      format={
        duration - currentTime > 3.15e10
          ? 'Y 年 M 月 D 天 H 时 m 分 s 秒后撤回'
          : duration - currentTime > 2.63e9
            ? ' M 月 D 天 H 时 m 分 s 秒后撤回'
            : duration - currentTime > 86400000
              ? 'D 天 H 时 m 分 s 秒后撤回'
              : duration - currentTime > 3600000
                ? 'H 时 m 分 s 秒后撤回'
                : duration - currentTime > 60000
                  ? 'm 分 s 秒后撤回'
                  : 's 秒后撤回'
      }
      // "D 天 H 时 m 分"
    ></Countdown>
  );
};

export default CountDown;
