import { Button, Input, Select } from 'antd';
import { FC, useContext, useEffect, useState } from 'react';
import RootContext from './context';
import styles from './index.module.less';
interface QueryInfoProps {
  isLeft?: boolean; // 是否是左边
}
const ModalSearch: FC<QueryInfoProps> = ({ isLeft = true }) => {
  useEffect(() => {}, []);

  const [formData, setFormData] = useState<{ [key: string]: string }>({});
  const handleChange = (val: string, name: string) => {
    setFormData((pre) => ({ ...pre, [name]: val }));
    queryAll({ [name]: val });
  };
  const handleSelectChange = (val: any) => {
    setFormData({ ...formData, payChannelCode: val });
  };

  const handleSearchAll = (data: any) => {
    isLeft ? queryAll(data) : queryAllRight(data);
  };

  const handleSearchResult = (data: any) => {
    isLeft ? queryResult(data) : queryResultRight(data);
  };

  const { queryResult, queryResultRight, queryAll, queryAllRight, formConfig } =
    useContext(RootContext);
  return (
    <>
      <div className={styles.haaderSearch} style={{ visibility: isLeft ? 'visible' : 'hidden' }}>
        {formConfig.map((itm: any, index: number) => {
          return (
            <div className={styles.searchInput} key={index}>
              <span>{itm.label}：</span>
              {itm.name === 'payChannelCode' ? (
                <Select
                  style={{ width: 180 }}
                  value={formData[itm.name]}
                  onChange={handleSelectChange}
                  options={[
                    { value: 'wx_native', label: '微信' },
                    { value: 'alipay_qr', label: '支付宝' },
                  ]}
                />
              ) : (
                <Input
                  placeholder={itm.placeholder}
                  value={formData[itm.name]}
                  onChange={(e) => handleChange(e.target.value, itm.name)}
                />
              )}
            </div>
          );
        })}
        <div className={styles.btns}>
          <Button
            type="primary"
            className={styles.btnsParmary}
            onClick={() => handleSearchAll(formData)}
          >
            全部查询
          </Button>
          <Button
            type="primary"
            className={styles.btnsParmary}
            onClick={() => handleSearchResult(formData)}
          >
            结果查询
          </Button>
          {/* {!isLeft && (
            <Button type="primary" className={styles.btnsParmary} onClick={() => confirmSelect()}>
              确定选择
            </Button>
          )} */}
        </div>
      </div>
    </>
  );
};

export default ModalSearch;
