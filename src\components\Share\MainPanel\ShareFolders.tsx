import { Button, Space, Table } from 'antd';
import { useContext, useMemo } from 'react';
import Context from '../Context';
import { getColumns } from '../ShareFolders/columns';
import styles from './index.module.less';

const Component = () => {
  const { useMainPanelCtxStore } = useContext(Context);
  const [sharedFolderList, setSharedFolderList, sharedFolderMap, setSharedFolderMap] =
    useMainPanelCtxStore!((state) => [
      state.sharedFolderList,
      state.setSharedFolderList,
      state.sharedFolderMap,
      state.setSharedFolderMap,
    ]);
  const columns = useMemo(() => {
    return getColumns().map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              {sharedFolderList.length === 0 && <span>操作</span>}
              {sharedFolderList.length > 0 && (
                <Button
                  type="primary"
                  size="small"
                  ghost
                  onClick={() => {
                    setSharedFolderMap({});
                    setSharedFolderList([]);
                  }}
                >
                  全部取消
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          return (
            <Button
              ghost
              type="primary"
              size="small"
              onClick={() => {
                const map = { ...sharedFolderMap };
                delete map[row.id];
                setSharedFolderMap(map);
                setSharedFolderList(Object.values(map));
              }}
            >
              取消
            </Button>
          );
        };
      }
      return item;
    });
  }, [sharedFolderList, sharedFolderMap]);

  return (
    <div className={styles.list}>
      <div className={styles.title}>已选共享群({sharedFolderList.length})</div>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={sharedFolderList}
        rowKey={'id'}
        scroll={{ y: 163 }}
        pagination={false}
      />
    </div>
  );
};

export default Component;
