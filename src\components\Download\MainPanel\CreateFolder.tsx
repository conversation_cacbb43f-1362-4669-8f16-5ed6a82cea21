import MultiModal from '@/components/MultiModal';
import { cwsRequest } from '@/store/useCppWebSocketStore';
import { Button, Input, message } from 'antd';
import { useContext, useMemo, useState } from 'react';
import Context from '../Context';
import styles from './index.module.less';

interface Props {
  onCancel?: () => void;
  onOk?: () => void;
}

const Component = ({ onCancel, onOk }: Props) => {
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [location, setLocation] = useMainPanelCtxStore!((state) => [
    state.location,
    state.setLocation,
  ]);
  const [folder, setFolder] = useState('');
  const okDisabled = useMemo(() => {
    return folder === '';
  }, [folder]);
  const cancel = () => {
    setFolder('');
    if (onCancel) {
      onCancel();
    }
  };
  const ok = () => {
    cwsRequest({
      module: 'desktopFile',
      method: 'createDirectory',
      data: {
        path: `${location}/${folder}`,
      },
    }).then((res: any) => {
      if (res.code === 0) {
        const { status, msg } = JSON.parse(res.data);
        if (status === 'fail') {
          return message.error(msg || '新建文件夹失败');
        }
        setLocation(`${location}/${folder}`);
        if (onOk) {
          onOk();
        }
      }
    });
  };

  return (
    <MultiModal
      layoutGroup={`download_${config?.module}_${config?.type}`}
      layoutClassName="normal"
      destroyOnClose={true}
      top={185}
      width={550}
      title="新建文件夹"
      open={true}
      onCancel={cancel}
      zIndex={1900}
      footer={[
        <Button key="ok" type="primary" disabled={okDisabled} onClick={ok}>
          确认
        </Button>,
        <Button key="cancel" type="primary" ghost onClick={cancel}>
          取消
        </Button>,
      ]}
    >
      <div className={styles.createFolder}>
        <div className={styles.item}>
          <span>新建文件夹名称：</span>
          <Input
            style={{ width: '320px' }}
            value={folder}
            placeholder="请输入"
            onChange={(event) => setFolder(event.target.value)}
          />
        </div>
      </div>
    </MultiModal>
  );
};

export default Component;
