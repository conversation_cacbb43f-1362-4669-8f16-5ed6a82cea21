export interface File {
  id?: string;
  userId?: string;
  userName?: string;
  tenantId?: string;
  realName?: string;
  groupId?: string;
  title?: string;
  content?: string;
  source?: number;
  sourceName?: string;
  safeLevel?: number;
  safeLevelName?: null;
  bizId?: null;
  deleted?: number;
  remark?: string;
  bizTime?: string;
  fileType?: string;
  fileFormatType?: string;
  fileFormatTypeName?: string;
  fileSize?: number;
  filePath?: string;
  imgIsStandard?: null;
  shareFlag?: null;
  createTime?: string;
  deleteTime?: null;
  shareUserId?: null;
  shareUserName?: null;
  shareRealName?: null;
  shareTenantId?: null;
  blackListFlag?: null;
  version?:number;
  visitPath?: string;  
  secretKey?: string;
  shareExpireTime?: number;
  verifyValue?: boolean;
  verifyText?: string;
}
