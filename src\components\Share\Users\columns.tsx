import { COLUMN } from '@/const';
import { getWidth } from '@/utils/common';

export const getColumns = () => {
  return [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: getWidth(4.5),
      render: (value: any, row: any, index: number) => index + 1,
    },
    {
      title: '姓名',
      dataIndex: 'contactName',
      key: 'contactName',
      width: getWidth(7.5),
      ellipsis: true,
    },
    {
      title: '裕邦账号',
      dataIndex: 'username',
      key: 'username',
      width: getWidth(10.0),
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      key: 'mobile',
      width: getWidth(10.0),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      ellipsis: true,
      width: getWidth(22.0),
    },
    {
      title: '操作',
      dataIndex: 'actions',
      key: 'actions',
      width: getWidth(11.0),
    },
  ];
};
