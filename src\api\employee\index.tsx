import { HrProps } from '@/pages/Employee/types';
import { ApiResponse } from '@/pages/Employee/useFetch';
import request, { Extra } from '../index';
import {
  GetHrPageParams,
  GetShareHrListParams,
  GetUserCatalog,
  GetUserCatalogPic,
  ShareHrListParams,
} from './employee.type';
const base = '/web-api/hr';

/**
 * 查询人事管理列表
 * @returns
 */
export const getHrList = (data: GetHrPageParams, extra?: Extra) => {
  return request.post(
    {
      url: `${base}/query/hr/list`,
      data,
    },
    extra,
  );
};

/**
 * 查询用户下的所有目录列表
 * @returns
 */
export const getHrCatalogById = (params: GetUserCatalog): Promise<ApiResponse<HrProps>> => {
  return request.get<ApiResponse<HrProps>>({
    url: `${base}/hr-catalog/getAllCatalogInUser`,
    params,
  });
};
/**
 * 获得指定目录下的所有图片信息
 */
export const getPicInCatalog = (params: GetUserCatalogPic) => {
  return request.get({
    url: `${base}/hr-catalog-pic/getPicInCatalog`,
    params,
  });
};
/**
 * 删除人事目录图片
 */
export const deletePicInCatalog = (params: GetUserCatalogPic) => {
  return request.delete({
    url: `${base}/hr-catalog-pic/delete`,
    params,
  });
};
/**
 * 获得人事阅档记录
 */
export const getHrRecord = (data: any) => {
  return request.post({
    url: `${base}/hr-read-record/page`,
    data,
  });
};
/**
 * 获得人事档案信息
 */
// export const getHrDetail = (params: { id: string }): Promise<ApiResponse<HrProps>> => {
export const getHrDetail = (params: { id: string }) => {
  return request.get({
    url: `${base}/hr/getDetail`,
    params,
  });
};

/**
 * 更新人事档案信息
 */
export const updateHrDetail = (data: HrProps) => {
  return request.put({
    url: `${base}/hr/update`,
    data,
  });
};

/**
 * 添加档案文件
 */
export const addCatalogFiles = (data: HrProps) => {
  return request.post({
    url: `${base}/catalogpic/addCatalogFiles`,
    data,
  });
};
/**
 * 获得分享的用户列表
 */
export const getShareHrList = (data: GetShareHrListParams) => {
  return request.post({
    url: `${base}/hr/hrInfoShare`,
    data,
  });
};
/**
 * 分享档案给指定的用户
 */
export const shareHrList = (data: ShareHrListParams) => {
  return request.post({
    url: `${base}/hr-read-record/share`,
    data,
  });
};

/**
 * 数据银行查询列表
 */
// export const getDataBankHrs = (data: any) => {
//   return request.post({
//     url: `${base}/query/deleteDataBankHrs`,
//     data,
//   });
// };

/**
 * 回收站查询列表
 */
export const getRecycleHrs = (data: any) => {
  return request.post({
    url: `${base}/query/recycle/list`,
    data,
  });
};

/**
 * 数据银行删除选择
 */
export const deleteDataBankHrs = (data: any) => {
  return request.post({
    url: `${base}/query/deleteDataBankHrs`,
    data,
  });
};

/**
 * 回收站删除/取消删除选择
 */
export const deleteRecyclingHrs = (data: any) => {
  return request.post({
    url: `${base}/query/deleteRecyclingHrs`,
    data,
  });
};

/**
 * 数据银行删除全部
 */
export const deleteDataBankAllHrs = (data: any) => {
  return request.post({
    url: `${base}/query/deleteDataBankAllHrs`,
    data,
  });
};

/**
 * 回收站删除/取消删除全部
 */
export const deleteRecyclingAllHrs = (data: any) => {
  return request.post({
    url: `${base}/query/deleteRecyclingAllHrs`,
    data,
  });
};

/**
 * 当前账号下所有文件(文档、图片、ppt)
 */
export const getLibraryFilesOfSelf = (data: any) => {
  return request.post({
    url: `${base}/library/getLibraryFilesOfSelf`,
    data,
  });
};

// 获得租户下在职人事信息分页
export const getPageStaff = (data: any) => {
  return request.post({
    url: `${base}/hr/pageStaff`,
    data,
  });
};
