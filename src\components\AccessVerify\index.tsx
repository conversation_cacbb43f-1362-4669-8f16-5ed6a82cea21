import { TenantType } from '@/const';
import Permission from '@/const/permission';
import useUserStore from '@/store/useUserStore';
import { PropsWithChildren } from 'react';

interface PermissionProps {
  permission: Permission;
}

/**
 * 检查是否有权限
 * @param permission Permission
 * @returns
 */

export const checkPermission = (permission: Permission) => {
  // 不要验证的菜单
  const noAuthMenus = [Permission.MORE_APP, Permission.INTERNET_RECORD, Permission.HOME];
  const { permissonList, tenantType, unauthPermissionList } = useUserStore.getState();
  return (
    tenantType === TenantType.GENERAL ||
    noAuthMenus.includes(permission) ||
    permissonList.includes(permission) ||
    !unauthPermissionList.includes(permission)
  );
};

const AccessVerify = ({ children, permission }: PropsWithChildren<PermissionProps>) => {
  return checkPermission(permission) ? children : null;
};

export default AccessVerify;
