import { Button, Input } from 'antd';
import { FC, useState } from 'react';
import styles from './index.module.less';

interface Props {
  onSend: (msg: string) => void;
}

const InputArea: FC<Props> = ({ onSend }) => {
  const [msg, setMsg] = useState('');
  const sendClick = () => {
    onSend(msg);
    setMsg('');
  };
  return (
    <div className={styles.inputArea}>
      <Input placeholder="裕邦聊天" value={msg} onChange={(e) => setMsg(e.target.value)} />
      <Button type="primary" onClick={sendClick}>
        发送
      </Button>
    </div>
  );
};
export default InputArea;
