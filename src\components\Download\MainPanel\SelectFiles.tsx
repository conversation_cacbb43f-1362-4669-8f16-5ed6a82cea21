import FilePreview from '@/components/FliePreview';
import type { FilePreviewAPI } from '@/components/FliePreview';
import { Button, Space, Table } from 'antd';
import { useContext, useMemo, useState, useRef } from 'react';
import Context from '../Context';
import { getColumns } from '../SelectFiles/columns';
import styles from './index.module.less';

const Component = () => {
  const filePreviewRef = useRef<FilePreviewAPI>();
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [selectedFileList, setSelectedFileList, selectedFileMap, setSelectedFileMap] =
    useMainPanelCtxStore!((state) => [
      state.selectedFileList,
      state.setSelectedFileList,
      state.selectedFileMap,
      state.setSelectedFileMap,
    ]);
  const columns = useMemo(() => {
    const { module, type } = config!;
    const hasInitFiles = selectedFileList.filter((item: any) => item.status === 'init').length > 0;
    const isDownloadAll = module !== 'preview' && type === 'downloadAll';
    const isPreview = module === 'preview' && type === 'downloadAll';
    const isDownload = type === 'download';
    return getColumns(false, config).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              {!hasInitFiles && <span>进度</span>}
              {hasInitFiles && isPreview && <span>进度</span>}
              {hasInitFiles && isDownloadAll && <span>操作</span>}
              {hasInitFiles && isDownload && (
                <Button
                  type="primary"
                  size="small"
                  ghost
                  onClick={() => {
                    const map = { ...selectedFileMap };
                    selectedFileList.forEach((item: any) => {
                      if (item.status === 'init') {
                        delete map[item.filePath];
                      }
                    });
                    setSelectedFileMap(map);
                    setSelectedFileList(Object.values(map));
                  }}
                >
                  全部取消
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          let element;
          if (row._status === 'pause') {
            element = <span className={styles.pause}>暂停下载</span>;
          } else if (row.status === 'abort') {
            element = <span className={styles.abort}>终止下载</span>;
          } else if (row.status === 'success') {
            element = <span className={styles.success}>已完成</span>;
          } else if (row.status === 'loading') {
            element = <span className={styles.uploading}>正在下载</span>;
          } else if (row.status === 'waiting') {
            element = <span className={styles.waiting}>等待中</span>;
          } else if (row.status === 'error') {
            element = <span className={styles.error}>下载错误</span>;
          } else if (isPreview) {
            element = <span className={styles.waiting}>未开始</span>;
          } else {
            element = (
              <Space>
                {isDownload && (
                  <Button
                    ghost
                    type="primary"
                    size="small"
                    onClick={() => {
                      const map = { ...selectedFileMap };
                      delete map[row.filePath];
                      setSelectedFileMap(map);
                      setSelectedFileList(Object.values(map));
                    }}
                  >
                    取消
                  </Button>
                )}
                <Button
                  style={{ paddingLeft: 0, paddingRight: 0 }}
                  type="link"
                  size="small"
                  onClick={() => {
                    preview(row);
                  }}
                >
                  浏览
                </Button>
              </Space>
            );
          }
          return element;
        };
      }
      return item;
    });
  }, [selectedFileList, selectedFileMap, config]);
  const preview = (row: any) => {
    filePreviewRef.current?.open({ ...row, fileSource: 13 });
  };

  return (
    <div className={styles.list}>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={selectedFileList}
        rowKey={'id'}
        scroll={{ y: 491 }}
        pagination={false}
      />
      <FilePreview ref={filePreviewRef} />
    </div>
  );
};

export default Component;
