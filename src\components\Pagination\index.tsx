import { Pagination, PaginationProps } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.module.less';

// 自定义分页组件的Props类型，这里我们直接扩展PaginationProps
interface CustomPaginationProps extends PaginationProps {
  customTotal?: React.ReactNode;
  pageSize: number;
  customJump?: boolean;
  total: number; // 总数据量，用于计算末页
  onChange?: (page: number, pageSize: number) => void; // 页码改变的回调函数
}

// 自定义分页组件，包含首页和末页按钮的逻辑
const CustomPagination: React.FC<CustomPaginationProps> = ({
  current = 1,
  total,
  onChange,
  customJump = true,
  ...restProps
}) => {
  const [jumperPage, setJumperPage] = useState<string | number>('');
  // 末页页码的计算
  const lastPage = (restProps.pageSize && Math.ceil(total / restProps.pageSize || 10)) || 1; // 默认pageSize为10
  useEffect(() => {
    if (document.querySelector('.ant-pagination-simple-pager')) {
      document
        .querySelector('.ant-pagination-simple-pager')
        ?.querySelector('input')
        ?.setAttribute('readonly', 'true');
    }
  }, []);
  // 自定义分页项的渲染函数
  const itemRender = (
    current: number,
    type: 'prev' | 'next' | 'page' | 'jump-prev' | 'jump-next',
    originalElement: React.ReactNode,
  ) => {
    if (type === 'prev') {
      // 自定义上一页的渲染
      return (
        <div className={styles.cusButList}>
          {/* 首页按钮 */}
          <span
            title="首页"
            onClick={(event) => {
              event.stopPropagation();
              onChange?.(1, restProps.pageSize);
            }}
          >
            首页
          </span>
          <div className={`${styles.pageBtn} ${current === 0 && styles.disabledBtn}`}>上一页</div>
        </div>
      );
    }
    if (type === 'next') {
      // 自定义下一页的渲染
      return (
        <div className={styles.cusButList}>
          <div
            className={`${styles.pageBtn} ${current >= Math.ceil(total / restProps.pageSize) && styles.disabledBtn}`}
          >
            下一页
          </div>
          {/* 末页按钮 */}
          <span
            title="末页"
            onClick={(event) => {
              event.stopPropagation();
              onChange?.(lastPage, restProps.pageSize);
            }}
          >
            末页
          </span>
        </div>
      );
    }
    // 对于其他类型的分页项，返回原始元素
    return originalElement;
  };
  return (
    <div className={styles.paginationContainer}>
      {/* 分页组件，注意我们没有在这里添加首页和末页按钮的渲染逻辑 */}
      <Pagination
        {...restProps}
        itemRender={itemRender}
        current={current}
        showQuickJumper={false}
        showTotal={(total) => ''}
        total={total}
        onChange={onChange}
        // ... 其他Pagination的props
      />
      {/* 页码输入框失去焦点自动触发了跳转，按钮纯摆设 */}
      {customJump ? (
        <div className={styles.customPages}>
          <span>转到</span>
          <input
            type="init"
            value={jumperPage}
            onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
              console.log(Number(e.target.value));
              const value = parseInt(e.target.value);
              if (Number(value)) {
                setJumperPage(value);
              }
            }}
          />
          <span>页</span>
          <button
            disabled={jumperPage === ''}
            onClick={() => {
              console.log(Number(jumperPage));
              if (Number(jumperPage) > lastPage) {
                onChange?.(lastPage, restProps.pageSize);
              } else if (Number(jumperPage) < 1) {
                onChange?.(1, restProps.pageSize);
              } else {
                onChange?.(Number(jumperPage), restProps.pageSize);
              }
              setJumperPage('');
            }}
          >
            确认
          </button>
        </div>
      ) : null}
      {restProps.customTotal ? (
        restProps.customTotal
      ) : (
        <div className={styles.totalBox}>
          共计<span>{total}</span>条
        </div>
      )}
    </div>
  );
};

export default CustomPagination;
