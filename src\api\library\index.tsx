import type { Extra } from '../index';
import api from '../index';

export const searchConditionAll = (data: Record<string, any>, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/library/es/doc/searchConditionAll',
      data,
    },
    extra,
  );
};

export const searchConditionResult = (data: Record<string, any>[], extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/library/es/doc/searchConditionResult',
      data,
    },
    extra,
  );
};

export const libraryFileUpload = ({ data, onUploadProgress }: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/library/file/upload',
      headers: { 'Content-Type': 'multipart/form-data' },
      data,
      onUploadProgress,
    },
    extra,
  );
};

// 公共文件上传
export const uploadByPublic = ({ data, onUploadProgress }: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/library/file/uploadByPublic',
      headers: { 'Content-Type': 'multipart/form-data' },
      data,
      onUploadProgress,
    },
    extra,
  );
};

export const libraryFileDownloadSingle = ({ data, onDownloadProgress }: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/library/file/download/single',
      data,
      responseType: 'blob',
      onDownloadProgress,
    },
    extra,
  );
};

export const libraryFileDownloadAll = ({ params, onDownloadProgress }: any, extra?: Extra) => {
  return api.get(
    {
      url: '/web-api/library/file/download/all',
      params,
      responseType: 'blob',
      onDownloadProgress,
    },
    extra,
  );
};

export const libraryDictDataQuery = (params: any, extra?: Extra) => {
  return api.get(
    {
      url: '/web-api/library/dict/data/query',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      params,
    },
    extra,
  );
};

export const libraryShareSaveFile = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/library/share/saveFile',
      data,
    },
    extra,
  );
};

/**
 * 分布式用户鉴权接口-生成返回auth信息
 * 1-裕邦邮箱，2-魔术相册，3-音频播放器，4-视频播放器，5-我的日记，6-备忘祝福，7-裕邦编辑，8-网络世界，9-分享，10-桌面文件，11-通讯录， 12-桌面软件，13-文库大全，14-数据银行，15-回收站，16-登录回顾，17-裕邦浏览器，18-账户管理，19-共享天地
 */
export const libraryAuthCipher = (data: any, extra?: Extra): Promise<any> => {
  return api.post(
    {
      url: '/web-api/library/cipher/authCipher',
      data,
    },
    extra,
  );
};

export const libraryCallbackUpload = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/library/file/callbackUpload',
      data,
    },
    extra,
  );
};
export const desktopFileCallbackUpload = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/library/file/callbackUploadDesktop',
      data,
    },
    extra,
  );
};
export const libraryCallbackDownload = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/library/file/callbackDownload',
      data,
    },
    extra,
  );
};

export const libraryRemoveOrCancel = (data: any, extra?: Extra) => {
  return api.delete(
    {
      url: '/web-api/library/main/removeOrCancel',
      data,
    },
    extra,
  );
};

export const libraryDataBankDeleteSelect = (data: any, extra?: Extra) => {
  return api.delete(
    {
      url: '/web-api/library/dataBank/deleteSelectDocumentData',
      data,
    },
    extra,
  );
};

export const libraryDataBankDeleteAll = (data: any, extra?: Extra) => {
  return api.delete(
    {
      url: '/web-api/library/dataBank/deleteAllDocumentData',
      data,
    },
    extra,
  );
};

export const libraryRecycleRemoveOrCancel = (data: any, extra?: Extra) => {
  return api.delete(
    {
      url: '/web-api/library/main/removeOrCancel',
      data,
    },
    extra,
  );
};

export const blackActionByLibrary = (data: any, extra?: Extra) => {
  return api.put(
    {
      url: '/web-api/contacts/contact/person/blackActionByLibrary',
      data,
    },
    extra,
  );
};

export const previewVerify = (params: any, extra?: Extra) => {
  return api.get(
    {
      url: '/web-api/library/share/viewAble',
      params,
    },
    extra,
  );
};

export const preview = (params: any, extra?: Extra) => {
  return api.get(
    {
      url: '/preview/preview',
      params,
    },
    extra,
  );
};
