import Editor, { EditorExposeProperts } from '@/pages/Editor/Main/Content';
import getMediaUrl from '@/utils/getMediaUrl';
import { Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import type { File } from '../interface';
import styles from './YBWordPreview.module.less';

interface Props {
  file: File;
}
const YBWordPreview = ({ file }: Props) => {
  const getEditorContent = async () => {
    let url = getMediaUrl(file.visitPath || '');
    console.log('[ 下载url ] >', url);
    url = '/ceph-api' + url.slice(url.indexOf('/api/'));
    console.log('代理处理的url', url);

    const res: any = await fetch(url);
    const blob = await res.blob();
    const reader = new FileReader();
    let fileContent;
    reader.onerror = (error) => {
      console.error(error);
    };
    reader.readAsText(blob);
    return new Promise((resolve) => {
      reader.onloadend = () => {
        fileContent = reader.result;
        console.log('[ 读取文件内容 ] >', fileContent);
        resolve(fileContent);
      };
    });
  };
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    try {
      setLoading(true);
      // const content = file.content || '';
      getEditorContent()
        .then((fileContent: any) => {
          // const content = typeof fileContent === 'string' ? JSON.parse(fileContent) : fileContent;

          // 有可能是json字符串，有可能是对象
          if (typeof fileContent === 'string') {
            // 有可能是来自pc或者移动端
            if (fileContent.indexOf('root') > -1) {
              editorRef.current?.setJSONToContent(JSON.parse(fileContent));
            } else {
              editorRef.current?.setDOMToContent(fileContent);
            }
          } else {
            editorRef.current?.setJSONToContent(fileContent);
          }
        })
        .finally(() => {
          setLoading(false);
        });
      // // 如果文件来源是备忘祝福
      // if (file.source == 6) {
      //   getEditorContent()
      //     .then((fileContent: any) => {
      //       editorRef.current?.setJSONToContent(fileContent);
      //     })
      //     .finally(() => {
      //       setLoading(false);
      //     });
      // } else {
      //   editorRef.current?.setJSONToContent(content);
      //   setLoading(false);
      //   // editorRef.current?.setJSONToContent(
      //   //   '{"root":{"direction":"ltr","format":"","indent":0,"type":"root","version":1,"children":[{"direction":"ltr","format":"","indent":0,"textFormat":0,"textStyle":"","type":"paragraph","version":1,"children":[]}]}}',
      //   // );
      // }
    } catch (error) {
      console.error(error);
    }
  }, []);
  const editorRef = useRef<EditorExposeProperts>(null);
  return (
    <Spin wrapperClassName={styles.spin} spinning={loading}>
      <div className={styles.EditorPreview}>
        <Editor ref={editorRef} />
      </div>
    </Spin>
  );
};

export default YBWordPreview;
