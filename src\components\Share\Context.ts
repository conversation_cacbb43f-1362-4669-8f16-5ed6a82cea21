import type { UseStore } from '@/store/middlewares';
import { createContext } from 'react';
import type {
  SetState as MainPanelSetState,
  State as MainPanelState,
} from './MainPanel/useCtxStore';
import type {
  SetState as SelectFilesSetState,
  State as SelectFilesState,
} from './SelectFiles/useCtxStore';
import type {
  SetState as ShareFoldersSetState,
  State as ShareFoldersState,
} from './ShareFolders/useCtxStore';
import type { SetState as UsersSetState, State as UsersState } from './Users/<USER>';
export { default as createUseMainPanelCtxStore } from './MainPanel/useCtxStore';
export { default as createUseSelectFilesCtxStore } from './SelectFiles/useCtxStore';
export { default as createUseShareFoldersCtxStore } from './ShareFolders/useCtxStore';
export { default as createUseUsersCtxStore } from './Users/<USER>';

export interface Config {
  module: 'library' | 'audioPlay' | 'videoPlay' | 'preview' | 'netWorld';
  fileList?: any[];
  typeName?: string; 
}

interface Value {
  useMainPanelCtxStore: null | UseStore<MainPanelState, MainPanelSetState>;
  useUsersCtxStore: null | UseStore<UsersState, UsersSetState>;
  useShareFoldersCtxStore: null | UseStore<ShareFoldersState, ShareFoldersSetState>;
  useSelectFilesCtxStore: null | UseStore<SelectFilesState, SelectFilesSetState>;
  config: null | Config;
}

export default createContext<Value>({
  useMainPanelCtxStore: null,
  useUsersCtxStore: null,
  useShareFoldersCtxStore: null,
  useSelectFilesCtxStore: null,
  config: null,
});
