/**
 * @description 相册模板列表组件
 * <AUTHOR>
 * @date 2024-08-16
 */
import { setBrowseLog } from '@/pages/LoginReview/setRecord';
import { formatDate } from '@/utils/date';
import { Button, Image } from 'antd';
import { FC } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import styles from './index.module.less';
import { ListItem } from './interface';

const AlbumList: FC<any> = (AlbumListProps) => {
  const { renderList, pager, hidePager, fromModule, renderType = 1 } = AlbumListProps;
  const navigate = useNavigate();
  const { state } = useLocation();
  const netWorldClick = (item: ListItem) => {
    setBrowseLog({
      fileName: `网络世界${item.albumsTemplateName}浏览`,
      fileType: 'other',
      fileSource: 8,
    });

    let path = '/networkWorld';
    const query: any = {
      albumsTemplateId: item.albumsTemplateId,
      isNetWorld: true,
      isTopping: true,
    };
    switch (fromModule) {
      case 'share':
        path = '/common';
        query.name = state.name;
        break;
      case 'dataBank':
        path = '/dataBankList';
        query.name = state.name;
        AlbumListProps.changeType('dataOrReView');
        break;
      case 'recycle':
        path = '/recycleList';
        query.name = state.name;
        AlbumListProps.changeType('dataOrReView');
        break;
      case 'workReview':
        path = '/work/reviewList';
        query.name = state.name;
        AlbumListProps.changeType('dataOrReView');
        break;
      default:
        AlbumListProps.changeType('netView');
        break;
    }
    navigate(path, {
      state: query,
    });
  };
  return (
    <div className={styles.albumContainOut}>
      {/* 共享天地跳转到魔术相册 增加返回操作 */}
      {fromModule && renderType !== 2 && (
        <Button
          type="primary"
          ghost
          onClick={() => navigate('/share/category')}
          style={{ margin: '16px 24px 0px 24px', background: '#fff' }}
        >
          返回
        </Button>
      )}
      {fromModule === 'workReview' && (
        <Button
          type="primary"
          onClick={() => navigate(-1)}
          style={{ margin: '16px 24px 0px 24px' }}
        >
          返回
        </Button>
      )}
      <div className={styles.albumContainInner}>
        <div className={`${styles.albumContain} ${hidePager ? styles.netAlbumContain : ''}`}>
          {renderList.map((item: ListItem, index: number) => (
            <div
              key={item.albumsTemplateId}
              className={styles.albumItem}
              onClick={() => {
                renderType === 2
                  ? netWorldClick(item)
                  : navigate('/magicAlbum/albumView', {
                      state: {
                        isTopping: item.templateTag && pager.pageNo === 1 && index === 0,
                        albumsTemplateId: item.albumsTemplateId,
                        senderUserId: item.senderUserId,
                        renderList: renderList,
                        albumsId: item.albumsId,
                        albumsName: item.albumsName, // 为了带去修改相册回填相册名称
                        page: pager.pageNo, //当前第几页
                        pageSize: pager.pageSize, //一页几条
                        total: pager.total, //总数
                        pageTotal: Math.ceil(pager.total / pager.pageSize),
                        index: index, //当前页的第几个
                      },
                    });
              }}
            >
              <div className={styles.albumName}>{item.albumsTemplateName}</div>
              {item.createTime && (
                <div className={styles.timeRow}>{formatDate(item.createTime, 'YYYY-MM-DD')}</div>
              )}
              <div className={styles.imageWrap}>
                <Image className={styles.image} src={item.albumsTemplateCoverUrl} preview={false} />
              </div>
              <div className={styles.imageRight} />
              {AlbumListProps.renderItemBtn?.(item, index)}
            </div>
          ))}
        </div>
      </div>
      {AlbumListProps.RightBtn}
      {!hidePager && pager.total > 0 && (
        <div className={styles.pager}>
          <span className={styles.blueFont}>{pager.total}</span> 个相册 共{' '}
          <span className={styles.blueFont}>{Math.ceil(pager.total / pager.pageSize)}</span> 页
          当前第 <span className={styles.blueFont}>{pager.pageNo}</span> 页
        </div>
      )}
    </div>
  );
};
export default AlbumList;
