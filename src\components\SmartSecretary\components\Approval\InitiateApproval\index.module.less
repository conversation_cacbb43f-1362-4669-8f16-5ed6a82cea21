.InitiateApproval {
  width: 100%;
  height: 100%;
  background-color: #fff;
  .InitiateApprovalContent {
    height: calc(100% - 80px);
    font-size: 16px;
    .requiredTag {
      position: relative;
      &::after {
        display: block;
        content: '*';
        color: #e35141;
        position: absolute;
        right: -10px;
        top: 51%;
        transform: translateY(-50%);
      }
    }
    .InputItem {
      height: 60px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      input {
        outline: none;
        text-align: right;
        padding-right: 20px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
      }
    }
    .textareaItem{
      padding:  16px;
      min-height: 120px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      .textareaTop{
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      input {
        outline: none;
        padding-right: 20px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        margin-top: 10px;
      }
      .TextAreaNox{
        width: 100%;
        outline: none;
        margin-top: 10px;
        resize: none;
        &:focus{
          border: none;
        }
      }
    }
  }
  .submitBut {
    width: 80%;
    margin-left: 10%;
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
