import { AddressList, MailChatAttachVOList } from '@/api/mail/chat/mailModels';
import { AddressBookData } from '@/components/MailBook/MailBook';
import { FileRecord } from '@/components/SelectFile/columns';

//发送  通讯录转邮箱数据   人
export const convertAddress = (data: AddressBookData[]) => {
  const result: AddressList[] = data.map((item) => {
    return {
      readFlag: 0,
      addresseeStatus: 0,
      revokeFlag: 0,
      blackFlag: 0,
      addressStatus: item.contactType,
      addressee: item.email,
      shareFlag: 0,
      addresseeName: item.contactName,
      contactPersonId: item.id,
      userId: item.userId,
      mobile: item.mobile,
      realName: item.contactName,
      username: item.username,
      contactType: item.contactType,
      avatar: item?.avatar || '',
    };
  });
  return result;
};
//续发  邮箱转通讯录  人
export const revertAddress = (data: any) => {
  const result = data.map((item: any) => {
    return {
      id: item?.contactPersonId || '',
      contactName: item?.addresseeName || '',
      username: item.username,
      email: item.addressee,
      mobile: item.mobile,
      userId: item.userId || '',
      contactType: item.userId ? 1 : 2,
      avatar: item?.avatar || '',
      type: 1,
    };
  });
  return result;
};
//发送  通讯录转邮箱  群
export const convertGroup = (data: any) => {
  const result = data.map((item: any) => {
    return {
      groupId: item.groupId,
      groupName: item.contactName,
      contactPersonVOS: [],
      avatar: item.avatar || '',
    };
  });
  return result;
};
//续发  邮箱转通讯录  群
export const revertGroup = (data: any) => {
  const result = data.map((item: any) => {
    return {
      id: item.groupId,
      groupId: item.groupId,
      contactName: item.groupName,
      type: 2,
      contactType: 0,
    };
  });
  return result;
};

//回复  邮箱转通讯录  群
export const converReply = (data: any) => {
  const result = data.map((item: any) => {
    return {
      id: item.groupId,
      groupName: item.groupName,
      member: item.contactPersonVOS.map((item: any) => item.realName),
      type: 2,
    };
  });
  return result;
};

export const convertAttach = (data: FileRecord[]) => {
  const result: MailChatAttachVOList[] = data.map((item) => {
    return {
      attachmentId: item.id,
      attachmentUrl: item.filePath,
      attachmentName: item.title,
      attachmentSize: item.fileSize,
      fileType: item.fileType,
      fileFormatType: item.fileFormatType,
      mediaType: 0,
      visitPath: item.visitPath,
      secretKey: item.secretKey,
    };
  });
  return result;
};

export const revert = (data: any) => {
  const result = {
    avatar: data.avatar || '',
    contactName: data.realName || data.groupName || '',
    id: data?.userId || '',
    username: data.username || '',
    email: data.email,
    userId: data.userId || '',
    contactType: data.userId ? 1 : 2,
    type: data.groupId ? 2 : 1,
    groupId: data.groupId,
    groupCount: data.countNum,
  };
  return result;
};

export const convert = (item1: any, item2: any, item3: any, item4: any) => {
  console.log('item1', item1);
  console.log('item2', item2);
  console.log('item3', item3);
  console.log('item4', item4);
  const res1: any = item1.map((item: any) => {
    return {
      attachmentId: item.id,
      attachmentUrl: item.filePath,
      attachmentName: item.title,
      attachmentSize: item.fileSize,
      fileType: item.fileType,
      fileFormatType: item.fileFormatType,
      mediaType: 3,
      secretKey: item.secretKey,
      mediaDuration: 0,
    };
  });
  const res2: any = item2.map((item: any) => {
    return {
      attachmentId: item.id,
      attachmentUrl: item.filePath,
      attachmentName: item.title,
      attachmentSize: item.fileSize,
      fileType: item.fileType,
      fileFormatType: item.fileFormatType,
      mediaType: 0,
      secretKey: item.secretKey,
      mediaDuration: item.duration || 0,
    };
  });
  const res3: any = item3.map((item: any) => {
    return {
      attachmentId: item.id,
      attachmentUrl: item.filePath,
      attachmentName: item.title,
      attachmentSize: item.fileSize,
      fileType: item.fileType,
      fileFormatType: item.fileFormatType,
      mediaType: 1,
      visitPath: item.visitPath,
      secretKey: item.secretKey,
      mediaDuration: 0,
    };
  });
  const res4: any = item4.map((item: any) => {
    return {
      attachmentId: item.id,
      attachmentUrl: item.filePath,
      attachmentName: item.title,
      attachmentSize: item.fileSize,
      fileType: item.fileType,
      fileFormatType: item.fileFormatType,
      mediaType: 2,
      visitPath: item.visitPath,
      secretKey: item.secretKey,
      mediaDuration: 0,
    };
  });
  return [...res1, ...res2];
};

export const secondsToHMS = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return [
    hours ? hours.toString().padStart(2, '0') : '00',
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0'),
  ].join(':');
};
