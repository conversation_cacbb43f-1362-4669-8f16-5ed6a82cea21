.midcard,
.firstCard,
.lastCard,
.editMidCard {
  border-radius: 10px;
  opacity: 1;
  background: #fff;
  box-sizing: border-box;
  border: 1px solid #3d5afe;
  z-index: 1;
  margin-bottom: 8px;
  position: relative;
  :global {
    .ant-card-body {
      padding: 16px;
    }
    .ant-descriptions-item-label {
      color: rgba(0, 0, 0, 0.6);
      font-weight: 600;
    }
    .ant-typography {
      margin-bottom: 0 !important;
    }
  }

  .rbt {
    position: absolute;
    top: 20px;
    right: 20px;
    height: 16px;
    opacity: 1;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    color: #3d5afe;
    z-index: 1;
  }
  .red {
    color: #e35141;
  }
}
.lastCard {
  .rbt {
    position: initial;
    margin-bottom: 10px;
    display: flex;
  }
}
