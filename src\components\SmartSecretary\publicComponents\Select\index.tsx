/**
 * 公共下拉组件
 */

import { getDomRealWH } from '@/utils/common';
import { Button, Drawer, List } from 'antd';
import classNames from 'classnames';
import { FC } from 'react';
import styles from './index.module.less';

interface Props {
  title?: string;
  value?: string;
  onConfirm: (val: string) => void;
  onClose: () => void;
  open: boolean;
  dataSource: any[];
}

const ErrorTime: FC<Props> = ({ title, value, onConfirm, open, onClose, dataSource }) => {
  return (
    <Drawer
      placement="bottom"
      closable={false}
      onClose={onClose}
      open={open}
      getContainer={false}
      width={getDomRealWH('#smartSecretaryRoot').width}
      className={styles.ybSelect}
      title={
        <div className={styles.header}>
          <div className={styles.title}>{title}</div>
          <Button onClick={onClose}>关闭</Button>
        </div>
      }
      footer={
        <div className={styles.footer} onClick={onClose}>
          取消
        </div>
      }
    >
      <List
        bordered
        dataSource={dataSource}
        renderItem={(item) => (
          <List.Item>
            <div
              className={classNames(styles.listItem, item.value === value ? styles.selected : '')}
              onClick={() => onConfirm(item)}
            >
              {item.label}
            </div>
          </List.Item>
        )}
      />
    </Drawer>
  );
};

export default ErrorTime;
