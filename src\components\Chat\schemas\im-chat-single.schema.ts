import { RxJsonSchema } from 'rxdb';

export interface ImChatSingleData {
  id: string;
  content: string;
  msgType: number; // 确保是 number 类型
  extra: string;
  sendUserId: number; // 确保是 number 类型
  sendTime: number; // 确保是 number 类型
  receiveUserId: number; // 确保是 number 类型
  status: string;
}

export function getSchema(): RxJsonSchema<ImChatSingleData> {
  return {
    version: 0,
    type: 'object',
    primaryKey: 'id', // 明确声明主键字段
    properties: {
      id: {
        type: 'string',
        minLength: 1,
        maxLength: 256, // 添加 maxLength 属性
      },
      content: { type: 'string' },
      msgType: { type: 'integer' }, // 确保是 integer 类型
      extra: { type: 'string' },
      sendUserId: { type: 'string' }, // 确保是 integer 类型
      sendTime: { type: 'integer' }, // 确保是 integer 类型
      receiveUserId: { type: 'string' }, // 确保是 integer 类型
      status: { type: 'string' }, // 发送状态
    },
    required: ['id', 'content', 'msgType', 'extra', 'sendUserId', 'sendTime', 'receiveUserId'],
    additionalProperties: false, // 确保始终为 false
  };
}
