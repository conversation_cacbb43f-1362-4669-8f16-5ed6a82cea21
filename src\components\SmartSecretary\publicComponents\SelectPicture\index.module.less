.employeeListContainer {
  background: #fff;
  .emplopeeList {
    .title {
      font-weight: bold;
      font-size: 16px;
    }
    .username {
      font-size: 12px;
      color: #8b9cfe;
    }
    button {
      font-size: 12px;
      padding: 0 6px;
      border-radius: 4px;
      height: auto;
    }
    .defaultBtn {
      color: #3d5afe;
      border: 1px #3d5afe solid;
    }
  }
  .imgListContainer {
    display: flex;
    flex-flow: wrap;
    justify-content: center;
    .imageItem {
      width: 104px;
      height: 104px;
      border: 0.5px solid transparent;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .imageItemBtn {
        position: absolute;
        width: 40px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.6);
        color: #fff;
        font-size: 14px;
        top: 2px;
        right: 2px;
        cursor: pointer;
        &.imageSelected {
          background: #3d5afed0;
        }
      }
    }
  }
}
.footer {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 16px;
  justify-content: space-between;
  div:first-child {
    color: #a6a6a6;
  }
  button {
  }
}
:global {
  .ant-drawer .ant-drawer-body {
    // padding: 0 20px !important;
    padding: 0 !important;
  }
}
