.wrapContent {
  :global {
    .ant-modal-content {
      padding: 0 !important;
    }
  }
}
.ModalTitle {
  // padding: 16px;
  // box-sizing: border-box;
  // border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .headerTitle {
      font-size: 20px;
      font-weight: 500;
      color: #4d70fe;
    }
    .menubtn {
      border: 1px solid #3d5afe;
      color: #3d5afe;
    }
  }
}
.file {
  // padding: 0 20px;
  // box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 20px 0px;
  .fileSelect {
    display: flex;
    align-items: center;

    .filePosition {
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
      font-weight: normal;
    }
  }
}
.downloadBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  // border-top: 1px solid rgba(0, 0, 0, 0.05);
  .confirmBtn {
    background: #3d5afe;
    color: #fff;
    width: 110px;
    height: 40px;
    margin-left: 10px;
    &:nth-of-type(2n) {
      width: 68px;
      height: 40px;
    }
  }
  .cancelBtn {
    margin-left: 5px;
    border: 1px solid #3d5afe;
    color: #3d5afe;
    width: 68px;
    height: 40px;
    margin-left: 10px;
  }
}
