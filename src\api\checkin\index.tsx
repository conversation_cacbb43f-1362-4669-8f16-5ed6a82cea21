import api, { Extra } from '../index';

export const getAttendanceList = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/attendance/Collects/page',
      data,
    },
    extra,
  );
};

export const createAttendRec = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/attendance/record/generateRecordPc',
      data,
    },
    extra,
  );
};

export const faceCheck = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/attendance/callback/personDetectionScanPc',
      headers: { 'Content-Type': 'multipart/form-data' },
      data,
    },
    extra,
  );
};

export const getTemplateList = (extra?: Extra) => {
  return api.get(
    {
      url: '/web-api/attendance/template/getTemplateList',
    },
    extra,
  );
};

export const faceRegiest = (data: any, extra?: Extra) => {
  return api.post(
    {
      url: '/web-api/attendance/callback/personRegisterScan',
      headers: { 'Content-Type': 'multipart/form-data' },
      data,
    },
    extra,
  );
};
