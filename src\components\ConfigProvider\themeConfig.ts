import { getWidth } from '@/utils/common';

export default function () {
  return {
    cssVar: true,
    token: {
      colorPrimary: '#3d5afe',
      colorInfo: '#3d5afe',
      colorLink: '#3d5afe',
      fontSize: getWidth(14),
      fontSizeSM: getWidth(12),
      fontSizeLG: getWidth(16),
      fontSizeXL: getWidth(20),
      fontSizeHeading1: getWidth(38),
      fontSizeHeading2: getWidth(30),
      fontSizeHeading3: getWidth(24),
      fontSizeHeading4: getWidth(20),
      fontSizeHeading5: getWidth(16),
      fontHeight: getWidth(22),
      fontHeightLG: getWidth(24),
      fontHeightSM: getWidth(20),
      lineWidthBold: getWidth(2),
      borderRadius: getWidth(6),
      borderRadiusXS: getWidth(2),
      borderRadiusSM: getWidth(4),
      borderRadiusLG: getWidth(8),
      borderRadiusOuter: getWidth(4),
      fontSizeIcon: getWidth(12),
      lineWidthFocus: getWidth(4),
      controlHeight: getWidth(32),
      controlHeightSM: getWidth(26),
      controlHeightXS: getWidth(16),
      controlHeightLG: getWidth(40),
      controlOutlineWidth: getWidth(2),
      controlInteractiveSize: getWidth(16),
      controlPaddingHorizontal: getWidth(12),
      controlPaddingHorizontalSM: getWidth(8),
      paddingContentHorizontalLG: getWidth(24),
      paddingContentVerticalLG: getWidth(16),
      paddingContentHorizontal: getWidth(16),
      paddingContentVertical: getWidth(12),
      paddingContentHorizontalSM: getWidth(16),
      paddingContentVerticalSM: getWidth(8),
      sizePopupArrow: getWidth(16),
      paddingXXS: getWidth(4),
      paddingXS: getWidth(8),
      paddingSM: getWidth(12),
      padding: getWidth(16),
      paddingMD: getWidth(20),
      paddingLG: getWidth(24),
      paddingXL: getWidth(32),
      marginXXS: getWidth(4),
      marginXS: getWidth(8),
      marginSM: getWidth(12),
      margin: getWidth(16),
      marginMD: getWidth(20),
      marginLG: getWidth(24),
      marginXL: getWidth(32),
      marginXXL: getWidth(48),
    },
    components: {
      Table: {
        headerBg: '#e8eaf6',
        headerBorderRadius: getWidth(2),
        borderRadius: getWidth(4),
      },
      Input: {
        paddingBlock: getWidth(4),
        paddingBlockSM: getWidth(0),
        paddingBlockLG: getWidth(7),
        paddingInline: getWidth(11),
        paddingInlineSM: getWidth(7),
        paddingInlineLG: getWidth(11),
        inputFontSize: getWidth(14),
        inputFontSizeLG: getWidth(16),
        inputFontSizeSM: getWidth(14),
      },
      Form: {
        labelFontSize: getWidth(14),
        labelHeight: getWidth(32),
        labelColonMarginInlineStart: getWidth(2),
        labelColonMarginInlineEnd: getWidth(8),
        itemMarginBottom: getWidth(24),
        verticalLabelPadding: `0 0 ${getWidth(8)}`,
      },
      Button: {
        paddingInline: getWidth(15),
        paddingInlineLG: getWidth(15),
        paddingInlineSM: getWidth(7),
        onlyIconSize: getWidth(16),
        onlyIconSizeSM: getWidth(14),
        onlyIconSizeLG: getWidth(18),
        contenFontSize: getWidth(14),
        contentFontSizeSM: getWidth(14),
        contentFontSizeLG: getWidth(16),
        contentLineHeight: 1.5714285714285714,
        contentLineHeightSM: 1.5714285714285714,
        contentLineHeightLG: 1.5,
        paddingBlock: getWidth(4),
        paddingBlockSM: 0,
        paddingBlockLG: getWidth(7),
        contentFontSize: getWidth(14),
      },
    },
  };
}
