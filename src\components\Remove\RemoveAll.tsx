import { useRef, useState } from 'react';
import { message, Button } from 'antd';
import { libraryDataBankDeleteAll, libraryRecycleRemoveOrCancel } from '@/api/library';
import RemoveModal from './RemoveModal';
import Context, { createUseMainPanelCtxStore } from './Context';
import useUserStore from '@/store/useUserStore';
import type { Config } from './Context';

interface Props {
  config: Config;
  onClose?: () => void;
}

const Component = ({ config, onClose }: Props) => {
  const removeModalRef = useRef<{ open: () => void }>();
  const [useMainPanelCtxStore] = useState(() => createUseMainPanelCtxStore({ ...config }));
  const [userInfo] = useUserStore((state) => [state.userInfo]);
  const getFileFormatType = (config: Config) => {
    switch (config.module) {
      case 'audioPlay':
        return 'library_file_type_audio';
      case 'videoPlay':
        return 'library_file_type_video';
    }
  };
  const removeOk = (next: () => void) => {
    if (config.removeType === 'dataBank-all-remove') {
      return libraryDataBankDeleteAll({
        userId: userInfo?.id,
        userType: userInfo?.userType,
        startTime: undefined,
        endTime: undefined,
        fileFormatType: getFileFormatType(config),
        tenantType: userInfo?.tenantType,
        deleted: 0,
      }).then((res: any) => {
        if (res.code === 0) {
          message.info('删除成功').then(() => {
            next();
            if (onClose) {
              onClose();
            }
          });
        }
      });
    }
    if (config.removeType === 'recycle-all-remove') {
      return libraryRecycleRemoveOrCancel({
        idList: undefined,
        operateType: 2,
        startTime: undefined,
        endTime: undefined,
        fileFormatType: getFileFormatType(config),
      }).then((res: any) => {
        if (res.code === 0) {
          message.info('删除成功').then(() => {
            next();
            if (onClose) {
              onClose();
            }
          });
        }
      });
    }
    if (config.removeType === 'recycle-all-cancel') {
      return libraryRecycleRemoveOrCancel({
        idList: undefined,
        operateType: 0,
        startTime: undefined,
        endTime: undefined,
        fileFormatType: getFileFormatType(config),
      }).then((res: any) => {
        if (res.code === 0) {
          message.info('取消成功').then(() => {
            next();
            if (onClose) {
              onClose();
            }
          });
        }
      });
    }
  };

  return (
    <Context.Provider
      value={{
        useMainPanelCtxStore,
        config,
      }}
    >
      <Button
        type="primary"
        onClick={() => {
          removeModalRef.current?.open();
        }}
      >
        {config.buttonText}
      </Button>
      <RemoveModal ref={removeModalRef} onOk={removeOk} />
    </Context.Provider>
  );
};

export default Component;
