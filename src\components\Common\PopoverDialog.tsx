import { ReactNode } from 'react';
import { createRoot, Root } from 'react-dom/client';

interface Props {
  content: ReactNode;
  okText?: string;
  onOk?: () => void;
  cancelText?: string;
  onCancel?: () => void;
  onClose?: () => void;
  closeText?: string;
  showAction?: boolean;
  showClose?: boolean;
}

const POPOVER_DIALOG_ELEMENTID = 'popover-dialog-container';
export function disposePopoverDialog() {
  document.getElementById(POPOVER_DIALOG_ELEMENTID)?.hidePopover();
}

/**
 * 右下角-弹出框组件
 * @param props 参数对象
 * @param props.content 对话框内容
 * @param props.onOk 确认执行方法
 * @param props.onCancel 取消执行方法
 * @param props.onClose 关闭执行方法
 * @param props.okText 确认按钮文字，默认为‘同意’（可选）
 * @param props.cancelText 确认按钮文字，默认为‘拒绝’（可选）
 * @param props.closeText 关闭按钮文字，默认为‘关闭’（可选）
 */
export default function createPopoverDialog({
  closeText = '关闭',
  content,
  showAction = true,
  okText = '同意',
  onOk,
  onCancel,
  onClose = onCancel,
  cancelText = '拒绝',
  showClose = true,
}: Props) {
  const container = document.getElementById(POPOVER_DIALOG_ELEMENTID);

  const popoverDialogElment = (
    <div
      style={{
        boxShadow: `var(--ant-box-shadow)`,
        background: 'linear-gradient(180deg, #BBD6FF 1%, #FFFFFF 27%), #FFFFFF',
      }}
      className="rounded-lg bg-white"
    >
      <div className="flex justify-between p-4">
        <span className={`text-xl font-medium text-indigo-A300`}></span>
        {showClose && (
          <button
            onClick={() => {
              (container ?? containerNew).togglePopover(false);
              onClose?.();
            }}
            className={`rounded border border-indigo-A300 px-4 py-1 text-sm text-indigo-A300`}
          >
            {closeText}
          </button>
        )}
      </div>

      <div className={`px-14 py-7`}>{content}</div>

      {showAction && (
        <div className="flex justify-center">
          <div className="flex pb-5 pt-2">
            <button
              className={`mr-[5px] rounded border bg-indigo-A300 px-5 py-2 text-sm font-medium text-white`}
              onClick={() => {
                (container ?? containerNew).togglePopover(false);
                onOk?.();
              }}
            >
              {okText}
            </button>
            <button
              className={`ml-[5px] rounded border border-indigo-A300 px-5 py-2 text-sm font-medium text-indigo-A300`}
              onClick={() => {
                (container ?? containerNew).togglePopover(false);
                onCancel?.();
              }}
            >
              {cancelText}
            </button>
          </div>
        </div>
      )}
    </div>
  );

  if (container) {
    container.className = 'top-auto left-auto right-[112px] bottom-[103px]';
    root?.render(popoverDialogElment);
    container.showPopover();
    return;
  }

  const containerNew = document.createElement('div');
  containerNew.id = POPOVER_DIALOG_ELEMENTID;
  containerNew.className = 'top-auto left-auto right-[112px] bottom-[103px]';
  containerNew.popover = 'manual';
  document.body.appendChild(containerNew);

  root = createRoot(containerNew);
  root.render(popoverDialogElment);
  containerNew.showPopover();
}

let root: Root | null = null;
