.menu-list {
  display: flex;
  flex-wrap: wrap;
  height: calc(100% - 105px);
  .menu-item {
    cursor: pointer;
    display: flex;
    flex: none;
    width: 25%;
    max-height: 110px;
    flex-direction: column;
    align-items: center;
    margin-top: 16px;
    margin-bottom: 0;
    position: relative;
    :global {
      .install {
        background: '#4CAF50';
        color: '#fff';
        position: 'absolute';
        bottom: '30px';
      }
    }
    &.uninstall {
      cursor: default;
    }
    .shareIcon {
      width: 60px;
      height: 60px;
      margin-bottom: 8px;
      border-radius: 8px;
    }
    .icon {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      margin-bottom: 8px;
    }
    .badge {
      position: absolute;
      border-radius: 8px;
      background-color: red;
      padding: 1px 4px;
      top: 2px;
      left: 50%;
      margin-left: 32px;
      transform: translateX(-50%);
      color: #fff;
      font-size: 13px;
      height: 16px;
      line-height: 14px;
      font-weight: 600;
    }
    .txt {
      opacity: 1;
      color: rgba(0, 0, 0, 0.65);
      font-size: 12px;
      letter-spacing: 0px;
      text-align: center;
      width: 90px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    &:hover {
      .txt {
        color: #6799ff;
      }
    }
  }
}
