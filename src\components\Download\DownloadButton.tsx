import Permission from '@/const/permission';
import useUserStore from '@/store/useUserStore';
import { Button } from 'antd';
import type { PropsWithChildren } from 'react';
import { checkPermission } from '../AccessVerify';

interface Props extends PropsWithChildren {
  type?: 'primary' | 'default' | 'link' | 'text' | 'dashed';
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
}

const Component = ({ type, className, disabled, onClick, children }: Props) => {
  const capableInfo = useUserStore((state) => state.capableInfo);

  return (
    <Button
      type={type || 'primary'}
      className={className}
      disabled={
        disabled || !capableInfo.capable || !checkPermission(Permission.LIBRARY_ONE_CLICK_DOWNLOAD)
      }
      onClick={onClick}
    >
      {children}
    </Button>
  );
};
export default Component;
