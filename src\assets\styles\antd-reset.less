.ant-table-wrapper {
  /** 选择表格 **/
  &.select-table {
    .ant-table-thead {
      .ant-table-cell {
        background: var(--ant-table-hd-select-color) !important;
      }
    }
    .ant-table-tbody {
      tr.ant-table-row:nth-of-type(2n) {
        td {
          background: var(--ant-table-select-even-row-bg-color) !important;
        }
      }
    }
  }
  .ant-table-tbody {
    tr.ant-table-row:nth-of-type(2n) {
      td {
        background: var(--ant-table-even-row-bg-color);
      }
    }
    .ant-table-row {
      &.row-selected {
        .ant-table-cell {
          color: var(--ant-table-select-row-color);
        }
      }
    }
  }
  .ant-table-pagination.ant-pagination {
    margin: var(--ant-margin) 15px;
  }
}
.ant-btn-primary:disabled,
.ant-btn-primary.ant-btn-disabled {
  color: var(--ant-button-primary-color);
  background: #3d5afe69;
  box-shadow: var(--ant-button-primary-shadow);
  border-color: transparent;
}
.ant-btn-primary.ant-btn-background-ghost:disabled,
.ant-btn-primary.ant-btn-background-ghost.ant-btn-disabled {
  color: #3d5afe69;
  background: var(--ant-button-ghost-bg);
  border-color: #3d5afe69;
}
.ant-btn-primary.ant-btn-dangerous.ant-btn-background-ghost:disabled,
.ant-btn-primary.ant-btn-dangerous.ant-btn-disabled {
  color: #ff4d4f70;
  background: var(--ant-button-ghost-bg);
  border-color: #ff4d4f70;
}
.anticon-caret-down {
  width: 24px;
  height: 12px;
  position: relative;
  svg {
    display: none;
  }
  &::after {
    position: absolute;
    display: block;
    content: ' ';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    background-image: url('@/assets/images/publicImg/xialaCHZ.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
}
.anticon-caret-up {
  width: 24px;
  height: 12px;
  position: relative;
  svg {
    display: none;
  }
  &::after {
    position: absolute;
    display: block;
    content: ' ';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    background-image: url('@/assets/images/publicImg/zhankaiCHZ.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
}
.ant-tree-switcher_open {
  width: 24px;
  height: 12px;
  position: relative;
  svg {
    display: none;
  }
  &::after {
    position: absolute;
    display: block;
    content: ' ';
    top: 7px;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    background-image: url('@/assets/images/publicImg/shouqiCHZ.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
}
.ant-tree-switcher_close {
  width: 24px;
  height: 12px;
  position: relative;
  svg {
    display: none;
  }
  &::after {
    position: absolute;
    display: block;
    content: ' ';
    top: 7px;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    background-image: url('@/assets/images/publicImg/zhankaiCHZ.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
}
.ant-dropdown .ant-table-filter-dropdown {
  // padding: 1px;
  border-radius: 8px;
}
.ant-table-wrapper .ant-table-filter-column {
  justify-content: flex-start !important;
  // justify-content: center !important;
}
.ant-table-wrapper .ant-table-column-title {
  flex-grow: 0;
  flex-shrink: 0;
  white-space: nowrap;
  word-break: keep-all;
}
.ant-table-wrapper .ant-table-cell-ellipsis .ant-table-column-title {
  overflow: visible;
}
.ant-table-tbody-virtual-scrollbar.ant-table-tbody-virtual-scrollbar-vertical {
  width: 8px !important;
}
.ant-empty-image {
  pointer-events: none;
}
