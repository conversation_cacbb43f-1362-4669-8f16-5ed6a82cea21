import MailBook, { AddressBookData } from '@/components/MailBook/MailBook';
import MultiModal from '@/components/MultiModal';
import { useEffect, useRef } from 'react';

interface Props {
  open: boolean;
  onCancel: () => void;
  onAdd: (val: AddressBookData[]) => void; // 数据回传方法
  selectDataList: any[];
}

const Component = ({ open, onCancel, onAdd, selectDataList }: Props) => {
  const mailBookRef = useRef<any>(null);

  useEffect(() => {
    if (open) {
      mailBookRef.current?.setSelectData(selectDataList);
    } else {
      mailBookRef.current?.resetSelectData();
    }
  }, [open]);

  return (
    <MultiModal
      layoutClassName="modalLeft"
      title={null}
      open={open}
      onCancel={onCancel}
      footer={null}
      closable={false}
    >
      <MailBook
        notAbsolute
        ref={mailBookRef}
        onClose={onCancel}
        onAdd={onAdd}
        selectDataList={selectDataList}
      ></MailBook>
    </MultiModal>
  );
};

export default Component;
