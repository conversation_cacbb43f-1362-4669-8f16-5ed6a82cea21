.chatWindow {
  // position: absolute;
  // top: 0;
  // width: 100%;
  // height: 100%;
  // z-index: 999;
  background: #fafafa;
  .header {
    display: flex;
    align-items: center;
    background: #fff;
    padding: 16px;
    justify-content: space-between;
    width: 100%;
    .title {
      color: rgba(0, 0, 0, 0.95);
      font-size: 18px;
    }
    button {
      color: #3d5afe;
      border: 1px #3d5afe solid;
      font-size: 12px;
      padding: 6px;
      border-radius: 4px;
    }
  }
  .messageList {
    overflow-y: auto;
    height: calc(100vh - 305px);
    p {
      word-break: break-all;
    }
  }
}
.chatCard {
  box-shadow: none !important;
  background: #fafafa;
  // background: rgba(0, 0, 0, 0.05);
  :global {
    .ant-card-meta-avatar {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      box-sizing: content-box;
      padding-right: 10px;
    }
    .ant-card-meta-title {
      color: rgba(0, 0, 0, 0.45);
      font-weight: 500;
      margin-bottom: 5px !important;
    }
    .ant-card-meta-description {
      max-width: 266px;
      background: #fff;
      box-sizing: border-box;
      border: 1px solid #c5cae9;
      border-radius: 8px;
      padding: 5px 10px;
      color: #000;
      float: left;
    }
    .AvatarBox > div span {
      font-size: 12px;
    }
  }
}
.selfMessageCard {
  :global {
    .ant-card-meta-avatar {
      padding-left: 10px;
      padding-right: 0;
    }
    .ant-card-meta {
      flex-direction: row-reverse;
      text-align: right;
    }
    .ant-card-meta-description {
      float: right;

      border-width: 0;
      background-color: #3d5afe;
      color: #fff;
    }
  }
}
.inputArea {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 16px;
  gap: 8px;
  box-sizing: border-box;
  input,
  button {
    height: 40px;
    box-sizing: border-box;
  }
  input {
    background: rgba(0, 0, 0, 0.05);
    border: 0.5px solid rgba(0, 0, 0, 0.05);
    padding: 9px 12px;
    border-radius: 4px;
  }
}
