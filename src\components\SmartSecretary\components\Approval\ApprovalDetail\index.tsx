import { queryDetailById } from '@/api/Approval';
import { approvalRes, creatProval } from '@/api/Approval/module';
import Drawer from '@/components/Drawer';
import MobileHeaderTitle from '@/components/SmartSecretary/publicComponents/Header';
import useUserStore from '@/store/useUserStore';
import { Empty } from 'antd';
import { FC, useEffect, useState } from 'react';
import EditMidCard from './cardItem/editMidCard';
import FirstCard from './cardItem/firstCard';
import LastCard from './cardItem/lastCard';
import LastHonerCard from './cardItem/lastHonerCard';
import LastMailCard from './cardItem/lastMailCard';
import MidCard from './cardItem/midCard';
import styles from './index.module.less';

const ApprovalDetail: FC<any> = ({ open, close, approvalId, approvalStatus }) => {
  const userInfo = useUserStore((state) => state.userInfo);
  const emptyData: creatProval = {
    initUserId: userInfo?.id as string,
    initUserName: userInfo?.username as string,
    initUserRealName: userInfo?.realName as string,
    configCode: '',
    approvalFullName: '',
    approvalName: '',
    content: '',
    startTime: '',
    endTime: '',
    approvalUserRealName: '',
    approvalUserName: '',
    fileReqVOList: [],
    mainId: '',
    middleContent: '',
    toUserId: '',
    toUserName: '',
    toUserRealName: '',
    configId: '',
    approvalUser: '',
    attendanceApproval: {
      userId: '',
      userName: '',
      userRealName: '',
      date: '',
      time: '',
      code: '',
      money: 0,
    },
  };
  const [approvalDetailShow, setApprovalDetailShow] = useState(false);
  const [dataList, setDataList] = useState<approvalRes[]>([]); //当前分类的数据列表
  const [status, setStatus] = useState(0); //0 查看审批状态  1发起审批状态  2中间审批状态
  const [approvalData, setApprovalData] = useState<creatProval>(emptyData);
  const [label, setLabel] = useState<string>('审批人');
  const fetchList = () => {
    queryDetailById({ mainId: approvalId }).then((res: any) => {
      if (res.data) {
        setDataList(res.data);
        if (approvalStatus === 2 && res.data[0]) {
          handleCard(res.data[0]);
        }
      }
    });
  };
  const handleCard = (data: approvalRes) => {
    console.log('data', data);
    if (data.currentStatus !== 1) return;
    if (userInfo?.username !== data.fromUserName) return;
    setLabel(data.fromUserLevel);
    setStatus(2); // 修改状态为中间审批
    setApprovalData({
      ...approvalData,
      configCode: data.configCode,
      approvalName: data.approvalName,
      approvalUser: data.fromUserId,
      approvalUserName: data.fromUserName,
      approvalUserRealName: data.fromUserRealName,
      mainId: data.mainId,
      approvalFullName: data.approvalFullName,
    });
  };
  useEffect(() => {
    setApprovalDetailShow(open);
    if (open) {
      console.log('approvalId', approvalId);
      console.log('approvalStatus', approvalStatus);
      setStatus(approvalStatus);
      fetchList();
    }
  }, [open]);

  //查看状态的第一张卡片
  const renderFirst = () => {
    if (dataList[0].currentStatus === 2) {
      return <MidCard approvalRes={dataList[0]}></MidCard>;
    } else {
      return <FirstCard approvalRes={dataList[0]}></FirstCard>;
    }
  };
  //查看和中间审批的中间卡片
  const renderMiddle = () => {
    if (dataList.length === 2) {
      return;
    } else {
      return dataList.slice(1, -1).map((item) => {
        return <MidCard approvalRes={item} key={item.id}></MidCard>;
      });
    }
  };
  //最后一张卡片
  const renderLast = () => {
    if (dataList[dataList.length - 1].configCode === '207') {
      return <LastMailCard approvalRes={dataList[dataList.length - 1]}></LastMailCard>;
    } else if (
      dataList[dataList.length - 1].configCode === '234' ||
      dataList[dataList.length - 1].configCode === '235'
    ) {
      return <LastHonerCard approvalRes={dataList[dataList.length - 1]}></LastHonerCard>;
    }
    return <LastCard approvalRes={dataList[dataList.length - 1]}></LastCard>;
  };
  return (
    <Drawer
      destroyOnClose={true}
      title={
        <MobileHeaderTitle
          title="审批管理"
          onCancel={() => {
            close();
          }}
        />
      }
      open={approvalDetailShow}
      onClose={() => {
        close();
      }}
    >
      <div className={styles.approvalDetailBox}>
        {/* 中间审批编辑卡 */}
        {status === 2 && <EditMidCard approvalData={approvalData} labelName={label}></EditMidCard>}
        {/* TODO  */}
        {/* 查看审批-触发编辑卡 */}
        {status === 0 && dataList.length > 0 && renderFirst()}
        {/* 查看审批-中间卡 */}
        {status !== 1 && dataList.length > 0 && renderMiddle()}
        {/* 查看审批-发起审批卡 */}
        {status !== 1 && dataList.length > 0 && renderLast()}
        {status === 0 && dataList.length === 0 && <Empty />}
      </div>
    </Drawer>
  );
};

export default ApprovalDetail;

export const isJSON = (str: string) => {
  try {
    JSON.parse(str);
  } catch (e) {
    // 转换出错，抛出异常
    return false;
  }
  return true;
};
export const getApprovalName = (item: any) => {
  if (item?.approvalName) {
    //考勤异常
    if (item.configCode && item.configCode.slice(0, 3) === '201' && isJSON(item.approvalName)) {
      return JSON.parse(item.approvalName).approvalContent || item.approvalFullName;
    } else {
      return item.approvalFullName;
    }
  } else {
    return item.approvalFullName;
  }
};
