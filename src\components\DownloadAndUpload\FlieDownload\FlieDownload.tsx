import NormalRang from '@/components/NormalRang';
import type { TableProps } from 'antd';
import { Select } from 'antd';
import { useEffect, useState } from 'react';
import SelectDownloadLocation from '../CustomDownload/SelectDownloadLocation/SelectDownloadLocation';
import CustomTable from '../CustomTable';
import styles from './FlieDownload.module.less';
interface DataType {
  key?: string;
  name?: string;
  title?: string;
  acount?: string;
  flieFormat?: string;
  fileSource?: string;
  level?: string;
  fileSize?: string;
  time?: string;
}
interface ModalProps {
  onCloseModal?: () => void; // 关闭弹框回调方法
  showFileChoose?: () => void; // 显示选择文件
  data?: DataType[];
}
const FlieDownload = (ModalProps: ModalProps) => {
  const [list, setList] = useState<DataType[]>([]);
  const [value, setValue] = useState('');
  const [openSelectDownloadLocation, setOpenSelectDownloadLocation] = useState(false);
  // 文库大全文件下载列表表头
  const FileChooseColumns: TableProps<DataType>['columns'] = [
    {
      title: '序号',
      dataIndex: 'key',
      key: 'key',
      width: 46,
      align: 'center',
      render: (_: any, record: DataType) => <span>{record.key}</span>,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 48,
      align: 'center',
      render: (_: any, record: DataType) => <span>{record.name}</span>,
    },

    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 267,
      align: 'left',
      ellipsis: true,
      render: (_: any, record: DataType) => <span>{record.title}</span>,
    },
    {
      title: '文件格式',
      dataIndex: 'flieFormat',
      key: 'flieFormat',
      width: 93,
      align: 'left',
      render: (_: any, record: DataType) => <span>{record.flieFormat}</span>,
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 93,
      align: 'left',
      render: (_: any, record: DataType) => <span>{record.fileSource}</span>,
    },

    {
      title: (
        <div
          className={styles.tableHeaderBut}
          onClick={() => {
            setList([]);
          }}
        >
          全部取消
        </div>
      ),
      key: 'action',
      width: 134,
      align: 'left',
      render: (_: any, record: DataType, index: number) => (
        <div className={styles.btnsAll}>
          <span>浏览</span>
          <span
            className={styles.nomalBut}
            onClick={() => {
              list.splice(index, 1);
              setList([...list]);
            }}
          >
            取消
          </span>
        </div>
      ),
    },
  ];
  useEffect(() => {
    console.log(ModalProps.data);
    setList(list.concat(ModalProps.data || []));
  }, [ModalProps.data]);
  return (
    <>
      <div className={styles.FlieDownload}>
        <SelectDownloadLocation
          open={openSelectDownloadLocation}
          onCloseModal={() => {
            setOpenSelectDownloadLocation(false);
          }}
          GetFilePosition={(value: string) => {
            console.log(value);
          }}
        />
        <div className={styles.header}>
          <div>
            <span>一键下载（裕邦操作系统版）</span>
            共计<span>{list.length}</span>个，已下载<span>2</span>个，剩余<span>8</span>个
          </div>
          <div
            className={styles.closeBut}
            onClick={() => {
              if (ModalProps && ModalProps.onCloseModal) {
                ModalProps.onCloseModal();
              }
            }}
          >
            关闭
          </div>
        </div>
        <div className={styles.chooesFileBox}>
          <div
            onClick={() => {
              if (ModalProps && ModalProps.showFileChoose) {
                ModalProps.showFileChoose();
              }
            }}
          >
            选择下载文件
          </div>
          <div>
            文件保存位置：
            <Select
              className={styles.selectItem}
              popupClassName={styles.popupClassName}
              placeholder="请选择"
              options={[
                {
                  label: 'C盘',
                  value: 'c',
                },
                {
                  label: 'D盘',
                  value: 'D',
                },
                {
                  label: 'E盘',
                  value: 'E',
                },
                {
                  label: '移动硬盘',
                  value: 'F',
                },
              ]}
            />
          </div>
        </div>
        <div className={styles.TableBox}>
          <CustomTable
            columns={FileChooseColumns}
            scroll={{ y: 420 }}
            pagination={false}
            dataSource={list}
            virtual
          />
        </div>
        <div className={styles.Range}>
          <NormalRang
            value={19}
            placeholder="下载进度"
            strokeColor="#3D5AFE"
            trailColor="#E8EAF6"
            total={80}
          />
        </div>
        <div className={styles.OperateButList}>
          <div
            className={styles.ConfirmBut}
            onClick={() => {
              setOpenSelectDownloadLocation(true);
            }}
          >
            确认下载
          </div>
        </div>
      </div>
    </>
  );
};

export default FlieDownload;
