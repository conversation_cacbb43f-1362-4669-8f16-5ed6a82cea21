.FlieDownload {
  width: 720px;
  min-height: 300px;
  box-sizing: border-box;
  border-radius: 4px;
  position: relative;
  * {
    box-sizing: border-box;
  }
  .header {
    width: 100%;
    height: 62px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 0 16px;
    & > div:first-of-type {
      display: flex;
      align-items: center;
      font-size: 14px;
      & > span {
        color: #e40026;
        &:first-of-type {
          font-size: 20px;
          color: #4d70fe;
          padding-right: 20px;
          font-weight: 600;
        }
      }
    }
    .closeBut {
      width: 60px;
      height: 32px;
      border-radius: 4px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #3d5afe;
      border: 1px solid #3d5afe;
      cursor: pointer;
    }
  }
  .chooesFileBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    padding: 0 8px;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    margin-bottom: 10px;
    > div:first-of-type {
      width: 116px;
      height: 32px;
      border-radius: 4px;
      background: #3d5afe;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      margin-right: 8px;
      font-size: 14px;
      cursor: pointer;
    }
    .selectItem {
      width: 160px;
      height: 32px;

      border: none !important;
      :global {
        .ant-select-selector,
        .ant-select-focused,
        .ant-select-outlined {
          border: none !important;
          box-shadow: none !important;
          background-color: #f3f3f5 !important;
          &:focus {
            border: none !important;
            box-shadow: none !important;
          }
          input {
            &:focus {
              border: none !important;
            }
          }
        }
      }
    }
  }
  .TableBox {
    width: 100%;
    height: 461px;
    box-sizing: border-box;
    padding: 0 8px;
    :global {
      .ant-table-wrapper table,
      .ant-table-container,
      .ant-table-container,
      .ant-table-wrapper .ant-table .ant-table-header {
        border-radius: 0 !important;
      }
      .ant-table-cell-scrollbar:not([rowspan]) {
        box-shadow: none;
      }
      .ant-table {
        .ant-table-thead > tr {
          border-radius: 0 !important;
          &:first-child > *:first-child,
          &:first-child > *:last-child {
            border-start-start-radius: 0;
            border-start-end-radius: 0;
          }
          > th {
            height: 40px;
            line-height: 40px;
            padding: 0 !important;
            font-size: 14px !important;
            font-weight: normal !important;
            background-color: #fff3e0 !important;
            &::before {
              height: 0 !important;
            }
          }
        }
        tr:nth-of-type(2n),
        div.ant-table-row:nth-of-type(2n) {
          background: #fffaf3;
        }
        td,
        .ant-table-cell {
          height: 40px;
          line-height: 40px;
          padding: 0 !important;
          font-size: 14px !important;
        }
        div.ant-table-cell {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          >span{
            width: 100%;
            text-align: left;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: keep-all;
          }
        }
        .ant-table-placeholder> div.ant-table-cell{
          height: auto;
        }
      }
    }
    .tableHeaderBut {
      width: 72px;
      height: 26px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 12px;
      border-radius: 4px;
      border: 1px solid #3d5afe;
      color: #3d5afe;
    }
    .btnsAll {
      width: 100%;
      display: flex;
      span {
        width: 44px;
        height: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
        border-radius: 4px;
        &:first-of-type {
          color: #3d5afe;
        }
      }
      .activedBut {
        background-color: #7986cb;
        color: #fff;
      }
      .nomalBut {
        border: 1px solid #3d5afe;
        color: #3d5afe;
      }
    }
  }
  .OperateButList {
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: center;
    padding: 7px 0 20px 0;
  }
  .ConfirmBut {
    width: 96px;
    height: 40px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #3d5afe;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
  }
  .Range{
    width: 680px;
    height: 20px;
    margin: 18px auto;
  }
}
.popupClassName {
  border: 1px solid #4d70f1;
  :global {
    .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
      background-color: #e8eaf6 !important;
    }
  }
}
