import { useState } from 'react';
import MainPanel from './MainPanel';
import SelectFiles from './SelectFiles';
import Context, { createUseMainPanelCtxStore, createUseSelectFilesCtxStore } from './Context';
import { Button } from 'antd';
import type { Config } from './Context';

interface Props {
  config: Config;
  onClose?: () => void;
}

const Component = ({ config, onClose }: Props) => {
  const [useMainPanelCtxStore] = useState(() => createUseMainPanelCtxStore({ ...config }));
  const [useSelectFilesCtxStore] = useState(() => createUseSelectFilesCtxStore({ ...config }));
  const [setMainPanelOpen] = useMainPanelCtxStore((state) => [state.setMainPanelOpen]);
  const [setSelectFilesOpen] = useSelectFilesCtxStore((state) => [state.setSelectFilesOpen]);
  const toolBarClick = (type: string) => {
    switch (type) {
      case 'selectFiles':
        setSelectFilesOpen(true);
        break;
    }
  };

  return (
    <Context.Provider
      value={{
        useMainPanelCtxStore,
        useSelectFilesCtxStore,
        config,
      }}
    >
      <Button
        type="primary"
        onClick={() => {
          setMainPanelOpen(true);
        }}
      >
        {config.buttonText}
      </Button>
      <MainPanel
        onCancel={() => {
          setSelectFilesOpen(false);
          setMainPanelOpen(false);
          if (onClose) {
            onClose();
          }
        }}
        onToolBarClick={toolBarClick}
      />
      <SelectFiles />
    </Context.Provider>
  );
};

export default Component;
