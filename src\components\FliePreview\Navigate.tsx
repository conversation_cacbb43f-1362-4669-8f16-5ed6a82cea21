import { getDiaryInfo } from '@/api/diary';
import { createDocVersion } from '@/api/editor';
import { queryAlbumListByPhotoId } from '@/api/magicAlbum';
import EditorContent, { EditorExposeProperts } from '@/pages/Editor/Main/Content';
import useFromModuleStore from '@/store/useFromModuleStore';
import useUserStore from '@/store/useUserStore';
import { getImg } from '@/utils/common';
import getMediaUrl from '@/utils/getMediaUrl';
import { Button, Flex, Modal } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './FilePreview.module.less';
import type { File } from './interface';

interface Props {
  file: File;
}

export default ({ file }: Props) => {
  const { userInfo } = useUserStore.getState();
  const { fileFormatType, fileType, source } = file;
  const history = useNavigate();
  const [btnLoading, setBtnLoading] = useState(false);
  // 图片提取文字
  const [getImgTxtLoading, setGetImgTxtLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [orcResult, setOrcResult] = useState<any>({}); //svg_content, text
  const { setFromModuleQuery } = useFromModuleStore((state: any) => state);
  // const [showColorPicker, setShowColorPicker] = useState(true);
  const fillColorRef = useRef('');
  const editRef = useRef<EditorExposeProperts>(null);
  console.log('file', file);
  const getImgTxt = async () => {
    const { accessToken } = useUserStore.getState();
    const domain = window.location.host;
    const data: any = {
      url: getImg(file.visitPath || ''),
    };
    let env = '';
    if (process.env.NODE_ENV === 'development' || domain.includes('client-dev')) {
      env = 'dev';
    } else if (domain.includes('client-sit')) {
      env = 'sit';
    } else if (domain.includes('client-uat')) {
      env = 'uat';
    }
    try {
      setGetImgTxtLoading(true);
      const result = await fetch(`/smartocr/uat/api/image_ocr/image_url `, {
        // pdf_url 生成pdf
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });
      if (result.ok) {
        const ocrRes = await result.json();
        setOrcResult(ocrRes.data);
        setIsModalOpen(true);
      } else {
        console.error('Failed to fetch:', result.statusText);
      }
    } catch (error) {
      setGetImgTxtLoading(false);
    } finally {
      setGetImgTxtLoading(false);
    }
  };
  useEffect(() => {
    if (isModalOpen && editRef.current) {
      editRef.current.setDOMToContent(orcResult.text);
      editRef.current?.autoFormat();
      setTimeout(() => {
        saveInWenKu();
      }, 200);
    }
  }, [isModalOpen]);
  const saveInWenKu = async () => {
    const dom = document.querySelector('.yubang-editor-container') as HTMLDivElement;
    const editorPDomList: any = dom?.querySelector('.yubang-editor-input');
    const JSONFromContent = await editRef.current?.getJSONFromContent();
    const createDocVersionResult = await createDocVersion({
      title: file.title + '图片提取文字结果',
      docFormat: 'ybd',
      docSource: 1, // 1裕邦编辑器
      content: JSONFromContent!,
      saveAs: true,
      htmlContent: editorPDomList.innerHTML,
    });
  };
  const routerAudioOrVideoPlayer = (isAduio: boolean) => {
    history(`${isAduio ? '/audioPlay/audioPlayDetail' : '/videoPlay/videoPlayDetail'}`, {
      state: {
        data: [file],
        id: file.id,
        index: 1,
        title: file.title,
        size: file.fileSize,
        filePath: file.filePath,
        url: getMediaUrl(file.visitPath || ''), //http://172.31.100.201:8080/testv.mp4
      },
    });
  };
  const queryAlbum = async () => {
    try {
      if (btnLoading) return;
      setBtnLoading(true);
      const res: any = await queryAlbumListByPhotoId({ userId: userInfo?.id, photoId: file.id });
      if (res.data && res.data.length) {
        const item = res.data[0];
        history('/magicAlbum/albumView', {
          state: {
            isTopping: false, // 用于判断是否为置顶模版
            albumsTemplateId: item.albumsTemplateId,
            senderUserId: item.senderUserId, // 判断是否来自分享
            albumsId: item.albumsId,
            albumsName: item.albumsName, // 为了带去修改相册回填相册名称
            renderList: [item],
            page: 1, //当前第几页
            pageSize: 1, //一页几条
            total: 0, //总数
            pageTotal: 0,
            index: 0, //当前页的第几个
          },
        });
      } else {
        console.log('图片没在相册里');
        history('/magicAlbum/albumView', {
          state: {
            isTopping: true, // 用于判断是否为置顶模版
            albumsTemplateId: 1,
            senderUserId: null, // 判断是否来自分享
            albumsId: null,
            albumsName: null, // 为了带去修改相册回填相册名称
            renderList: [{ albumsTemplateId: 1 }],
            page: 1, //当前第几页
            pageSize: 1, //一页几条
            total: 0, //总数
            pageTotal: 0,
            index: 0, //当前页的第几个
          },
        });
        // TODO 没在相册里，跳到通用模版的预览
      }
    } finally {
      setBtnLoading(false);
    }
  };

  const queryDiary = async () => {
    const res: any = await getDiaryInfo(file.bizId);
    console.log('res', res);
    history('/diary', { state: { diaryInfo: res.data } });
  };
  switch (fileFormatType) {
    case 'library_file_type_pic':
      return (
        <>
          <Button
            type="primary"
            loading={btnLoading}
            onClick={() => {
              queryAlbum();
            }}
          >
            去魔术相册
          </Button>
          <Button
            type="primary"
            loading={getImgTxtLoading}
            onClick={() => {
              getImgTxt();
            }}
            disabled={getImgTxtLoading}
          >
            提取文字
          </Button>

          <Modal
            title={
              <div className="flex items-center justify-between pr-24">
                <span>图片识别提取文字结果</span>
                <Button
                  type="primary"
                  ghost
                  onClick={() => {
                    setFromModuleQuery({
                      isSourceFromNetWorld: false,
                    });
                    history('/editor');
                  }}
                >
                  去裕邦编辑
                </Button>
              </div>
            }
            width={'100vw'}
            open={isModalOpen}
            onOk={() => {}}
            onCancel={() => {
              setIsModalOpen(false);
            }}
          >
            <Flex gap="large" className={styles.imgOcrBox}>
              <div
                className={styles.imgOcrItem}
                dangerouslySetInnerHTML={{ __html: orcResult.svg_content }}
              ></div>
              <div className={styles.imgOcrItem}>
                <EditorContent ref={editRef} />
              </div>
            </Flex>
          </Modal>
        </>
      );
    case 'library_file_type_audio':
      return (
        <Button
          type="primary"
          onClick={() => {
            routerAudioOrVideoPlayer(true);
          }}
        >
          去音频播放器
        </Button>
      );
    case 'library_file_type_video':
      return (
        <Button
          type="primary"
          onClick={() => {
            routerAudioOrVideoPlayer(false);
          }}
        >
          去视频播放器
        </Button>
      );
    case 'library_file_type_doc':
      if (fileType === 'yb') {
        return <Button type="primary">去裕邦编辑</Button>;
      } else if (fileType === 'ybd' && source === 5) {
        return (
          <Button
            type="primary"
            onClick={() => {
              queryDiary();
            }}
          >
            去我的日记
          </Button>
        );
      } else {
        return null;
      }
    default:
      return null;
  }
};
