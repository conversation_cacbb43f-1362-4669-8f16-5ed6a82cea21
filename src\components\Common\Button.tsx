import { ReactNode } from "react";

const PRESET_HEIGHT = {
  small: "18px",
  normal: "26px",
  large: "32px",
} as const;

const PRESET_BACKGROUD_COLOR = {
  orange:
    "linear-gradient(270deg, #FF8484 0%, #FF5A5A 100%),linear-gradient(270deg, #68B2FF 0%, #4D70FE 100%),linear-gradient(180deg, #FF9863 0%, #F16E42 100%)",
};

export default function Button({
  children,
  onClick,
  size = "normal",
}: {
  children: ReactNode;
  onClick?: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  size: keyof typeof PRESET_HEIGHT;
}) {
  const height = PRESET_HEIGHT[size];

  const handleClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    (onClick as React.MouseEventHandler<HTMLButtonElement>)?.(e);
  };

  return (
    <button type="button" style={{ height }} onClick={handleClick}>
      {children}
    </button>
  );
}
