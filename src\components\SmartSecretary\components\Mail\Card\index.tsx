import headerImage from '@/assets/images/account/avatar.png';
import groupAvatar from '@/assets/images/account/groupAvatar.png';
import AvatarText from '@/components/Avatar';
import { getPublicImg, getWidth } from '@/utils/common';
import { parserHtmlToString } from '@/utils/parser';
import { Avatar, Badge, Button, Col, Flex, Row } from 'antd';
import { useContext } from 'react';
import { Context } from '../../../context';
import styles from './index.module.less';

const getSenderName = (data: any) => {
  return data.senderName + data.senderUserName;
};
const getFromName = (data: any) => {
  if (data.addresseeUserName) {
    return data.addresseeName + data.addresseeUserName;
  } else if (data.addresseeName) {
    return data.addresseeName + data.addressee;
  } else {
    return data.addressee;
  }
};
const getSendGroupName = (data: any) => {
  return `${data.groupName}(` + `${data.groupNum})`;
};
const getFromGroupName = (data: any) => {
  return `${data.groupName}(` + `${data.groupNum})`;
};

export const SendPreson = ({ item, index }: { item: any; index: number }) => {
  const { goDetail } = useContext(Context);
  return (
    <Flex vertical className={styles.content}>
      <Row>
        <Col className="mr-3 flex w-14 items-center justify-center">
          {/* <Badge dot={item.readFlag === 0 ? true : false}> */}
          <Avatar
            shape="square"
            style={{
              width: getWidth(48),
              height: getWidth(48),
              objectFit: 'cover',
            }}
            src={
              item.addresseeAvatar ? (
                getPublicImg(item.addresseeAvatar)
              ) : (
                <AvatarText userName={item.addresseeName} Avatar={''} isUser={true} />
              )
            }
          />
          {/* </Badge> */}
        </Col>
        <Col flex="auto">
          <Flex vertical>
            <Flex justify="space-between">
              <span className={styles.mark}>{item.mark}</span>
              <Flex>
                <Button
                  type="primary"
                  size={'small'}
                  onClick={() => {
                    goDetail(item, index);
                  }}
                >
                  查看
                </Button>
              </Flex>
            </Flex>
            <div className="mt-1 w-64 overflow-hidden text-ellipsis whitespace-nowrap text-sm">
              {getFromName(item)}
            </div>
          </Flex>
        </Col>
      </Row>
      {getBottom(item)}
    </Flex>
  );
};

export const FromPreson = ({ item, index }: { item: any; index: number }) => {
  const { goDetail } = useContext(Context);
  return (
    <Flex vertical className={styles.content}>
      <Row>
        <Col className="mr-3 flex w-14 items-center justify-center">
          <Badge dot={item.readFlag === 0 ? true : false}>
            <Avatar
              shape="square"
              style={{
                width: getWidth(48),
                height: getWidth(48),
                objectFit: 'cover',
              }}
              src={getPublicImg(item.senderAvatar) || headerImage}
            />
          </Badge>
        </Col>
        <Col flex="auto">
          <Flex vertical>
            <Flex justify="space-between">
              <span className={styles.mark}>{item.mark}</span>
              <Flex>
                {getIgnoringFlag(item.ignoringFlag)}
                <Button
                  type="primary"
                  size={'small'}
                  onClick={() => {
                    goDetail(item, index);
                  }}
                >
                  查看
                </Button>
              </Flex>
            </Flex>
            <div className="mt-1 w-64 overflow-hidden text-ellipsis whitespace-nowrap text-sm">
              {getSenderName(item)}
            </div>
          </Flex>
        </Col>
      </Row>
      {getBottom(item)}
    </Flex>
  );
};

export const SendGroup = ({ item, index }: { item: any; index: number }) => {
  const { goDetail } = useContext(Context);
  return (
    <Flex vertical className={styles.content}>
      <Row>
        <Col className="mr-3 flex w-14 items-center justify-center">
          <Badge dot={item.readFlag === 0 ? true : false}>
            <Avatar
              shape="square"
              style={{
                width: getWidth(48),
                height: getWidth(48),
                objectFit: 'cover',
              }}
              src={getPublicImg(item.groupAvatar) || groupAvatar}
            />
          </Badge>
        </Col>
        <Col flex="auto">
          <Flex vertical>
            <Flex justify="space-between">
              <span className={styles.mark}>{item.mark}</span>
              <Flex>
                <Button
                  type="primary"
                  size={'small'}
                  onClick={() => {
                    goDetail(item, index);
                  }}
                >
                  查看
                </Button>
              </Flex>
            </Flex>
            <div className="mt-1 w-64 overflow-hidden text-ellipsis whitespace-nowrap text-sm">
              {getSendGroupName(item)}
            </div>
          </Flex>
        </Col>
      </Row>
      {getBottom(item)}
    </Flex>
  );
};

export const FromGroup = ({ item, index }: { item: any; index: number }) => {
  const { goDetail } = useContext(Context);
  return (
    <Flex vertical className={styles.content}>
      <Row>
        <Col className="mr-3 flex w-14 items-center justify-center">
          <Badge dot={item.readFlag === 0 ? true : false}>
            <Avatar
              shape="square"
              className={styles.avatar}
              style={{
                width: getWidth(48),
                height: getWidth(48),
                objectFit: 'cover',
              }}
              src={getPublicImg(item.groupAvatar) || groupAvatar}
            />
          </Badge>
        </Col>
        <Col flex="auto">
          <Flex vertical>
            <Flex justify="space-between">
              <span className={styles.mark}>{item.mark}</span>
              <Flex>
                {getIgnoringFlag(item.ignoringFlag)}
                <Button
                  type="primary"
                  size={'small'}
                  onClick={() => {
                    goDetail(item, index);
                  }}
                >
                  查看
                </Button>
              </Flex>
            </Flex>
            <div className="mt-1 w-64 overflow-hidden text-ellipsis whitespace-nowrap text-sm">
              {getFromGroupName(item)}
            </div>
          </Flex>
        </Col>
      </Row>
      {getBottom(item)}
    </Flex>
  );
};

const getBottom = (item: any) => {
  return (
    <Row className={styles.titName}>
      <Col className="mr-3">
        <span className={styles.sysSpan}>裕邦邮箱</span>
      </Col>
      <Col flex="auto">
        {item.appAttachRefDOS && item.appAttachRefDOS.length > 0 ? (
          <span>{getEasName(item.appAttachRefDOS[0].attachmentName)}</span>
        ) : item.groupFlag ? (
          <span>
            {item.senderName}: {getEasName(item.mailTitle || parserHtmlToString(item.mailContent))}
          </span>
        ) : (
          <span>{getEasName(item.mailTitle || parserHtmlToString(item.mailContent))}</span>
        )}
      </Col>
    </Row>
  );
};

const getEasName = (data: any) => {
  if (data.length <= 15) {
    return data;
  } else {
    return data.slice(0, 12) + '...';
  }
};

const getIgnoringFlag = (data: any) => {
  return data === 1 ? (
    <Button
      size={'small'}
      style={{ color: '#3D5AFE', background: '#fff', borderColor: '#3D5AFE' }}
      disabled
      className="mr-2"
    >
      已忽略
    </Button>
  ) : (
    <Button size={'small'} className="mr-2">
      忽略
    </Button>
  );
};
