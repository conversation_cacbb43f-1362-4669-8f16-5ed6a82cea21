import { getDataByResult, queryAllData, queryGroupByPage } from '@/api/addressBook';
import noDataPng from '@/assets/images/addressBook/nobody.png';
import Avatar from '@/components/Avatar';
import VideoCall from '@/components/VideoCall';
import VoiceCall from '@/components/VoiceCall';
import MoreMember from '@/pages/addressBook/components/MoreMember';
import useUserStore from '@/store/useUserStore';
import { getPublicImg } from '@/utils/common';
import { smartSecretaryEmitter } from '@/utils/emitters';
import getCamera from '@/webRTC/getCamera';
import { Button, Flex, Tooltip, Typography } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './index.module.less';

const AddressBook = forwardRef((props, ref) => {
  const moreRef = useRef<any>(null);
  useImperativeHandle(ref, () => ({}));
  const [voiceCall, setVoiceCall] = useState<boolean>(false);
  const [videoCall, setVideoCall] = useState<boolean>(false);
  const [addressList, setAddressList] = useState<
    {
      addressee: string;
      mobile: string;
      realName: string;
      addresseeStatus: number;
      addresseeName: string;
      userId: string;
      avatar: string;
    }[]
  >([]);
  const navigate = useNavigate();
  const [pageNo, setPageNo] = useState<number>(1);
  const latestPageNo = useRef(pageNo);
  const [total, setTotal] = useState<number>(0);
  const [lastType, setLastType] = useState<any>('lookAll'); // 记录展示类型
  const [shareLoading, setShareLoading] = useState(false); // 分享中
  const [dataPage, setDataPage] = useState<number>(1);
  const [queryParams, setQueryParams] = useState({
    pageNo: 1,
    pageSize: 1000,
    keyWordList: '',
  });
  interface Item {
    id: string;
    key: string;
    contactName: string;
    username: string;
    mobile: string;
    email: string;
    isEdit: boolean;
    [key: string]: any;
  }
  const [contactList, setContactList] = useState<Item[]>([]);
  const { userInfo } = useUserStore.getState();

  // 获取好友列表
  const queryDataList = () => {
    const obj: any = {
      ...queryParams,
      pageNo: dataPage,
    };
    const api =
      lastType === 'lookFrinds'
        ? getDataByResult
        : lastType === 'lookGroup'
          ? queryGroupByPage
          : queryAllData;

    if (lastType === 'lookGroup') {
      obj.keyWordList = queryParams.keyWordList?.split(',');
    }
    if (lastType === 'lookAll') {
      obj.keyWord = queryParams.keyWordList?.split(',');
    }
    api(obj)
      .then((res: any) => {
        setContactList([]);
        setTotal(res.data.total);
        setContactList(res.data.list);
      })
      .catch(() => {
        setContactList([]);
      });
  };

  const showPng = (item: any) => {
    const img = item.avatar || item.groupAvatar;
    if (img && img.includes('data:image/')) {
      return img;
    } else if (img) {
      return getPublicImg(img);
    } else {
      return '';
    }
  };

  // 获取更多群成员
  const getMoreMember = (id: string) => {
    moreRef.current?.openFn(id);
  };

  // 发送邮件
  const sendEmail = (item: any, type?: string) => {
    item.id = type ? item.groupId : item.id;
    navigate('/mailHomePage', {
      state: {
        fromModule: 'addressBook',
        type: type || 'person', // group
        content: { ...item },
      },
    });
  };

  useEffect(() => {
    queryDataList();
  }, [pageNo]);
  // 聊天
  const chatClick = (data: any) => {
    smartSecretaryEmitter.emit('changePage', { pageName: 'chatWindow', data });
  };
  return (
    <div className={styles.detailContainer}>
      <div className={styles.detailCenter}>
        <div className={styles.detailContent}>
          <div className={styles.contentRight}>
            {contactList?.length ? (
              contactList?.map((item) => {
                return (
                  <div key={item.id} className={styles.rightItem}>
                    <div className={styles.itemAvatar}>
                      <Avatar
                        userName={item.contactName || item.groupName}
                        Avatar={showPng(item)}
                        isUser={item.type !== 2}
                      />
                    </div>
                    {item.type === 2 ? (
                      <div className={styles.itemInfo}>
                        <div
                          className={`${styles.infoName} ${styles.infoGroupName}`}
                          title={item.contactName + ' (' + item.groupCount + ')人'}
                        >
                          <span className={styles.name} style={{ maxWidth: 200 }}>
                            {item.contactName}
                          </span>
                          {item.memberNames?.length && <span>&nbsp;({item.groupCount}人)</span>}
                        </div>
                        <div className={styles.infoDesc}>&nbsp;</div>
                        <div className={styles.infoDesc}>
                          <Typography.Paragraph
                            ellipsis={{
                              rows: 1,
                              expandable: 'collapsible',
                              expanded: false,
                              symbol: () => (
                                <span
                                  style={{
                                    padding: '0 4px',
                                    background: '#ccc',
                                    borderRadius: 4,
                                    color: '#000',
                                  }}
                                >
                                  更多
                                </span>
                              ),
                              onExpand: () => getMoreMember(item.groupId),
                            }}
                          >
                            {item.memberNames && <span className={styles.infoDesc}>群成员：</span>}
                            {item.memberNames?.slice(0, 10).map((e: any, index: number) => {
                              return (
                                <span key={index} className={styles.infoDesc}>
                                  {e}
                                  {index + 1 !== item.memberNames.length && <span>、</span>}
                                </span>
                              );
                            })}
                          </Typography.Paragraph>
                        </div>
                        <Flex gap={10} className={`${styles.options} mb-2 mt-2`}>
                          <Button className={styles.myBtn} onClick={() => chatClick(item)}>
                            聊天
                          </Button>
                          <Button onClick={() => sendEmail(item, 'group')} className={styles.myBtn}>
                            发邮件
                          </Button>
                          <Button disabled>语音通话</Button>
                          <Button disabled>视频通话</Button>
                        </Flex>
                      </div>
                    ) : (
                      <div className={styles.itemInfo}>
                        <div className={`${styles.infoPhone} ${styles.infoDesc}`}>
                          {item.username}
                        </div>
                        <div className={styles.infoName} title={item.contactName}>
                          <span className={styles.name}>{item.contactName}</span>
                          {item.contactType === 2 && item.blackFlag !== 1 && (
                            <span className={styles.outside}>外部</span>
                          )}
                          {item.blackFlag === 1 && <span className={styles.blackTag}>黑名单</span>}
                        </div>
                        <div className={styles.infoDesc}>电话：{item.mobile}</div>

                        <div className={`${styles.infoDesc} flex`}>
                          邮箱：
                          <Tooltip title={item.email}>
                            <span className={`${styles.emailArea}`}>{item.email}</span>
                          </Tooltip>
                        </div>
                        <Flex gap={10} className={`${styles.options} mb-2 mt-2`}>
                          <Button
                            className={`${item.blackFlag === 1 || item.contactType === 2 ? '' : styles.myBtn}`}
                            disabled={item.blackFlag === 1 || item.contactType === 2}
                            onClick={() => chatClick(item)}
                          >
                            聊天
                          </Button>
                          <Button
                            className={`${item.blackFlag === 1 ? '' : styles.myBtn}`}
                            onClick={() => sendEmail(item)}
                            disabled={item.blackFlag == 1}
                          >
                            发邮件
                          </Button>
                          <Button
                            className={`${item.blackFlag === 1 || item.contactType === 2 || item.username === userInfo?.username ? '' : styles.myBtn}`}
                            disabled={
                              item.blackFlag === 1 ||
                              item.contactType === 2 ||
                              item.username === userInfo?.username
                            }
                            onClick={async () => {
                              const result = await getCamera(true);
                              if (result.length === 0) {
                                return false;
                              }
                              setAddressList([
                                {
                                  addressee: item.contactName || item.username,
                                  mobile: item.mobile,
                                  realName: item.contactName || item.username,
                                  addresseeStatus: 1,
                                  addresseeName: item.email,
                                  userId: item.userId,
                                  avatar: item.avatar,
                                },
                              ]);
                              setVoiceCall(true);
                            }}
                          >
                            语音通话
                          </Button>
                          <Button
                            className={`${item.blackFlag === 1 || item.contactType === 2 || item.username === userInfo?.username ? '' : styles.myBtn}`}
                            disabled={
                              item.blackFlag === 1 ||
                              item.contactType === 2 ||
                              item.username === userInfo?.username
                            }
                            onClick={async () => {
                              const result = await getCamera(false);
                              if (result.length === 0) {
                                return false;
                              }
                              setAddressList([
                                {
                                  addressee: item.contactName || item.username,
                                  mobile: item.mobile || item.username,
                                  realName: item.contactName,
                                  addresseeStatus: 1,
                                  addresseeName: item.email,
                                  userId: item.userId,
                                  avatar: item.avatar,
                                },
                              ]);
                              setVideoCall(true);
                            }}
                          >
                            视频通话
                          </Button>
                        </Flex>
                      </div>
                    )}
                  </div>
                );
              })
            ) : (
              <>
                <div className={styles.noDataWrap}>
                  <img src={noDataPng} alt="" />
                  <p className={styles.noDataText}>{'暂无通讯'}</p>
                  <p className={styles.tip}>{'快去添加好友吧'}</p>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      <MoreMember ref={moreRef} />
      {voiceCall && (
        <VoiceCall
          open={voiceCall}
          title={'语音通话'}
          addressData={addressList}
          sourceType={2}
          onCancel={() => setVoiceCall(false)}
        ></VoiceCall>
      )}
      {videoCall && (
        <VideoCall
          open={videoCall}
          title={'视频通话'}
          addressData={addressList}
          onCancel={() => setVideoCall(false)}
          sourceType={2}
        ></VideoCall>
      )}
    </div>
  );
});

export default AddressBook;
