import { useEffect, useState, useMemo, useRef, useContext } from 'react';
import { Table, Button, Space } from 'antd';
import { formatQueryData } from './useCtxStore';
import { searchConditionAll, searchConditionResult } from '@/api/library';
import { getColumns } from './columns';
import { formatFileSizeFilterValue } from '@/components/Filters';
import FilePreview from '@/components/FliePreview';
import type { FilePreviewAPI } from '@/components/FliePreview';
import Context from '../Context';
import styles from './index.module.less';

const Component = () => {
  const filePreviewRef = useRef<FilePreviewAPI>();
  const { useSelectFilesCtxStore, useMainPanelCtxStore, config } = useContext(Context);
  const [
    selectFilesOpen,
    setColumnsFilterData,
    queryData,
    list,
    setList,
    selectedList,
    setSelectedList,
    selectedMap,
    setSelectedMap,
  ] = useSelectFilesCtxStore!((state) => [
    state.selectFilesOpen,
    state.setColumnsFilterData,
    state.queryData,
    state.list,
    state.setList,
    state.selectedList,
    state.setSelectedList,
    state.selectedMap,
    state.setSelectedMap,
  ]);
  const [selectedFileMap] = useMainPanelCtxStore!((state) => [state.selectedFileMap]);  
  const [loading, setLoading] = useState(false);
  const [pageNumber, setPageNumber] = useState({ current: 1 });
  const [pageSize] = useState(1000);
  const [total, setTotal] = useState(0);
  const [loadedList, setLoadedList] = useState<any[]>([]);
  const didMountRef = useRef(false);
  const columns = useMemo(() => {
    return getColumns(true, config).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>              
              {list.length === 0 && <span>操作</span>}
              {list.length > 0 && (
                <Button
                  type="primary"
                  size="small"
                  ghost={selectedList.length < list.length}
                  onClick={() => {
                    if (selectedList.length === list.length) {
                      const map = { ...selectedFileMap };
                      setSelectedMap(map);
                      setSelectedList(Object.values(map));
                    } else {
                      const map: any = {};
                      list.forEach((item: any) => {
                        map[item.id] = item;
                      });
                      setSelectedMap(map);
                      setSelectedList(Object.values(map));
                    }
                  }}
                >
                  全部选择
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          let element = null;
          if (selectedMap[row.id]) {
            element = (
              <Button
                type="primary"
                size="small"
                disabled={Boolean(selectedFileMap[row.id])}
                onClick={() => {
                  const map = { ...selectedMap };
                  delete map[row.id];
                  setSelectedMap(map);
                  setSelectedList(Object.values(map));
                }}
              >
                已选
              </Button>
            );
          } else {
            element = (
              <Button
                type="primary"
                size="small"
                ghost
                onClick={() => {
                  const map = { ...selectedMap };
                  map[row.id] = row;
                  setSelectedMap(map);
                  setSelectedList(Object.values(map));
                }}
              >
                多选
              </Button>
            );
          }
          return (
            <Space>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  preview(row);
                }}
              >
                浏览
              </Button>
              {element}
            </Space>
          );
        };
      }
      return item;
    });
  }, [list, selectedList, selectedMap, selectedFileMap]);
  const getList = () => {
    const api = queryData.length === 1 ? searchConditionAll : searchConditionResult;
    api(formatQueryData(queryData, { pageNumber, pageSize, config }), { setLoading }).then(
      ({ data }: any) => {
        const { total, list } = data;
        const nextLoadedList = [...loadedList, ...list];
        setTotal(total);
        setLoadedList(nextLoadedList);
        setList([...nextLoadedList]);
        if (total > pageSize * pageNumber.current) {
          setPageNumber((value) => ({ current: value.current + 1 }));
        }
      },
    );
  };
  const change = (pagination: any, filters: any, sorter: any, extra: any) => {
    const fileSize: any = filters.fileSize ? filters.fileSize[0] : {};
    switch (extra.action) {
      case 'filter':
        setColumnsFilterData({
          fileFormatTypeList: filters.fileFormatType ?? [],
          sourceList: filters.source ?? [],
          ...formatFileSizeFilterValue(fileSize),
        });
        break;
    }
  };
  const preview = (row: any) => {
    filePreviewRef.current?.open({ ...row });
  };
  useEffect(() => {
    if (selectFilesOpen) {
      const map = { ...selectedFileMap };
      setSelectedMap(map);
      setSelectedList(Object.values(map));
    }
  }, [selectFilesOpen, queryData, selectedFileMap]);
  useEffect(() => {
    if (!loadedList.length || loadedList.length < total) {
      getList();
    }
  }, [pageNumber]);
  useEffect(() => {
    if (didMountRef.current) {
      setTotal(0);
      setLoadedList([]);
      setList([]);
      setPageNumber({ current: 1 });
    } else {
      didMountRef.current = true;
    }
  }, [queryData]);

  return (
    <div className={styles.filesList}>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={list}
        loading={loading}
        rowKey={'id'}
        scroll={{ y: 491 }}
        pagination={false}
        onChange={change}
      />
      <FilePreview ref={filePreviewRef} />
    </div>
  );
};

export default Component;
