import { useMemo } from 'react';
import TimeFilter from './TimeFilter';

const Component = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  close,
  clearFilters,
  config,
}: any) => {
  const value = useMemo(() => {
    return selectedKeys[0] ?? {};
  }, [selectedKeys]);
  const change = (value: any) => {
    if (Object.keys(value).find((key) => value[key])) {
      setSelectedKeys([
        {
          ...value,
        },
      ]);
    } else {
      setSelectedKeys([]);
    }
    submit('ok'); // 为了change直接触发接口请求
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
      submit('ok');
    } else if (type === 'close') {
      close();
      config?.setFileTimeOpen(false);
    } else if (type === 'ok') {
      confirm({ closeDropdown: false });
      // close();
    }
  };
  return <TimeFilter value={value} onChange={change} onSubmit={submit} />;
};

export default Component;
