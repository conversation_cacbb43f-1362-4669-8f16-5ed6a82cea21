import request, { Extra } from '../index';
import { creatProval } from './module';
const base = '/web-api/approval';

// 查询审批查询列表接口
export const getApprovalListHome = (data: any, extra?: Extra) => {
  return request.post(
    {
      url: `${base}/manage/list`,
      data,
    },
    extra,
  );
};
// 查询审批列表
export const getApprovalList = (data: any, extra?: Extra) => {
  return request.post(
    {
      url: `${base}/manage/list/page`,
      data,
    },
    extra,
  );
};
// 获得审批类别配置树

export const getConfigTree = () => {
  return request.get({
    url: `${base}/config/getConfigTree`,
  });
};

//发起审批接口
export const create = (data: creatProval) => {
  return request.post({
    url: `${base}/manage/create`,
    data,
  });
};
//中间审批接口
export const approval = (data: creatProval) => {
  return request.post({
    url: `${base}/manage/approval`,
    data,
  });
};
//退回审批接口
export const back = (data: creatProval) => {
  return request.post({
    url: `${base}/manage/back`,
    data,
  });
};
//根据审批类别配置编码获取当前类别的审批数据
export const queryMainPageList = (data: any) => {
  return request.get({
    url: `${base}/manage/queryMainPageList`,
    params: data,
  });
};

//查询某个审批事项的审批明细数据
export const queryDetailById = (data: any) => {
  return request.get({
    url: `${base}/manage/queryDetailById`,
    params: data,
  });
};

//获取冒泡数
export const getBubbleCount = () => {
  return request.get({
    url: `${base}/config/getBubbleCount`,
  });
};

// 获得审批-考勤异常字典
export const getKqycDict = (data: any) => {
  return request.get({
    url: `admin-api/infra/dict-data/listByDictType`,
    params: data,
  });
};
