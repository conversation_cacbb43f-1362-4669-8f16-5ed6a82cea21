import { useEffect } from 'react';
import styles from './CustomModal.module.less';
interface ModalProps {
  isOpen: boolean; // 弹框状态
  mask?: boolean; // 是否显示遮罩
  onClose?: () => void; // 关闭弹框回调方法
  childenNode?: any;
  onAdd?: () => void; // 数据回传方法
  activeModalStyle?: any; // 弹框样式
  onFriendClick?: () => void; // 打开通讯录弹框方法
}
const CustomModal = (ModalProps: ModalProps) => {
  useEffect(() => {}, []);
  return (
    <>
      {ModalProps.isOpen ? (
        <div
          className={styles.CustomModal}
          onClick={(event:React.UIEvent<HTMLDivElement>) => {
            const className: string = (event.target as HTMLElement).className;
            if (className === styles.CustomModal) {
              //ModalProps .onClose&&ModalProps.onClose();
            }
          }}
        >
          <div className={styles.mask} />
          <div className={styles.ModalContent} onClick={() => {}}>
            {ModalProps.childenNode}
          </div>
        </div>
      ) : null}
    </>
  );
};

export default CustomModal;
