import type { TableProps } from 'antd';
import { useEffect, useState } from 'react';
import CustomTable from '../CustomTable';
import styles from './FileChoose.module.less';
interface DataType {
  key?: number;
  name?: string;
  title?: string;
  acount?: string;
  flieFormat?: string;
  fileSource?: string;
  level?: string;
  fileSize?: string;
  time?: string;
  checked?: boolean;
}
interface ModalProps {
  isOpen: boolean; // 弹框状态
  onClose?: () => void; // 关闭弹框回调方法
  setData?: (data: DataType[]) => void; // 将已选择数据传回父组件
}
const data: DataType[] = Array.from({ length: 100000 }, (_, index: number) => {
  return {
    key: index,
    name: '蔡依婷',
    acount: '16579586936',
    title: '董事长年会发言录音董事长年会发言录音董事长年会发言录音董事长年会发言录音',
    flieFormat: 'MP3',
    fileSource: '内部共享文件',
    level: '只读分享',
    fileSize: '12兆',
    time: '2024-07-17 11:29',
  };
});
const FileChoose = (ModalProps: ModalProps) => {
  const [list, setList] = useState<DataType[]>([]);
  const [flag, setFlag] = useState<boolean>(false);
  useEffect(() => {
    console.log(ModalProps);
    setList(
      data.map((item: DataType) => {
        item.checked = false;
        return item;
      }),
    );
  }, []);
  // 文库大全文件选择列表表头
  const FileChooseColumns: TableProps<DataType>['columns'] = [
    {
      title: '序号',
      dataIndex: 'key',
      key: 'key',
      width: 46,
      align: 'center',
      render: (_: any, record: DataType) => (
        <span className={record.checked ? `${styles.activedTr}` : ''}>{record.key}</span>
      ),
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 48,
      align: 'center',
      render: (_: any, record: DataType) => (
        <span className={record.checked ? `${styles.activedTr}` : ''}>{record.name}</span>
      ),
    },

    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 267,
      align: 'left',
      ellipsis: true,
      render: (_: any, record: DataType) => (
        <span className={record.checked ? `${styles.activedTr}` : ''}>{record.title}</span>
      ),
    },
    {
      title: '文件格式',
      dataIndex: 'flieFormat',
      key: 'flieFormat',
      width: 93,
      align: 'left',
      render: (_: any, record: DataType) => (
        <span className={record.checked ? `${styles.activedTr}` : ''}>{record.flieFormat}</span>
      ),
    },
    {
      title: '文件来源',
      dataIndex: 'fileSource',
      key: 'fileSource',
      width: 93,
      align: 'left',
      render: (_: any, record: DataType) => (
        <span className={record.checked ? `${styles.activedTr}` : ''}>{record.fileSource}</span>
      ),
    },

    {
      title: (
        <div
          className={styles.tableHeaderBut}
          onClick={() => {
            //alert(flag)
            if (!flag) {
              const temp = JSON.parse(JSON.stringify(list));
              temp.forEach((item: DataType) => {
                item.checked = true;
              });
              setList(temp);
              setFlag(true);
            } else {
              const temp = JSON.parse(JSON.stringify(list));
              temp.forEach((item: DataType) => {
                item.checked = false;
              });
              setList(temp);
              setFlag(false);
            }
          }}
        >
          {flag ? '全部取消' : '全部选择'}
        </div>
      ),
      key: 'action',
      width: 134,
      align: 'left',
      render: (_: any, record: DataType) => (
        <div className={styles.btnsAll}>
          <span>浏览</span>
          <span
            onClick={() => {
              record.checked = !record.checked;
              if (!record.checked) {
                setFlag(false);
              } else if (getCheckedData().length === list.length) {
                setFlag(true);
              }
              setList([...list]);
            }}
            className={record.checked ? `${styles.activedBut}` : `${styles.nomalBut}`}
          >
            {record.checked ? '已选' : '多选'}
          </span>
        </div>
      ),
    },
  ];
  const getCheckedData = () => {
    const tempData: DataType[] = list.filter((item: DataType) => {
      return item.checked;
    });
    return tempData;
  };
  return (
    <>
      {ModalProps.isOpen ? (
        <div className={styles.FileChoose + ' ' + styles.FlieDownload}>
          <div className={styles.header}>
            <div>
              <span>选择文件</span>
              共计<span>{list.length}</span>个，已选择<span>{getCheckedData().length}</span>个
            </div>
            <div
              className={styles.closeBut}
              onClick={() => {
                ModalProps.onClose && ModalProps.onClose();
              }}
            >
              关闭
            </div>
          </div>
          <div className={styles.searchBox}>
            <div className={styles.searchInput}>
              <span>姓名:</span>
              <input placeholder="请输入姓名" />
            </div>

            <div className={styles.searchInput}>
              <span>标题:</span>
              <input placeholder="请输入标题" />
            </div>
            <div className={styles.searchButton}>全部查询</div>
            <div className={styles.searchButton}>结果查询</div>
          </div>
          <div className={styles.TableBox}>
            <CustomTable
              columns={FileChooseColumns}
              scroll={{ y: 420 }}
              pagination={false}
              dataSource={list}
              virtual
            />
          </div>
          <div className={styles.OperateButList}>
            <div
              className={styles.ConfirmBut}
              onClick={() => {
                ModalProps.setData && ModalProps.setData(getCheckedData());
                // ModalProps.onClose && ModalProps.onClose();
              }}
            >
              确认选择
            </div>
          </div>
        </div>
      ) : null}
    </>
  );
};

export default FileChoose;
