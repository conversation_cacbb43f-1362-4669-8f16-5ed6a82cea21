import { CaretDownOutlined } from '@ant-design/icons';
import CommonSelect from './CommonSelect';
import EmployeePost from './EmployeePost';
import FileFormatType from './FileFormatType';
import FileFormatTypeByType from './FileFormatTypeByType';
import FileSize from './FileSize';
import FileSizeFilter from './FileSizeFilter';
import { formatFileSizeFilterValue } from './FileSizeFilter/utils';
import FileTime from './FileTime';
import FileTimeOther from './FileTimeOther';
import LocalSource from './LocalSource';
import MenuFilter from './MenuFilter';
import RemoveTime from './RemoveTime';
import SaftLevel from './SeftLevel';
import SoftWareType from './SoftWareType';
import Source from './Source';
import TimeFilter from './TimeFilter';
import { formatTimeFilterValue } from './TimeFilter/utils';
export {
  FileSizeFilter,
  formatFileSizeFilterValue,
  formatTimeFilterValue,
  MenuFilter,
  RemoveTime,
  TimeFilter,
};
export const getLocalSourceFilterProps = (config?: any) => {
  if (config?.setFileSourceOpen) {
    return {
      filterDropdown: (props: any) => {
        return <LocalSource {...props} config={config} />;
      },
      filterIcon: (
        <CaretDownOutlined className="h-4 w-8" onClick={() => config.setFileSourceOpen(true)} />
      ),
      filterDropdownOpen: config.fileSourceOpen,
      getPopupContainer: (triggerNode: { parentNode: any }) => {
        console.log(triggerNode.parentNode);
        return triggerNode.parentNode;
      },
    };
  }
  return {
    filterDropdown: (props: any) => {
      return <LocalSource {...props} config={config} />;
    },
    filterIcon: <CaretDownOutlined className="h-4 w-8" />,
  };
};

export const getFileFormatTypeFilterProps = (config?: any) => {
  if (config)
    return {
      filterDropdown: (props: any) => {
        return <FileFormatType {...props} config={config} />;
      },
      filterIcon: (
        <CaretDownOutlined
          className="h-4 w-8"
          onClick={() => {
            config.setFileFormatTypeOpen(true);
          }}
        />
      ),
      filterDropdownOpen: config.fileFormatTypeOpen,
    };
  return {
    filterDropdown: (props: any) => {
      return <FileFormatType {...props} config={config} />;
    },
    filterIcon: <CaretDownOutlined className="h-4 w-8" />,
  };
};

// 单独新增的文件格式，跟标准的区分
export const getFileFormatTypeByTypeFilterProps = (config?: any) => {
  return {
    filterDropdown: (props: any) => {
      return <FileFormatTypeByType {...props} config={config} />;
    },
    filterIcon: <CaretDownOutlined className="h-4 w-8" />,
  };
};

export const getSourceFilterProps = (config?: any) => {
  if (config)
    return {
      filterDropdown: (props: any) => {
        return <Source {...props} config={config} />;
      },
      filterIcon: (
        <CaretDownOutlined className="h-4 w-8" onClick={() => config.setFileSourceOpen(true)} />
      ),
      filterDropdownOpen: config.fileSourceOpen,
    };
  return {
    filterDropdown: (props: any) => {
      return <Source {...props} config={config} />;
    },
    filterIcon: <CaretDownOutlined className="h-4 w-8" />,
  };
};

export const getSaftLevelFilterProps = (config?: any) => {
  if (config)
    return {
      filterDropdown: (props: any) => {
        return <SaftLevel {...props} config={config} />;
      },
      filterIcon: (
        <CaretDownOutlined className="h-4 w-8" onClick={() => config.setSafeLevelOpen(true)} />
      ),
      filterDropdownOpen: config.safeLevelOpen,
    };
  return {
    filterDropdown: (props: any) => {
      return <SaftLevel {...props} config={config} />;
    },
    filterIcon: <CaretDownOutlined className="h-4 w-8" />,
  };
};

export const getFileSizeFilterProps = (list?: any[], config?: any) => {
  if (config) {
    return {
      filterDropdown: (props: any) => {
        return <FileSize {...props} resetList={list} config={config} />;
      },
      filterIcon: (
        <CaretDownOutlined className="h-4 w-8" onClick={() => config.setFileSizeOpen(true)} />
      ),
      filterDropdownOpen: config.fileSizeOpen,
    };
  }
  return {
    filterDropdown: (props: any) => {
      return <FileSize {...props} resetList={list} config={config} />;
    },
    filterIcon: <CaretDownOutlined className="h-4 w-8" />,
  };
};

export const getFileTimeFilterProps = (config?: any) => {
  if (config)
    return {
      filterDropdown: (props: any) => {
        return <FileTime {...props} config={config} />;
      },
      filterIcon: (
        <CaretDownOutlined className="h-4 w-8" onClick={() => config.setFileTimeOpen(true)} />
      ),
      filterDropdownOpen: config.fileTimeOpen,
    };
  return {
    filterDropdown: (props: any) => {
      return <FileTime {...props} config={config} />;
    },
    filterIcon: <CaretDownOutlined className="h-4 w-8" />,
  };
};
// 时间筛选-区别标准版本
export const getFileTimeFilterOtherProps = () => ({
  filterDropdown: (props: any) => {
    return <FileTimeOther {...props} />;
  },
  filterIcon: <CaretDownOutlined className="h-4 w-8" />,
});

export const getSoftWareTypeFilterProps = () => ({
  filterDropdown: (props: any) => {
    return <SoftWareType {...props} />;
  },
  filterIcon: <CaretDownOutlined className="h-4 w-8" />,
});

export const getEmployeePostFilterProps = () => ({
  filterDropdown: (props: any) => {
    return <EmployeePost {...props} />;
  },
  filterIcon: <CaretDownOutlined className="h-4 w-8" />,
});

// 登录回顾-文件格式
export const getFilterPropsByType = (list: any[]) => ({
  filterDropdown: (props: any) => {
    return <CommonSelect {...props} list={list} />;
  },
  filterIcon: <CaretDownOutlined className="h-4 w-8" />,
});
