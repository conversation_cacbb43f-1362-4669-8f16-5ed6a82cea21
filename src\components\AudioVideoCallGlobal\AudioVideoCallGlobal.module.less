.videoCallModal {
  min-width: 400px;
  position: fixed;
  bottom: 40px;
  right: 30px;
  display: flex;
  height: 83.97px;
  align-items: center;
  justify-content: space-between;
  z-index: 3000;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  gap: 9.329446792602539px;
  padding: 13.99px 18.66px;
  border: 0.58px solid rgba(0, 0, 0, 0.05);
  border-radius: 13.99px;
  box-shadow: 0px 0px 9.33px 0px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  .userInfo {
    display: flex;
    align-items: center;
    & > div {
      & > span {
      }
    }
  }
  .userAvatar {
    width: 60px;
    height: 60px;
    margin-right: 10px;
    border-radius: 8px;
  }
  .operateButList {
    display: flex;
    align-items: center;
    & > span {
      width: 37px;
      height: 37px;
      background-size: cover;
      cursor: pointer;
      font-size: 12px;
      color: #fff;
      border-radius: 50%;
      user-select: none;
      display: flex;
      align-items: center;
      justify-content: center;
      &:first-of-type {
        background-color: #fb5251;
        margin-right: 18px;
      }
    }
    .audioTb,.videoTb {
      background-color: #09C060;
    }
  }
}
.videoCallRingedModal{
  min-width: 400px;
  height: 51px;
  position: fixed;
  top: 40px;
  right: 30px;
  display: flex;
  height: 83.97px;
  align-items: center;
  z-index: 3000;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  gap: 9.329446792602539px;
  padding: 13.99px 18.66px;
  border: 0.58px solid rgba(0, 0, 0, 0.05);
  border-radius: 13.99px;
  box-shadow: 0px 0px 9.33px 0px rgba(0, 0, 0, 0.1);
  font-size: 19px;
  user-select: none;
  span{
    width: 24px;
    height: 24px;
    background-image: url('@/assets/images/audioPlay/<EMAIL>');
    background-size: cover;
  }

}
.videoMeetingModal {
  width: 306px;
  box-sizing: content-box !important;
  position: fixed;
  bottom: 40px;
  right: 30px;
  z-index: 3000;
  background-color: #fff;
  gap: 9.329446792602539px;
  padding: 24px 16px;
  border: 0.58px solid rgba(0, 0, 0, 0.05);
  border-radius: 13.99px;
  box-shadow: 0px 0px 9.33px 0px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  .userInfo {
    display: flex;
    margin-bottom: 16px;
    span {
      flex: none;
      width: 24px;
      height: 24px;
      background-image: url('../../assets/images/videoMeeting/cameraTb.png');
      background-size: cover;
      margin-right: 10px;
    }
  }
  .operateButList {
    text-align: right;
    .refusedBut {
      background-color: #ffffff;
      border-color: #3d5afe;
      color: #3d5afe;
      margin-right: 10px;
    }
  }
}
