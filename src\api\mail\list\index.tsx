import request from '../../index';
import { DeleteMailReq, sendEmailReq, sendModalEmailReq, ShareEmailReq } from './mailModels';

// 列表式-查询邮件列表
export const listMailQuery = (data: sendEmailReq) => {
  return request.post({
    url: '/web-api/mail/listQuery/page',
    data,
  });
};

// 分享邮件页面——选择邮件分页
export const listShareQuery = (data: sendModalEmailReq) => {
  return request.post({
    url: '/web-api/mail/listQuery/sharePage',
    data,
  });
};

export const shareEmail = (data: ShareEmailReq) => {
  return request.post({
    url: '/web-api/mail/sendEmail/shareEmail',
    data,
  });
};

export const deleteBatchMailInfo = (data: DeleteMailReq) => {
  return request.post({
    url: '/web-api/mail/data/deleteBatchMailInfo',
    data,
  });
};
