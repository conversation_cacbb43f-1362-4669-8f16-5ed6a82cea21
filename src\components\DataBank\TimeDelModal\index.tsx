import ModalDel from '@/components/DataBank/ModalDel';
import ModalTip from '@/components/DataBank/ModalTip';
import { getDays, months, source, years } from '@/components/Filters/TimeFilter/utils.tsx';
import { CalendarOutlined } from '@ant-design/icons';
import { Button, Cascader, Popover, Select, message } from 'antd';
import dayjs from 'dayjs';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import styles from './index.module.less';
interface Value {
  rangeStartYear?: number;
  rangeEndYear?: number;
  rangeStartMonth?: number;
  rangeEndMonth?: number;
  rangeStartDay?: number;
  rangeEndDay?: number;
  startTime?: any[];
  endTime?: any[];
}
type TimeType = 'startTime' | 'endTime' | 'timeRange' | '';
const content = (submit: any) => {
  const [rangeMonths, setRangeMonths] = useState<any[]>([]);
  const [rangeStartDays, setRangeStartDays] = useState<any[]>([]);
  const [rangeEndDays, setRangeEndDays] = useState<any[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<any[]>([]);
  const [value, setValue] = useState<Value>({});
  const timeSelectType = useRef<TimeType>('');
  const [messageApi, contextHolder] = message.useMessage();
  useEffect(() => {
    setValue(selectedKeys[0] ?? {});
  }, [selectedKeys]);
  const onChange = (value: any) => {
    if (Object.keys(value).find((key) => value[key])) {
      setSelectedKeys([
        {
          ...value,
        },
      ]);
    } else {
      setSelectedKeys([]);
    }
  };
  function formatTime(time: any) {
    return time.toString().padStart(2, 0);
  }
  const onSubmit = () => {
    let params = {};
    const keyList = Object.keys(value);
    if (keyList.includes('endTime')) {
      //之前添加，实则是结束时间
      timeSelectType.current = 'endTime';
      const [year, month, day] = value.endTime || [];
      const endTime = `${year}-${formatTime(month)}-${formatTime(day)}`;
      params = { endTime };
    } else if (keyList.includes('startTime')) {
      //之后添加，实则是开始时间
      timeSelectType.current = 'startTime';

      const [year, month, day] = value.startTime || [];
      const startTime = `${year}-${formatTime(month)}-${formatTime(day)}`;
      params = { startTime };
    } else if (keyList.includes('rangeStartYear')) {
      timeSelectType.current = 'timeRange';
      const {
        rangeStartYear,
        rangeStartMonth,
        rangeEndYear,
        rangeEndMonth,
        // rangeStartDay,
        // rangeEndDay,
      } = value;
      if (!rangeStartYear || !rangeEndYear || !rangeStartMonth || !rangeEndMonth) {
        messageApi.warning('请选择完整的年份和月份');
        return;
      }
      // 处理没选日的兼容
      const rangeStartDay = value.rangeStartDay || '01';
      const rangeEndDay = rangeEndMonth
        ? value.rangeEndDay || dayjs(`${rangeEndYear}-${rangeEndMonth}`).daysInMonth()
        : '31';
      const startTime = `${rangeStartYear}-${formatTime(rangeStartMonth)}-${formatTime(rangeStartDay)}`;
      const endTime = `${rangeEndYear}-${formatTime(rangeEndMonth)}-${formatTime(rangeEndDay)}`;
      //开始时间和结束时间
      params = { startTime, endTime };
    } else {
      timeSelectType.current = '';
    }
    submit(timeSelectType.current, params);
    console.log('数据-------------', params, timeSelectType.current);
  };

  return (
    <>
      {contextHolder}
      <div className={styles.timeFilter}>
        <div className={styles.item}>
          <div className={styles.line}>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择年份"
              options={years}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeStartYear}
              onChange={(rangeStartYear) => {
                const next: Value = {
                  rangeStartYear,
                  rangeEndYear: value.rangeEndYear,
                };
                if (!next.rangeEndYear || rangeStartYear > next.rangeEndYear) {
                  next.rangeEndYear = rangeStartYear;
                }
                onChange(next);
              }}
            />
            <span>到</span>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择年份"
              options={years}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeEndYear}
              onChange={(rangeEndYear) => {
                const next: Value = {
                  rangeStartYear: value.rangeStartYear,
                  rangeEndYear,
                };
                if (!next.rangeStartYear || rangeEndYear < next.rangeStartYear) {
                  next.rangeStartYear = rangeEndYear;
                }
                onChange(next);
              }}
            />
          </div>
          <div className={styles.line}>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择月份"
              options={rangeMonths}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeStartMonth}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setRangeMonths(value.rangeStartYear ? months : []);
                }
              }}
              onChange={(rangeStartMonth) => {
                const next: Value = {
                  rangeStartYear: value.rangeStartYear,
                  rangeEndYear: value.rangeEndYear,
                  rangeStartMonth,
                  rangeEndMonth: value.rangeEndMonth,
                };
                if (next.rangeStartYear === next.rangeEndYear) {
                  if (!next.rangeEndMonth || rangeStartMonth > next.rangeEndMonth) {
                    next.rangeEndMonth = rangeStartMonth;
                  }
                } else {
                  if (!next.rangeEndMonth) {
                    next.rangeEndMonth = 1;
                  }
                }
                onChange(next);
              }}
            />
            <span>到</span>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择月份"
              options={rangeMonths}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeEndMonth}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setRangeMonths(value.rangeEndYear ? months : []);
                }
              }}
              onChange={(rangeEndMonth) => {
                const next: Value = {
                  rangeStartYear: value.rangeStartYear,
                  rangeEndYear: value.rangeEndYear,
                  rangeStartMonth: value.rangeStartMonth,
                  rangeEndMonth,
                };
                if (next.rangeStartYear === next.rangeEndYear) {
                  if (!next.rangeStartMonth || rangeEndMonth < next.rangeStartMonth) {
                    next.rangeStartMonth = rangeEndMonth;
                  }
                } else {
                  if (!next.rangeStartMonth) {
                    next.rangeStartMonth = 1;
                  }
                }
                onChange(next);
              }}
            />
          </div>
          <div className={styles.line}>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择日期"
              options={rangeStartDays}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeStartDay}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setRangeStartDays(
                    value.rangeStartYear && value.rangeStartMonth
                      ? getDays(value.rangeStartYear, value.rangeStartMonth)
                      : [],
                  );
                }
              }}
              onChange={(rangeStartDay) => {
                const next: Value = {
                  rangeStartYear: value.rangeStartYear,
                  rangeEndYear: value.rangeEndYear,
                  rangeStartMonth: value.rangeStartMonth,
                  rangeEndMonth: value.rangeEndMonth,
                  rangeStartDay,
                  rangeEndDay: value.rangeEndDay,
                };
                if (
                  next.rangeStartYear === next.rangeEndYear &&
                  next.rangeStartMonth === next.rangeEndMonth
                ) {
                  if (!next.rangeEndDay || rangeStartDay > next.rangeEndDay) {
                    next.rangeEndDay = rangeStartDay;
                  }
                } else {
                  if (!next.rangeEndDay) {
                    next.rangeEndDay = 1;
                  }
                }
                onChange(next);
              }}
            />
            <span>到</span>
            <Select
              popupClassName={styles.selectPopup}
              virtual={false}
              placeholder="选择日期"
              options={rangeEndDays}
              suffixIcon={<CalendarOutlined />}
              value={value.rangeEndDay}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setRangeEndDays(
                    value.rangeEndYear && value.rangeEndMonth
                      ? getDays(value.rangeEndYear, value.rangeEndMonth)
                      : [],
                  );
                }
              }}
              onChange={(rangeEndDay) => {
                const next: Value = {
                  rangeStartYear: value.rangeStartYear,
                  rangeEndYear: value.rangeEndYear,
                  rangeStartMonth: value.rangeStartMonth,
                  rangeEndMonth: value.rangeEndMonth,
                  rangeStartDay: value.rangeStartDay,
                  rangeEndDay,
                };
                if (
                  next.rangeStartYear === next.rangeEndYear &&
                  next.rangeStartMonth === next.rangeEndMonth
                ) {
                  if (!next.rangeStartDay || rangeEndDay < next.rangeStartDay) {
                    next.rangeStartDay = rangeEndDay;
                  }
                } else {
                  if (!next.rangeStartDay) {
                    next.rangeStartDay = 1;
                  }
                }
                onChange(next);
              }}
            />
          </div>
          <div className={styles.describe}>
            <p>查询范围：</p>
            <p>
              从哪年哪月之间添加的文件，或从哪年哪月到哪年哪月之间添加的文件，或从哪年哪月哪日到哪年哪月哪日添加的文件。
            </p>
          </div>
        </div>
        <div className={styles.item}>
          <div className={styles.line}>
            <Cascader
              popupClassName={styles.cascaderPopup}
              placeholder="选择时间"
              options={source}
              suffixIcon={<CalendarOutlined />}
              value={[value.endTime as any]}
              onChange={(endTime) => {
                const next: Value = {
                  endTime,
                };
                onChange(next);
              }}
            />
            <span>之前添加</span>
          </div>
          <div className={styles.describe}>
            <p>查询范围：</p>
            <p>从哪年之前添加的文件，或从哪年哪月添加的文件，或从哪年哪月哪日前添加的文件。</p>
          </div>
        </div>
        <div className={styles.item}>
          <div className={styles.line}>
            <Cascader
              popupClassName={styles.cascaderPopup}
              placeholder="选择时间"
              options={source}
              suffixIcon={<CalendarOutlined />}
              value={[value.startTime as any]}
              onChange={(startTime) => {
                const next: Value = {
                  startTime,
                };
                onChange(next);
              }}
            />
            <span>之后添加</span>
          </div>
          <div className={styles.describe}>
            <p>查询范围：</p>
            <p>从哪年之后添加的文件，或从哪年哪月添加的文件，或从哪年哪月哪日后添加的文件。</p>
          </div>
        </div>
        <div className={styles.footer}>
          <Button type="primary" onClick={() => onSubmit()}>
            确定
          </Button>
          <Button type="text" onClick={() => {}}>
            清空
          </Button>
        </div>
      </div>
    </>
  );
};
interface childProps {
  confirmDelTime: any;
  from?: string; //来源 数据银行或者回收站
  text?: string; //按钮文字 如`按时间取消删除`
  operateType?: number; //操作类型
  moduleConfirmTpl?: any; //各模块自己定义的确认弹窗内容
  className?: any; // 自定义样式
  confirmText?: string;
  disabled?: boolean;
}
const Component = forwardRef(
  (
    {
      confirmDelTime,
      from = 'dataBank',
      text = '按时间删除',
      operateType = 1,
      moduleConfirmTpl,
      className,
      confirmText,
      disabled = false,
    }: childProps,
    ref: any,
  ) => {
    const [open, setOpen] = useState(false);

    const handleOpenChange = (val: boolean) => {
      setOpen(val);
    };
    const [isConfirmOpen, setIsConfirmOpen] = useState<any>(false);
    const [isTipOpen, setisTipOpen] = useState<any>(false);
    const [loading, setLoading] = useState(false);
    const accpetParams = useRef({});
    const [tips, setTips] = useState('');
    const [messageApi, contextHolder] = message.useMessage();
    // 全部删除
    const confirmDel = async () => {
      setLoading(true);
      confirmDelTime(accpetParams.current, operateType);
    };
    const callbackHandle = (successCb?: any) => {
      console.log('收到了');
      setLoading(false);
      setIsConfirmOpen(false);
      successCb?.(); // 成功回调
      setisTipOpen(true);
    };
    const submit = (type: any, params: any) => {
      let finallyParams = {};
      if (type == 'startTime') {
        finallyParams = { startTime: `${params.startTime} 00:00:00` };
      } else if (type == 'endTime') {
        finallyParams = { endTime: `${params.endTime} 23:59:59` };
      } else if (type == 'timeRange') {
        finallyParams = {
          startTime: `${params.startTime} 00:00:00`,
          endTime: `${params.endTime} 23:59:59`,
        };
      }
      console.log('type', type, params);
      accpetParams.current = finallyParams;

      let tips =
        type == 'startTime'
          ? `${params.startTime}之后全部数据`
          : type == 'endTime'
            ? `${params.endTime}之前全部数据`
            : `${params.startTime}到${params.endTime}全部数据`;
      if (!Object.keys(params).length) {
        tips = '全部数据';
      }
      if (params.startTime && params.startTime.indexOf(undefined) > -1) {
        messageApi.warning('请选择完整的时间范围');
        return;
      }
      setTips(tips);
      setIsConfirmOpen(true);
    };
    // 组织确认弹框的内容
    const confirmTpl =
      operateType == 2 ? (
        <div className="text-left">
          确认后，你的<span className="text-[#F4511E]">{tips}</span>将
          <span className="text-[#F4511E]">永久删除</span>，请谨慎选择
        </div>
      ) : operateType == 0 ? (
        <div className="text-left">
          确认后，你的<span className="text-[#F4511E]">{tips}</span>将移回原来的位置
        </div>
      ) : (
        <div className="text-left">
          确认后，你的<span className="text-[#F4511E]">{tips}</span>将放入
          <span className="text-[#3D5AFE]">回收站</span>
          ！你可以在回收站
          <span className="text-[#F4511E]">取消删除</span>
          或者<span className="text-[#F4511E]">永久删除</span>
        </div>
      );

    useImperativeHandle(ref, () => {
      return {
        callbackHandle,
      };
    }, [callbackHandle]);
    return (
      <div className={className}>
        {contextHolder}
        <Popover
          overlayClassName="timePopover"
          placement="bottom"
          arrow={false}
          trigger={'click'}
          open={open}
          content={() => content(submit)}
          onOpenChange={handleOpenChange}
        >
          <Button
            type="primary"
            className={`btn ml-2`}
            onClick={() => setOpen(true)}
            disabled={disabled}
          >
            {text}
          </Button>
        </Popover>
        <ModalDel
          isConfirmOpen={isConfirmOpen}
          confirmDel={confirmDel}
          setIsConfirmOpen={setIsConfirmOpen}
          loading={loading}
          title={`确认${text}吗？`}
          onConfirmText={confirmText}
        >
          {moduleConfirmTpl || confirmTpl}
        </ModalDel>
        <ModalTip
          isTipOpen={isTipOpen}
          setisTipOpen={setisTipOpen}
          title={operateType == 0 ? '取消删除成功' : '删除成功'}
        >
          <>
            {operateType == 0 && (
              <>
                你的全部数据已移回 <span className="text-[#F4511E]">原来的位置</span>
              </>
            )}
            {operateType == 1 && (
              <>
                你可以在回收站选择<span className="text-[#F4511E]">取消删除</span>
                或者 <span className="text-[#F4511E]">永久删除</span>
              </>
            )}
            {operateType == 2 && (
              <>
                回收站的全部数据已<span className="text-[#F4511E]">永久删除</span>
              </>
            )}
          </>
        </ModalTip>
      </div>
    );
  },
);

export default Component;
