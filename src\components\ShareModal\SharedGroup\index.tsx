import { groupCol } from '@/components/ShareGroup/columns';
import { getWidth } from '@/utils/common';
import { Button, Table } from 'antd';
import { FC, useContext } from 'react';
import styles from '../index.module.less';
import ShareContext from '../ShareContext';

interface GroupProps {
  dataSource: any[] | undefined;
}

const Component: FC<GroupProps> = ({ dataSource }) => {
  const { setGroup } = useContext(ShareContext);

  const removeGroup = (record: any) => {
    (setGroup as any)(dataSource?.filter((item) => item.id !== record.id));
  };

  const columns = [
    ...groupCol,
    {
      title: '操作',
      key: 'action',
      width: getWidth(80),
      render: (_: any, record: any) => (
        <Button
          type="primary"
          size={'small'}
          ghost
          onClick={() => {
            removeGroup(record);
          }}
        >
          取消
        </Button>
      ),
    },
  ];
  return (
    <div className={styles.list}>
      <div className={styles.title}>已选共享群({(dataSource || []).length})</div>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        scroll={{ x: 700, y: 163 }}
        pagination={false}
      />
    </div>
  );
};

export default Component;
