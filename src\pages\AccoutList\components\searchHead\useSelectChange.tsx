import { getBillPage, getMobileConfigNew, getNewMemberPageNew } from '@/api/account';
import childStyles from '@/components/DataBank/ModalSelect/index.module.less';
import useUserStore from '@/store/useUserStore';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

export const useSelectChange = (activeIndex: string, fromModule: string | undefined) => {
  const loginUserInfo = useUserStore((state) => state.userInfo);
  const [data, setData] = useState<any>([]);
  const [rightData, setRightData] = useState<any[]>([]);
  const [originSearch, setOriginSearch] = useState('');
  const [resultStore, setResultStore] = useState<any>({ pageNo: 1, pageSize: 100 });
  const [formSearchList, setFormSearchList] = useState<any>([]); // 弹窗搜索项
  // 查询条件对象
  const [queryInfo, setQueryStr] = useState<any>({
    pageNo: 1,
    pageSize: 10,
  });
  const [mobileStore, setMobileStore] = useState<any>([]); // 缓存手机号结果查询
  const [payChannelCode, setPayChannelCode] = useState<string>('');
  const [nameStore, setNameStore] = useState<any>([]); // 缓存名字结果查询
  const [countStore, setCountStore] = useState<any>([]); // 缓存账户结果查询

  // 删除弹窗筛选项
  const allSearchList: any = {
    1: [{ name: 'mobile', label: '工作手机', type: 'input', placeholder: '请输入' }],
    2: [{ name: 'mobile', label: '备用手机号', type: 'input', placeholder: '请输入' }],
    3: [
      { name: 'payChannelCode', label: '支付方式', type: 'input', placeholder: '请输入' },
      // { name: 'title', label: '备注', type: 'input', placeholder: '请输入' },
    ],
    4: [
      { name: 'realName', label: '姓名', type: 'input', placeholder: '请输入' },
      { name: 'username', label: '账户', type: 'input', placeholder: '请输入' },
    ],
  };
  // 选中
  const toggleData = (record: any, newValue: boolean) => {
    setData((prevData: any) => {
      return prevData.map((item: any) => {
        if (record.key === item.key && newValue) {
          return { ...item, isSelected: newValue };
        }
        return item;
      });
    });
  };
  // 取消
  const cancelSelectData = (record: any) => {
    setRightData((prevData) => {
      return prevData.filter((item) => item.key !== record.key);
    });
    setData((prevData: any) => {
      return prevData.map((item: any) => {
        if (record.key === item.key) {
          return { ...item, isSelected: false };
        }
        return item;
      });
    });
  };
  // 选择全部
  const selectAll = () => {
    setData((prevData: any) => {
      return prevData.map((item: any) => {
        return { ...item, isSelected: true };
      });
    });
  };
  // 取消全部
  const cancelAll = () => {
    setData((prevData: any) => {
      return prevData.map((item: any) => {
        return { ...item, isSelected: false };
      });
    });
    setRightData([]);
  };
  // 全部查询
  const queryAll = (val: any) => {
    if (activeIndex === '1' || activeIndex === '2') {
      val.mobile ? setMobileStore([val.mobile]) : setMobileStore([]);
    } else if (activeIndex === '3') {
      setPayChannelCode(val.payChannelCode);
    } else if (activeIndex === '4') {
      val.realName ? setNameStore([val.realName]) : setNameStore([]);
      val.username ? setCountStore([val.username]) : setCountStore([]);
    }
    setResultStore({ ...resultStore });
  };
  // 结果查询
  const queryResult = (val: any) => {
    if (activeIndex === '1' || activeIndex === '2') {
      val.mobile && setMobileStore([...mobileStore, val.mobile]);
    } else if (activeIndex === '3') {
      setPayChannelCode(val.payChannelCode);
    } else if (activeIndex === '4') {
      val.realName && setNameStore([...nameStore, val.realName]);
      val.username && setCountStore([...countStore, val.username]);
    }
    setResultStore({ ...resultStore }); // 触发更新
  };
  const [columns, setColumns] = useState([
    {
      title: '序号',
      dataIndex: 'key',
      key: 'key',
      width: '8.3%',
      align: 'center',
    },
    {
      title: '更改时间',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '手机号',
      dataIndex: 'title',
      key: 'title',
      align: 'center',
      ellipsis: true,
    },
    {
      title: (
        <div className={`${childStyles.close} ${childStyles.closeAdjust}`} onClick={selectAll}>
          全部选择
        </div>
      ),
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: (_: any, record: any) => (
        <div className={childStyles.btnsAll}>
          <div
            className={`${childStyles.close} ${record.isSelected ? childStyles.closeSelected : childStyles.closeCancel}`}
            onClick={() => toggleData(record, !record.isSelected)}
          >
            {record.isSelected ? '已选' : '多选'}
          </div>
        </div>
      ),
    },
  ]);
  const [rightColums, setRightColums] = useState<any>([]);
  const getDataAndCloums1 = () => {
    const obj = {
      ...resultStore,
      source: fromModule == 'recycle' ? 2 : 1, // 1-数据银行,2-回收站
      optType: 2, // 配置类型:1-配置备用手机,2-修改工作手机,
      mobile: [...mobileStore],
    };
    getMobileConfigNew(obj).then((res: any) => {
      setData(
        res.data.list.map((itm: any, index: number) => ({
          ...itm,
          isactiveIndex: false,
          key: index + 1,
        })),
      );
      setRightData([]);
    });
    setColumns([
      {
        title: '序号',
        dataIndex: 'key',
        key: 'key',
        width: '8.3%',
        align: 'center',
      },
      {
        title: '更改时间',
        dataIndex: 'createTime',
        key: 'createTime',
        align: 'center',
      },
      {
        title: '手机号',
        dataIndex: 'mobile',
        key: 'mobile',
        align: 'center',
        ellipsis: true,
      },
      {
        title: (
          <div className={`${childStyles.close} ${childStyles.closeAdjust}`} onClick={selectAll}>
            全部选择
          </div>
        ),
        dataIndex: 'updateTime',
        key: 'updateTime',
        align: 'center',
        render: (_: any, record: any) => (
          <div className={childStyles.btnsAll}>
            <div
              className={`${childStyles.close} ${record.isSelected ? childStyles.closeSelected : childStyles.closeCancel}`}
              onClick={() => toggleData(record, !record.isSelected)}
            >
              {record.isSelected ? '已选' : '多选'}
            </div>
          </div>
        ),
      },
    ]);
  };
  const getDataAndCloums2 = () => {
    const obj = {
      ...resultStore,
      source: fromModule == 'recycle' ? 2 : 1, // 1-数据银行,2-回收站
      optType: 1, // 配置类型:1-配置备用手机,2-修改工作手机,
      mobile: [...mobileStore],
    };
    getMobileConfigNew(obj).then((res: any) => {
      setData(
        res.data.list.map((itm: any, index: number) => ({
          ...itm,
          isSelected: false,
          key: index + 1,
        })),
      );
      setRightData([]);
    });
    setColumns([
      {
        title: '序号',
        dataIndex: 'key',
        key: 'key',
        width: '8.3%',
        align: 'center',
      },
      {
        title: '更改时间',
        dataIndex: 'createTime',
        key: 'createTime',
        align: 'center',
      },
      {
        title: '备用手机号',
        dataIndex: 'mobile',
        key: 'mobile',
        align: 'center',
        ellipsis: true,
      },
      {
        title: (
          <div className={`${childStyles.close} ${childStyles.closeAdjust}`} onClick={selectAll}>
            全部选择
          </div>
        ),
        dataIndex: 'updateTime',
        key: 'updateTime',
        align: 'center',
        render: (_: any, record: any) => (
          <div className={childStyles.btnsAll}>
            <div
              className={`${childStyles.close} ${record.isSelected ? childStyles.closeSelected : childStyles.closeCancel}`}
              onClick={() => toggleData(record, !record.isSelected)}
            >
              {record.isSelected ? '已选' : '多选'}
            </div>
          </div>
        ),
      },
    ]);
  };
  // 支付历史记录
  const getDataAndCloums3 = async () => {
    const obj = {
      ...resultStore,
      payStatusType: 1, // 0-未支付，1-非未支付，2-已支付或者已退款
      payChannelCode, //支付方式
      deleted: fromModule == 'recycle' ? 1 : 0, // 删除标识,0-未删除，1-临时删除，2-永久删除,示例值(1)
    };
    getBillPage(obj).then((res: any) => {
      const arr = res.data.list.filter((e: any) => e.status !== 0);
      setData(
        arr.map((itm: any, index: number) => ({
          ...itm,
          isSelected: false,
          key: index + 1,
        })),
      );
    });
  };
  const getDataAndCloums4 = () => {
    const obj = {
      ...resultStore,
      source: fromModule == 'recycle' ? 2 : 1, // 1-数据银行,2-回收站
      realName: [...nameStore],
      username: [...countStore],
    };
    getNewMemberPageNew(obj).then((res: any) => {
      setData(
        res.data.list.map((itm: any, index: number) => ({
          ...itm,
          key: index + 1,
          isSelected: false,
        })),
      );
      setRightData([]);
    });

    setColumns([
      {
        title: '序号',
        dataIndex: 'key',
        key: 'key',
        width: '8.3%',
        align: 'center',
      },
      {
        title: '子账户姓名',
        dataIndex: 'realName',
        key: 'realName',
        align: 'center',
      },
      {
        title: '子账户',
        dataIndex: 'username',
        key: 'username',
        align: 'center',
        ellipsis: true,
      },
      {
        title: <div>密文</div>,
        dataIndex: 'password',
        key: 'password',
        align: 'center',
        render: (_: any, record: any) => <span>******</span>,
      },
      {
        title: (
          <div className={`${childStyles.close} ${childStyles.closeAdjust}`} onClick={selectAll}>
            全部选择
          </div>
        ),
        dataIndex: 'updateTime',
        key: 'updateTime',
        align: 'center',
        render: (_: any, record: any) => (
          <div className={childStyles.btnsAll}>
            <div
              className={`${childStyles.close} ${record.isSelected ? childStyles.closeSelected : childStyles.closeCancel}`}
              onClick={() => toggleData(record, !record.isSelected)}
            >
              {record.isSelected ? '已选' : '多选'}
            </div>
          </div>
        ),
      },
    ]);
  };
  const getPageData = () => {
    switch (activeIndex) {
      case '1':
        getDataAndCloums1();
        break;
      case '2':
        getDataAndCloums2();
        break;
      case '3':
        getDataAndCloums3();
        setColumns([
          {
            title: '序号',
            dataIndex: 'key',
            key: 'key',
            width: '8.3%',
            align: 'center',
          },
          {
            title: <div>支付时间</div>,
            dataIndex: 'payTime',
            key: 'payTime',
            align: 'center',
            render: (_: any, record: any) => (
              <div>{record.payTime ? dayjs(record.payTime).format('YYYY-MM-DD HH:mm:ss') : ''}</div>
            ),
          },
          {
            title: <div>支付方式</div>,
            dataIndex: 'payChannelCode',
            key: 'payChannelCode',
            align: 'center',
            render: (_: any, record: any) => {
              return (
                <span>
                  {record.payChannelCode === 'alipay_qr'
                    ? '支付宝'
                    : record.payChannelCode === 'wx_native'
                      ? '微信'
                      : record.payChannelCode}
                </span>
              );
            },
          },
          // {
          //   title: '备注',
          //   dataIndex: 'remark',
          //   key: 'remark',
          //   align: 'center',
          //   ellipsis: true,
          // },
          {
            title: (
              <div
                className={`${childStyles.close} ${childStyles.closeAdjust}`}
                onClick={selectAll}
              >
                全部选择
              </div>
            ),
            dataIndex: 'updateTime',
            key: 'updateTime',
            align: 'center',
            render: (_: any, record: any) => (
              <div className={childStyles.btnsAll}>
                <div
                  className={`${childStyles.close} ${record.isSelected ? childStyles.closeSelected : childStyles.closeCancel}`}
                  onClick={() => toggleData(record, !record.isSelected)}
                >
                  {record.isSelected ? '已选' : '多选'}
                </div>
              </div>
            ),
          },
        ]);
        break;
      case '4':
        getDataAndCloums4();
        break;
    }
  };

  const resetTableFn = () => {
    setResultStore([{ pageNo: 1, pageSize: 100 }]);
  };

  useEffect(() => {
    const arr = [...columns];
    arr[arr.length - 1] = {
      title: (
        <div className={`${childStyles.close} ${childStyles.closeAdjust}`} onClick={cancelAll}>
          全部取消
        </div>
      ),
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: (_: any, record: any) => (
        <div className={childStyles.btnsAll}>
          <div
            className={`${childStyles.close} ${childStyles.closeCancel}`}
            onClick={() => cancelSelectData(record)}
          >
            取消
          </div>
        </div>
      ),
    };
    setRightColums([...arr]);
  }, [columns]);

  useEffect(() => {
    setOriginSearch('');
    setResultStore({ pageNo: 1, pageSize: 100 });
    setFormSearchList(allSearchList[activeIndex]);
    setRightData([]);
  }, [activeIndex]);

  useEffect(() => {
    getPageData();
  }, [resultStore]);

  return {
    data,
    rightData,
    columns,
    rightColums,
    queryResult,
    queryAll,
    setRightData,
    formSearchList,
    resetTableFn,
  };
};
