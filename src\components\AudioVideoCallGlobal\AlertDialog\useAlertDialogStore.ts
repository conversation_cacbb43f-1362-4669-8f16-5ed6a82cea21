import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create, createJSONStorage, devtools } from '@/store';
interface State {
  alertDialogOpen: boolean;
  alertDialogTitle: string;
  alertDialogButText: string;
}
interface SetState {
  setAlertDialogOpen: Setter;
  setAlertDialogTitle: Setter;
  setAlertDialogButText: Setter;
}
const useAlertDialogStore = autoUseShallow<State, SetState>(
  create(
    devtools(
      autoCreateSetters<State>({
        alertDialogOpen: false,
        alertDialogTitle: '',
        alertDialogButText: '',
      }),
      { name: 'alert-dialog', storage: createJSONStorage(() => sessionStorage) },
    ),
  ),
);

export default useAlertDialogStore;
