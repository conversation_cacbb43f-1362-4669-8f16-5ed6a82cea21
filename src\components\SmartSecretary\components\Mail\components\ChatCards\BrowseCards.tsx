import { chatSingle, updateReadFlag, updateReadFlagAndRevokeflag } from '@/api/mail/chat';
import { deepType, MailBookDataType, MailChatAttachVOList } from '@/api/mail/chat/mailModels';
import ediAudio from '@/assets/images/mail/ediAutio.png';
import ediVoice from '@/assets/images/mail/ediVoice.png';
import fromAttach from '@/assets/images/mail/fromAttach.png';
import fromContent from '@/assets/images/mail/fromContent.png';
import fromTitle from '@/assets/images/mail/fromTitle.png';
import sendAttach from '@/assets/images/mail/sendAttach.png';
import sendContent from '@/assets/images/mail/sendContent.png';
import sendTitle from '@/assets/images/mail/sendTitle.png';
import type { FilePreviewAPI } from '@/components/FliePreview';
import FliePreview from '@/components/FliePreview';
import useUserStore from '@/store/useUserStore';
import { formatDate } from '@/utils/date';
import { parserHtmlToString } from '@/utils/parser';
import { Button, Flex, message, Typography } from 'antd';
import classNames from 'classnames';
import { FC, useEffect, useRef, useState } from 'react';
import ReadFlag from '../Chat/ReadFlag/Index';
import Collapse from '../Collapse/Collapse';
import CollapseFiles from '../Collapse/CollapseFiles';
import { secondsToHMS } from '../EditCard/EditCard';
import Preview from '../Preview/Index';
import styles from './BrowserCards.module.less';
import CountDown from './CountDown/Index';
import ItemSpan from './ItemSpan/Index';

const { Paragraph } = Typography;

type ChildProps = {
  item: MailBookDataType;
  openEditorMail: (obj: deepType, data?: MailBookDataType) => void;
  sendAddress: string | undefined;
  showDetail?: boolean;
  isReply?: boolean;
};

/**
 * 卡片
 */
const BrowseCards: FC<ChildProps> = ({
  item,
  openEditorMail,
  sendAddress,
  showDetail = false,
  isReply = true,
}) => {
  item.sendType = item.senderAddress == sendAddress ? 'send' : 'from';

  const filePreviewRef = useRef<FilePreviewAPI>();
  const [data, setData] = useState<MailBookDataType>(item);
  const imgList =
    data.senderAddress == sendAddress
      ? [sendTitle, sendContent, sendAttach]
      : [fromTitle, fromContent, fromAttach];
  const [mailFile, setMailFile] = useState<MailChatAttachVOList[]>([]);
  const [contentFile, setContentFile] = useState<MailChatAttachVOList[]>([]);
  const [openReadFlag, setOpenReadFlag] = useState<boolean>(false);
  const [openReadFlagModal, setOpenReadFlagModal] = useState<boolean>(false);
  const [imageFile, setimageFile] = useState<MailChatAttachVOList[]>([]);

  useEffect(() => {
    if (data.mailChatAttachVOList?.length > 0) {
      setMailFile(
        data.mailChatAttachVOList.filter((item) => !item.mediaType || item.mediaType == 0),
      ); //普通
      setContentFile(
        item.mailChatAttachVOList.filter(
          (item) => item.mediaType && item.mediaType !== 0 && item.mediaType !== 3,
        ),
        // item.mailChatAttachVOList.filter((item) => !item.mediaType || item.mediaType !== '1'),
      ); //音视频正文
      setimageFile(
        item.mailChatAttachVOList.filter((item) => item.mediaType && item.mediaType === 3),
      );
    }
    if (data.senderAddress !== sendAddress && data.readFlag === 0) {
      setOpenReadFlag(true);
    }
  }, [data]);

  const [expanded, setExpanded] = useState<boolean>(false);
  const [showDetails, setShowDetails] = useState<boolean>(showDetail);
  const containerRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    setExpanded(showDetails);
  }, [showDetails]);

  useEffect(() => {
    if (containerRef.current && expanded && containerRef.current.offsetHeight > 80) {
      console.log('containerRef.current', containerRef.current.offsetHeight);
      const div = document.createElement('div');
      div.style.cssText = 'width:100%;display:flex;justify-content:flex-end;padding:10px';
      const button = document.createElement('button');
      button.textContent = '折叠';
      button.style.cssText =
        ' display: inline-block;width: 40px;height: 18px;border-radius: 4px;background: #d3d3d3;cursor: pointer; font-size: 12px;line-height: 18px;text-align: center;color: #4d4d4d;';
      button.onclick = () => {
        setExpanded(false);
      };
      div.appendChild(button);
      containerRef.current.append(div);
    }
  }, [expanded]);

  useEffect(() => {
    console.log('useEffect');

    if (
      data.senderAddress !== sendAddress &&
      data.readFlag === 0 &&
      data.confidentialityStatus !== 6 &&
      data.confidentialityStatus !== 7
    ) {
      updateReadFlag({ id: data.id as string, type: 1 }).then();
    }
  }, []);

  const editRevoke = () => {
    setOpenReadFlagModal(true);
  };
  const userInfo = useUserStore((state) => state.userInfo);

  const preview = (index: number) => {
    if (item.sendType === 'from' && data.revokeFlag === 1) {
      return message.info('该邮件已撤回，无法播放');
    }
    filePreviewRef.current?.open({
      id: contentFile[index].attachmentId || '302470223',
      title: contentFile[index].attachmentName,
      fileSize: contentFile[index].attachmentSize,
      visitPath: contentFile[index].attachmentUrl,
      fileType:
        contentFile[index].fileType || contentFile[index].attachmentName.split('.')[1] || '',
      fileFormatType: contentFile[index].fileFormatType,
      safeLevel: item.sendType == 'send' ? 0 : data.confidentialityStatus,
      filePath: contentFile[index].filePath || contentFile[index].attachmentUrl,
      userId: userInfo?.id,
      source: 0,
    });
  };

  const submitRevokeflag = () => {
    updateReadFlagAndRevokeflag({ id: data.id as string }).then((res: any) => {
      if (res.data) {
        setOpenReadFlag(false);
        setOpenReadFlagModal(false);
        chatSingle({ id: data.id as string, mailId: data.mailId as string }).then((res: any) => {
          setData(res.data as MailBookDataType);
        });
      }
    });
  };

  const [pre, setPre] = useState<any>({
    show: false,
    fileName: '',
    filePath: '',
  });
  const handleClick = (e: any) => {
    setPre({
      show: true,
      fileName: '无标题',
      filePath: e.target.getAttribute('src'),
    });
  };
  const handleImage = (val: any) => {
    setPre({
      show: true,
      fileName: val.attachmentName,
      filePath: val.attachmentUrl,
    });
  };

  useEffect(() => {
    if (showDetails) {
      setTimeout(() => {
        const parent = document.getElementById(`div-${data.id}`);
        const parentDiv = parent!.querySelector('#myDiv');
        const allImgs = parentDiv!.querySelectorAll('img');
        allImgs.forEach((img, index) => {
          img.addEventListener('click', handleClick);
        });
      }, 300);
    }
  }, [showDetails]);

  return (
    <div
      id={`div-${data.id}`}
      key={data.id as string}
      className={classNames(styles.card, data.senderAddress !== sendAddress ? '' : styles.send)}
    >
      <Flex vertical>
        <div className={styles.cardHeader}>
          <Flex className={styles.itemPar} justify={'space-between'}>
            <Flex>
              <span className={classNames(styles.editSpan, openReadFlag ? styles.marked : '')}>
                <span className={styles.textSpan}>
                  {data.senderAddress !== sendAddress ? '发件人' : '收件人'}
                </span>
                ：
              </span>
              {data.senderAddress !== sendAddress ? (
                <span>
                  {`${item.senderName ? item.senderName : ''}` +
                    `${item.username ? item.username : item.senderAddress}`}
                </span>
              ) : (
                <div className={styles.headerContent}>
                  <Collapse
                    items={[
                      ...data.addressList.map(
                        (i) =>
                          `${i.addresseeName ? i.addresseeName : ''}` +
                          `${i.username ? i.username : i.addressee}`,
                      ),
                      ...data.addressGroupList.map(
                        (i) =>
                          `${i.groupName}(${i.contactPersonVOS?.length ? i.contactPersonVOS.length : 0})`,
                      ),
                    ]}
                    expand={showDetails}
                  ></Collapse>
                </div>
              )}
            </Flex>
            <Flex justify={'flex-end'} align={'center'}>
              <span className={styles.titleSpan}>
                {data.senderAddress !== sendAddress ? '已收邮件' : '已发邮件'}
              </span>
            </Flex>
          </Flex>
          <Flex className={styles.itemPar} justify={'space-between'}>
            <Flex>
              <span className={styles.editSpan}>
                <span className={styles.textSpan}>{'时间'}</span>：
              </span>
              <span>{formatDate(data.sendTime, 'YYYY-MM-DD HH:mm:ss')}</span>
            </Flex>
            <Flex align={'center'}>
              {data.confidentialityStatus !== 0 && (
                <ItemSpan status={data.confidentialityStatus}></ItemSpan>
              )}
              {[4, 5, 7].includes(data.confidentialityStatus) &&
                !data.revokeFlag &&
                data.timedTime && (
                  <CountDown
                    duration={+data.timedTime}
                    currentTime={item.serverCurrentTime as number}
                    spanStyle={data.senderAddress !== sendAddress ? 'from' : ('send' as string)}
                  ></CountDown>
                )}
              <span className={styles.detailSpan} onClick={() => setShowDetails(!showDetails)}>
                {showDetails ? '隐藏' : '详情'}
              </span>
            </Flex>
          </Flex>
          {showDetails && (
            <>
              <Flex className={styles.itemPar}>
                <span className={styles.editSpan}>
                  <span className={styles.textSpan}>
                    {data.senderAddress === sendAddress ? '发件人' : '收件人'}
                  </span>
                  ：
                </span>
                {data.senderAddress === sendAddress ? (
                  <span>
                    {`${item.senderName ? item.senderName : ''}` +
                      `${item.username ? item.username : item.senderAddress}`}
                  </span>
                ) : (
                  <div className={styles.headerContent}>
                    <Collapse
                      items={[
                        ...data.addressList.map(
                          (i) =>
                            `${i.addresseeName ? i.addresseeName : ''}` +
                            `${i.username ? i.username : i.addressee}`,
                        ),
                        ...data.addressGroupList.map(
                          (i) =>
                            `${i.groupName}(${i.contactPersonVOS?.length ? i.contactPersonVOS.length : 0})`,
                        ),
                      ]}
                      expand={showDetails}
                    ></Collapse>
                  </div>
                )}
              </Flex>
              {(data.copyAddressList.length > 0 || data.copyAddressGroupList.length > 0) && (
                <Flex className={styles.itemPar}>
                  <span className={styles.editSpan}>
                    <span className={styles.textSpan}>抄送</span>：
                  </span>
                  <Flex justify={'flex-start'} align={'flex-start'} gap={'small'} wrap>
                    {data.copyAddressList.map((i) => {
                      return (
                        <span key={i.addressee}>
                          {`${i.addresseeName ? i.addresseeName : ''}` +
                            `${i.username ? i.username : i.addressee}`}
                        </span>
                      );
                    })}
                    {data.copyAddressGroupList.map((i) => {
                      return (
                        <span
                          key={i.groupId}
                        >{`${i.groupName}(${i.contactPersonVOS?.length ? i.contactPersonVOS.length : 0})`}</span>
                      );
                    })}
                  </Flex>
                </Flex>
              )}
              {data.secretAddressList.length > 0 && (
                <Flex className={styles.itemPar} justify={'flex-start'}>
                  <span className={styles.editSpan}>
                    <span className={styles.textSpan}>密送</span>：
                  </span>
                  <Flex justify={'flex-start'} align={'flex-start'} gap={'small'} wrap>
                    {data.secretAddressList.map((i) => {
                      return (
                        <span key={i.addressee}>
                          {`${i.addresseeName ? i.addresseeName : ''}` +
                            `${i.username ? i.username : i.addressee}`}
                          ;
                        </span>
                      );
                    })}
                  </Flex>
                </Flex>
              )}
            </>
          )}
        </div>
        <div className={styles.cardContent}>
          <Flex vertical>
            <Flex className={styles.itemPar} justify={'flex-start'} align={'center'}>
              <img className={styles.imgStyles} src={imgList[0]} alt="" />
              <span className={styles.titleBox}>{data.mailTitle}</span>
            </Flex>
            <Flex className={styles.itemPar} justify={'flex-start'}>
              <img className={styles.imgStyles} src={imgList[1]} alt="" />
              <div className={styles.contentBox}>
                {expanded ? (
                  <>
                    <div
                      id="myDiv"
                      className={styles.contentHtml}
                      ref={containerRef}
                      dangerouslySetInnerHTML={{ __html: item.mailContent }}
                    ></div>
                    {imageFile.length > 0 &&
                      imageFile.map((item) => {
                        return (
                          <img
                            src={item.attachmentUrl}
                            key={item.id}
                            onClick={() => {
                              handleImage(item);
                            }}
                          />
                        );
                      })}
                  </>
                ) : (
                  <Paragraph
                    className={styles.paragraph}
                    ellipsis={{
                      rows: 3,
                      expandable: 'collapsible',
                      expanded: expanded,
                      onExpand: (_, info) => setExpanded(info.expanded),
                      symbol: expanded ? (
                        <span className={styles.symbolSpan}>收起</span>
                      ) : (
                        <span className={styles.symbolSpan}>展开</span>
                      ),
                    }}
                  >
                    {parserHtmlToString(data.mailContent)}
                    {data.mailContent?.includes('<img') || imageFile.length > 0
                      ? ' [ 图片：点击详情展开图片 ] '
                      : ''}
                  </Paragraph>
                )}
              </div>
            </Flex>
            {contentFile.length > 0 &&
              contentFile.map((item, index) => {
                return (
                  <Flex className={styles.itemParc} key={item.attachmentId}>
                    <Flex align={'center'} gap={10}>
                      <img
                        src={item.mediaType == 1 ? ediVoice : ediAudio}
                        className={styles.icon}
                      ></img>
                      <span className={styles.title}>{item.attachmentName}</span>
                    </Flex>
                    <Flex align={'center'} gap={10}>
                      <span className={styles.duration}>
                        {secondsToHMS(item.mediaDuration as number)}
                      </span>
                      <Button className={styles.okButton} onClick={() => preview(index)}>
                        {'播放'}
                      </Button>
                    </Flex>
                  </Flex>
                );
              })}
            <Flex className={styles.itemPar} justify={'space-between'}>
              <Flex>
                <img className={styles.imgStyles} src={imgList[2]} alt="" />
                <div className={styles.annexBox}>
                  {mailFile.length > 0 && (
                    <CollapseFiles
                      sendType={item.sendType}
                      items={mailFile}
                      safeLevel={data.confidentialityStatus}
                      revokeFlag={data.revokeFlag}
                    ></CollapseFiles>
                  )}
                </div>
              </Flex>
              <Flex className={styles.annexSpan}>
                {isReply && (
                  <>
                    {data.senderAddress !== sendAddress &&
                      [6, 7].includes(data.confidentialityStatus) &&
                      data.revokeFlag == 0 && (
                        <span className={styles.redeSpan} onClick={editRevoke}>
                          已阅
                        </span>
                      )}
                    {data.senderAddress !== sendAddress && !data.isSystemSend && (
                      <span
                        className={styles.huifu}
                        onClick={() => {
                          openEditorMail({ index: 1 }, item);
                        }}
                      >
                        回复邮件
                      </span>
                    )}
                    {data.senderAddress === sendAddress && !data.isSystemSend && (
                      <span
                        className={styles.xufa}
                        onClick={() => {
                          openEditorMail({ index: 1 }, item);
                        }}
                      >
                        续发邮件
                      </span>
                    )}
                  </>
                )}
              </Flex>
            </Flex>
          </Flex>
        </div>
      </Flex>
      <FliePreview ref={filePreviewRef} zIndex={1000} />
      {pre.show && (
        <Preview
          fileName={pre.fileName}
          filePath={pre.filePath}
          onCancel={() => {
            setPre({ show: false, fileName: '', filePath: '' });
          }}
        ></Preview>
      )}
      {openReadFlagModal && (
        <ReadFlag
          type={item?.confidentialityStatus as number}
          close={() => setOpenReadFlagModal(false)}
          submit={submitRevokeflag}
        ></ReadFlag>
      )}
    </div>
  );
};

export default BrowseCards;
