/**
 * 用于树形结构的选择组件
 * 暂用于权限管理-配置审批
 *
 * 接口数据格式不一样暂时没有做成通用的
 */
import { getConfigTree } from '@/api/Approval';
import { getWidth } from '@/utils/common';
import { DownOutlined } from '@ant-design/icons';
import { Button, Input, List, Space, Tabs, Tree } from 'antd';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.module.less';

export interface FieldNames {
  value?: string;
  label?: string;
  groupLabel?: string;
  options?: string;
}

// 定义 approvalType 的类型
type ApprovalType = 1 | 2;
const typelist = {
  1: '公共审批',
  2: '特殊审批',
};

export interface TreeSelectProps {
  mode?: 'single' | 'mutiple';
  title: string;
  selectKeys?: any[]; // 已选中的审批配置项
  onConfirm: (data: any[]) => void; // 确定回调
  onReset: () => void; // 重置回调
  onClose?: () => void; // 关闭回调
  searchKey?: string; // 默认搜索关键字，暂未用到不处理
}

const TreeSelect = ({
  mode,
  title,
  searchKey,
  selectKeys,
  onClose,
  onConfirm,
  onReset,
}: TreeSelectProps) => {
  const [selectList, setSelectList] = useState<any[]>([]); // 已选的列表
  const [expandedKeys, setExpandedKeys] = useState<number[]>([]); // 展开的树
  const [actTab, setActTab] = useState<string | undefined>(); // 当前打开的tab
  const [searchValue, setSearchValue] = useState(searchKey || ''); // 模糊搜索的value
  const [rawData, setRawData] = useState<any[]>([]); // 接口返回原始的数据
  const [treeList, setTreeList] = useState([]); // 展平后的所有数据
  const [visible, setVisible] = useState(false); // 控制弹窗的显示
  const [lastSelectList, setLastSelectList] = useState<any>([]); // 保存打开弹窗前选择的列表（接口返回的不显示名称
  const componentRef = useRef<any>(null); // 组件最外层的ref
  const treeRef = useRef<any>(null);
  const footerRef = useRef<any>(null);
  const [treeHeight, setTreeHeight] = useState(0);

  useEffect(() => {
    if (treeList.length > 0) {
      const sels = selectKeys?.map((item) => item.approvalConfigId);
      const res = treeList.filter((item: any) => sels?.includes(item.id));
      setSelectList(res);
      setLastSelectList(res);
    }
  }, [selectKeys, treeList]);
  useEffect(() => {
    if (treeRef.current) {
      console.log('treeRef.current', treeRef.current.offsetTop);
      const componentTop = componentRef.current.getBoundingClientRect();
      const dataWrapperHeight = componentRef.current.querySelector('.testtress').offsetHeight;
      const footerHeight = footerRef.current.offsetHeight;
      const treeTop = treeRef.current.getBoundingClientRect();
      const height = dataWrapperHeight - footerHeight - (treeTop.top - componentTop.top) + 30;
      console.log('height', height);
      setTreeHeight(height);
    }
  }, [visible]);

  // 获取审批类别树
  const getList = () => {
    getConfigTree().then((res: any) => {
      const mainData = (res.data || []).flatMap((item: any) => item.tree || []);
      const list = getFlatList(mainData);
      setActTab(mainData ? mainData[0].approvalType.toString() : []);
      setTreeList(list);
      setRawData(res.data);
    });
  };
  useEffect(() => {
    getList();
  }, []);

  // 选择
  const selectClick = (data: any) => {
    const newList: number[] = mode === 'single' ? [data] : [...selectList].concat(data);
    setSelectList(newList);
  };
  // 取消选择
  const cancelSelectClick = (data: any) => {
    if (mode === 'single') {
      setSelectList([]);
    } else {
      setSelectList((prev) => prev.filter((item) => item.id !== data.id));
    }
  };

  // 展开/收起 事件
  const expandedKeysClick = (id: number) => {
    setExpandedKeys((prev) => {
      if (prev.includes(id)) {
        return prev.filter((item) => item !== id);
      } else {
        return [...prev, id];
      }
    });
  };
  // tab change
  const tabOnChange = (key: string) => {
    setActTab(key);
  };

  // 展开/收起 按钮样式
  const getExpandedButton = (item: any) => {
    return !expandedKeys.includes(item.id) ? (
      <Button className={styles.btn} onClick={() => expandedKeysClick(item.id)}>
        展开
      </Button>
    ) : (
      <Button className={styles.actBtn} onClick={() => expandedKeysClick(item.id)}>
        收起
      </Button>
    );
  };

  // 返回具体每一个节点
  const renderContent = (item: any) => {
    const strTitle = item.approvalName;
    const index = strTitle.indexOf(searchValue);
    const beforeStr = strTitle.substring(0, index);
    const afterStr = strTitle.slice(index + searchValue.length);
    const title =
      index > -1 ? (
        <span key={item.key}>
          {beforeStr}
          <span className={styles['tree-search-value']}>{searchValue}</span>
          {afterStr}
        </span>
      ) : (
        <span key={item.key}>{strTitle}</span>
      );
    const children = item.children.length > 0 ? renderTree(item.children) : [];
    // 如果子节点中有匹配项，则标记为需要展开
    const isChildMatched = children.some((child: any) => child.isSearch);
    // console.log('isChildMatched', isChildMatched, item.approvalName);

    // if (isChildMatched && !expandedKeys.includes(item.id)) {
    //   setExpandedKeys((prev) => [...new Set([...prev, item.id])]);
    // }
    const isSel = selectList.some((sel: any) => sel.id === item.id);

    const isSearch = index > -1 || isChildMatched;

    return {
      key: item.id,
      isSearch,
      title: (
        <div className={styles.treeTitle}>
          {item.isExpand === 0 &&
            (!isSel ? (
              <Button className={styles.btn} onClick={() => selectClick(item)}>
                {mode === 'single' ? '选择' : '多选'}
              </Button>
            ) : (
              <Button className={styles.actBtn} onClick={() => cancelSelectClick(item)}>
                已选
              </Button>
            ))}
          <div style={{ flex: 1, marginLeft: 5 }} className={isSel ? styles.active : ''}>
            {title}
          </div>
          {item.isExpand === 1 && getExpandedButton(item)}
        </div>
      ),
      children,
    };
  };
  // 渲染当前tab下的tree
  const renderTree = (tree: any[]): any[] => {
    return tree
      .map((item: any) => {
        // 单选模式要根据isShow判断
        if ((item.isShow === 1 && mode === 'single') || mode === 'mutiple') {
          const renderedItem = renderContent(item);
          // 如果当前节点或其子节点匹配，则保留该节点
          if (renderedItem.isSearch || renderedItem.children.length > 0) {
            return renderedItem;
          }
        }
        return null;
      })
      .filter((item) => item !== null);
  };
  // 全部选择
  const allSelClick = () => {
    // 这部分代码是按tab选择所有的tree--start
    // const curTabData = rawData.find((item) => item.approvalType.toString() === actTab);
    // console.log('curTabData', curTabData, rawData, actTab);

    // const list = getFlatList(curTabData.tree);
    // 这部分代码是按tab选择所有的tree--end

    // 如果有子级的，不选中父级
    setSelectList(
      treeList.filter((item: any) => item.children.length === 0 || item.isExpand === 0),
    );
  };
  // 渲染tab
  const tabItems = useMemo(() => {
    const newData = rawData.map((item: any) => ({
      key: item.approvalType.toString(),
      label: typelist[item.approvalType as ApprovalType],
      children: (
        <div ref={treeRef}>
          {/* <div className={styles.allSel}>
            <div>审批类型</div>
            <Button onClick={allSelClick}>全部选择</Button>{' '}
          </div> */}
          <Tree
            height={treeHeight}
            treeData={renderTree(item.tree)}
            blockNode
            expandedKeys={expandedKeys}
            switcherIcon={<DownOutlined />}
          />
        </div>
      ),
    }));
    if (rawData.length > 0 && !actTab) {
      setActTab(rawData[0].approvalType);
    }

    return newData;
  }, [selectList, rawData, expandedKeys, searchValue, treeHeight]);

  // 得到展平的数组
  const getFlatList = (data: any) => {
    return data.flatMap((item: any) => {
      if (item.children.length > 0) {
        const children = getFlatList(item.children);
        return [item, ...children];
      } else {
        return item;
      }
    });
  };

  // console.log('treelist', treeList);

  // 确定
  const okClick = () => {
    onConfirm(selectList);
    // resetClick();
    setVisible(false);
    onClose?.();
  };
  // 重置
  const resetClick = () => {
    setSelectList([]);
    setSearchValue('');
    onReset?.();
  };

  // 添加点击事件监听器, 点击组件外则关闭选择窗
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (componentRef.current && !componentRef.current.contains(event.target)) {
        setVisible(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);

    // 清理事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  // 输入框change事件，使用防抖限制触发频率
  const searchOnChange = debounce((e) => {
    setSearchValue(e.target.value);
  }, 500);
  return (
    <div ref={componentRef}>
      {visible && (
        <div
          className={classNames(
            styles.dataWrapper,
            mode === 'single' ? styles.single : undefined,
            'testtress',
          )}
        >
          <div className={styles.header}>
            <div className={styles.col}>
              <div className={styles.filter}>
                <Input
                  placeholder="请输入关键字"
                  style={{ width: getWidth(290) }}
                  allowClear
                  onChange={searchOnChange}
                />
              </div>
            </div>
            {mode !== 'single' && (
              <div className={styles.col}>
                <h3>已选</h3>
              </div>
            )}
          </div>
          <div className={styles.treeBox}>
            <div className={styles.treeContent}>
              <div className={styles.col}>
                {/* <div className={styles.filter}>
                  <Input
                    placeholder="请输入关键字"
                    style={{ width: getWidth(290) }}
                    allowClear
                    onChange={searchOnChange}
                  />
                </div> */}
              </div>
              <Tabs
                defaultActiveKey="1"
                items={tabItems}
                onChange={tabOnChange}
                tabBarExtraContent={<Button onClick={allSelClick}>全部选择</Button>}
              />
            </div>
            <div className={styles.treeContent}>
              {mode !== 'single' && (
                <div className={styles.col}>
                  <List className={styles.treelist}>
                    {selectList.map((item: any) => {
                      return (
                        <List.Item className={styles.item} key={item.id}>
                          <Button
                            type="primary"
                            ghost
                            size="small"
                            onClick={() => cancelSelectClick(item)}
                          >
                            取消
                          </Button>
                          <span>{item.approvalName}</span>
                        </List.Item>
                      );
                    })}
                  </List>
                </div>
              )}
            </div>
          </div>
          <div className={styles.footer} ref={footerRef}>
            <Space>
              {mode === 'single' ? (
                <Button type="primary" ghost onClick={onClose}>
                  取消
                </Button>
              ) : (
                <Button type="text" onClick={resetClick}>
                  重置
                </Button>
              )}
              <Button type="primary" onClick={okClick}>
                确定
              </Button>
            </Space>
          </div>
        </div>
      )}
      <div
        className={classNames(styles.outShowBox, visible ? styles.active : '')}
        onClick={() => setVisible(true)}
      >
        <div className={lastSelectList.length === 0 ? styles.placeholder : ''}>
          {lastSelectList.length > 0
            ? lastSelectList?.map((item: any) => <span key={item.id}>{item.approvalName};</span>)
            : '请选择最终审批权限'}
        </div>
        <Button type="primary" ghost size="small">
          选择
        </Button>
      </div>
    </div>
  );
};
export default TreeSelect;
