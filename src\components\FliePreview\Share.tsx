import { useRef } from 'react';
import { Button } from 'antd';
import Share from '@/components/Share';
import type { File } from './interface';

interface Props {
  file: File;
}

export default ({ file }: Props) => {
  const shareRef = useRef<{ open: () => void }>(null);

  return (
    <>
      <Button
        type="primary"
        onClick={() => {
          shareRef.current?.open();
        }}
      >
        分享
      </Button>
      <Share ref={shareRef} config={{ module: 'preview', fileList: [file] }} />
    </>
  );
};
