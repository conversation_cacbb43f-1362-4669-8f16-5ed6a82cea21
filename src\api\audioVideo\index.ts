import useFromModuleStore from '@/store/useFromModuleStore';
import useUserStore from '@/store/useUserStore';
import http, { Extra } from '../index';

interface Response {
  code: number;
  data: number;
  msg: string;
}
/**type包含 audio，video */
interface AudioTransformTextParams {
  /**音频路劲 */
  libraryId: string;
  url?: string;
  keyWord?: string;
  size?: number;
  playTime?: number;
  filePath?: string;
  fileAbsolutePath?: string;
}
//人工智能提供接口
/**http 流式新离线版异步音频转文字 */
/**离线版音频转文字 */
export async function audioTransformTextStream(
  data: AudioTransformTextParams,
  signal: AbortSignal,
) {
  const { accessToken } = useUserStore.getState();
  const domain = window.location.host;
  let env = '';
  if (process.env.NODE_ENV === 'development' || domain.includes('client-dev')) {
    env = 'dev';
  } else if (domain.includes('client-sit')) {
    env = 'sit';
  } else if (domain.includes('client-uat')) {
    env = 'uat';
  }
  const result = await fetch(`/smartasr/${env}/api/web-api/media/audio/v5/convert/stream`, {
    method: 'POST',
    signal,
    headers: {
      'Content-Type': 'application/json',
      Accept: 'text/event-stream',
      authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  });
  if (result.ok) {
    return result;
  } else {
    console.error('Failed to fetch:', result.statusText);
  }
}
export async function audioTransformTextHis(data: AudioTransformTextParams) {
  const { accessToken } = useUserStore.getState();
  const domain = window.location.host;
  let env = '';
  if (process.env.NODE_ENV === 'development' || domain.includes('client-dev')) {
    env = 'dev';
  } else if (domain.includes('client-sit')) {
    env = 'sit';
  } else if (domain.includes('client-uat')) {
    env = 'uat';
  }
  const result:any = await fetch(`/smartasr/${env}/api/web-api/media/audio/v5/convert/history`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(data),
  });
  if (result.ok) {
    return result.json();
  } else {
    console.error('Failed to fetch:', result.statusText);
  }
}
/**新离线版异步音频转文字 */
export function newAudioTransformText(data: AudioTransformTextParams, type: string) {
  return http.post<Response>({
    url: `/web-api/media/${type}/v4/convert/offline`,
    timeout: 60_000,
    data,
  });
}
/**查询新离线版异步音频转文字结果 */
export function AudioTransformTextResult(params: { libraryId: string }, type: string) {
  return http.get<Response>({
    url: `/web-api/media/${type}/convert/offline/${params.libraryId}`,
  });
}
/**离线版音频转文字 */
export function audioTransformText(data: AudioTransformTextParams, type: string) {
  return http.post<Response>({ url: `/web-api/media/${type}/convert/text`, timeout: 60_000, data });
}
/**新增音频转文字 */
export function addConvertResult(
  data: {
    groupId?: string;
    libraryId: string;
    convertContent: string;
  },
  type: string,
) {
  const { fromModule, fromModuleQuery } = useFromModuleStore.getState();
  if (fromModule === 'share' && fromModuleQuery.groupId) {
    data.groupId = fromModuleQuery.groupId;
  }
  return http.post<Response>({
    url: `/web-api/media/${type}/${type === 'audio' ? 'convertResult' : 'convert'}`,
    data,
  });
}
/**音频转化历史分页查询 */
export function getConvertHistoryPage(
  data: { libraryId: string; pageNo: number; pageSize: number },
  type: string,
) {
  return http.post<Response>({
    url: `/web-api/media/${type}/${type === 'audio' ? 'convertHistoryPage' : 'convert/history'}`,
    data,
  });
}
// 获取最新一条音频转化结果
export const getConvertResult = (params: { libraryId: string }, type: string) => {
  return http.get<Response>({
    url: `/web-api/media/${type}/${type === 'audio' ? 'convertResult' : 'convert'}/${params.libraryId}`,
  });
};
// 获取视频直播列表
export const getVideoLive = (data: {
  pageNo: number;
  pageSize: number;
  mediaType: number;
  groupId?: string;
  liveTitles?: string[];
  userNames?: string[];
  realNames?: string[];
  liveStartTimes?: string[];
  liveEndTimes?: string[];
}) => {
  console.log(userstoreFun());
  return http.post<Response>({
    url: `/web-api/media/live/page`,
    data,
  });
};
// 获取视频直播列表详情
export const getVideoLiveDecs = (data: { id: string }) => {
  return http.get<Response>({
    url: `/web-api/media/live/page/broadcast`,
    params: data,
  });
};
// 获取视频直播观看列表
export const getVideoLivePeople = (data: { liveId: string; groupId?: string }) => {
  const { fromModule, fromModuleQuery } = useFromModuleStore.getState();
  if (fromModule === 'share' && fromModuleQuery.groupId) {
    data.groupId = fromModuleQuery.groupId;
  }
  return http.get<Response>({
    url: `/web-api/media/live/plays`,
    params: data,
  });
};
// 获取视频是否有直播
export const getHasLive = (data: { mediaType: string; groupId?: string }) => {
  return http.get<Response>({
    url: `/web-api/media/live/watchable`,
    params: data,
  });
};
// 获取直播历史字幕
export const getLiveText = (data: { liveId: string }) => {
  return http.get<Response>({
    url: `/web-api/media/live/convert/list`,
    params: data,
  });
};
// 文库大全数据查询
export const searchConditionAll = (data: Record<string, any>, extra?: Extra) => {
  const { fromModule, fromModuleQuery } = useFromModuleStore.getState();
  if (fromModule === 'share' && fromModuleQuery.groupId) {
    data.groupId = fromModuleQuery.groupId;
  }
  data.deleted = fromModule === 'recycle' ? 1 : 0;
  return http.post(
    {
      url: '/web-api/library/es/doc/searchConditionAll',
      data,
    },
    extra,
  );
};
export const searchConditionResult = (data: Record<string, any>, extra?: Extra) => {
  const { fromModule, fromModuleQuery } = useFromModuleStore.getState();
  if (fromModule === 'share' && fromModuleQuery.groupId) {
    data.groupId = fromModuleQuery.groupId;
  }
  data.deleted = fromModule === 'recycle' ? 1 : 0;
  return http.post(
    {
      url: '/web-api/library/es/doc/searchConditionResult',
      data,
    },
    extra,
  );
};
function userstoreFun() {
  const { fromModule, fromModuleQuery } = useFromModuleStore.getState();
  return { fromModule, fromModuleQuery };
}
// webRTC 信令处理
