import { useState, useRef, useMemo, useEffect } from 'react';
import { Carousel } from 'antd';
import Pagination from './Paging.tsx';
import Context from './Context.tsx';
import styles from './index.module.less';

interface Props {
  dataSource: Record<string, any>[];
  grid: [number, number];
  itemRender: (item: Record<string, any>) => any;
  className?: string;
  style?: Record<string, any>;
  isPagingShow?: boolean;
}

const Component = ({
  dataSource,
  grid,
  itemRender,
  className,
  style,
  isPagingShow = true,
}: Props) => {
  const carouselRef = useRef(null);
  const [pageSize, setPageSize] = useState(3);
  const [pageNumber, setPageNumber] = useState(1);
  const [total, setTotal] = useState(0);
  const [pageMax, setPageMax] = useState(1);
  const [pages, setPages] = useState<Record<string, any>[][]>([]);

  useEffect(() => {
    const pageSize = grid[0] * grid[1];
    setPageSize(pageSize);
    const total = dataSource.length;
    setTotal(total);
    const pageMax = Math.ceil(total / pageSize);
    setPageMax(pageMax);
    const pages = [];
    for (let i = 1; i <= pageMax; i++) {
      const page = dataSource.slice(pageSize * (i - 1), pageSize * i);
      pages.push(page);
    }
    setPages(pages);
  }, [grid, dataSource]);

  return (
    <Context.Provider
      value={{
        pageSize,
        setPageSize,
        pageNumber,
        setPageNumber,
        pageMax,
        total,
        carouselRef,
      }}
    >
      <div
        className={className ? `${styles.pagingCard} ${className}` : styles.pagingCard}
        style={style}
      >
        <Carousel ref={carouselRef} dots={false} infinite={false}>
          {pages.map((items, index) => {
            return (
              <div key={index} className={styles.section}>
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: `repeat(${grid[0]}, 1fr)`,
                    gridTemplateRows: `repeat(${grid[1]}, 1fr)`,
                  }}
                >
                  {items.map((item, idx) => {
                    return <div key={item.key || idx}>{itemRender(item)}</div>;
                  })}
                </div>
              </div>
            );
          })}
        </Carousel>
        {(isPagingShow || pageMax > 1) && <Pagination />}
      </div>
    </Context.Provider>
  );
};

export default Component;
