import { Button } from 'antd';
import React from 'react';
import styles from './index.module.less';
const Component = ({
  title,
  cancelText = '返回',
  onCancel,
  actionList = [],
}: {
  title: string;
  cancelText?: string;
  onCancel: () => void;
  actionList?: React.ReactNode[];
}) => {
  return (
    <div className={styles.MobileHeaderTitle}>
      <div className={styles.title}>{title}</div>
      <div>
        {actionList.length > 0 ? actionList : <Button onClick={onCancel}>{cancelText}</Button>}
      </div>
    </div>
  );
};

export default Component;
