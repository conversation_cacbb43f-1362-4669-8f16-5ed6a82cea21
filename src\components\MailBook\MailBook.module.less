.contentBox {
  position: absolute;
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
  --mailbox-title-height: 50px;
  --mailbox-title-padding-y: 15px;
  &.notAbsolute {
    position: static;
    .modal-body {
      height: auto;
    }
  }
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: var(--mailbox-title-padding-y) 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    .left-title {
      p {
        display: inline-block;
        font-weight: 500;
        line-height: 20px;
        font-size: 20px;
        text-align: center;
        color: #4d70fe;
      }

      span {
        display: inline-block;
        height: 20px;
        font-size: 14px;
        color: #252525;
        padding-left: 12px;

        a {
          color: #f66565;
        }
      }
    }
    button {
      width: 60px;
      height: 32px;
      border-radius: 4px;
      padding: 5px 16px;
      background: #ffffff;
      border: 1px solid #3d5afe;
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      color: #3d5afe;
    }
  }

  .modal-body {
    display: flex;
    flex-direction: column;
    background: #ffffff;
    padding: 0 8px;
    box-sizing: border-box;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    height: calc(100% - 70px);
    .serach {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 13px 15px 10px;
      :global {
        .ant-form-item {
          margin-inline-end: 8px;
        }
      }
    }
    .choose-text {
      color: #f4511e;
      height: 24px;
      line-height: 24px;
      margin-left: 24px;
    }
    .table {
      height: 320px;
      .active {
        width: 48px;
        height: 24px;
        border-radius: 8px;
        opacity: 1;
        background: #1556f0;
        font-size: 14px;
        text-align: center;
        color: #ffffff;
      }
      .deactive {
        width: 48px;
        height: 24px;
        border-radius: 8px;
        opacity: 1;
        background: #8c9eff !important;
        font-size: 14px;
        text-align: center;
        color: #ffffff !important;
      }
      :global {
        .ant-table-wrapper table,
        .ant-table-container,
        .ant-table-container,
        .ant-table-wrapper .ant-table .ant-table-header {
          border-radius: 0 !important;
        }
      }
    }
    .check-table {
      height: 280px;
      .check-title {
        padding: 14px 20px;
        span {
          font-size: 16px;
          font-weight: normal !important;
          line-height: 24px;
          height: 24px;
          color: var(--ant-table-select-title-color);
        }
      }
    }
    .active {
      background: #ffffff !important;
      color: #3d5afe !important;
      font-size: 14px;
      text-align: center;
      width: 44px !important;
      height: 26px !important;
      border-radius: 4px !important;
      padding: 2px 8px;
      box-sizing: border-box;
      border: 1px solid #3d5afe;
    }
    .deactive {
      width: 44px !important;
      height: 26px !important;
      border-radius: 4px !important;
      font-size: 14px;
      background: #7986cb !important;
      color: #ffffff;
    }
    .check-title {
      padding: 14px 20px;
      span {
        display: block;
        height: 24px;
        opacity: 1;
        font-size: 18px;
        font-weight: 500;
        line-height: normal;
        color: #1556f0;
      }
    }
    .footer {
      display: flex;
      align-items: center;
      justify-content: center;
      button {
        width: 96px;
        height: 40px;
        border-radius: 4px;
        opacity: 1;
        background: #3d5afe;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        color: #ffffff;
        margin-bottom: 16px;
        margin-top: 10px;
      }
    }
  }

  :global {
    .ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:first-child {
      border-radius: 0px;
    }
    .ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:last-child {
      border-radius: 0px;
    }
    .ant-table {
      font-size: 14px !important;
      width: 100%;
      th {
        height: 36px;
        line-height: 36px;
        padding: 0 0 0 10px !important;
        font-weight: normal !important;
        background-color: #e8eaf6 !important;
        &::before {
          height: 0 !important;
        }
      }
      td {
        height: 40px;
        padding: 0 0 0 10px !important;
        font-weight: normal !important;
        .ant-table-cell:empty:after,
        .ant-table-cell span:empty:after {
          content: '--';
        }
      }
      .ant-table-body {
        overflow-x: hidden;
      }
    }
  }
}
