.paginationContainer {
  width: 100%;
  height: 35px;
  display: flex;
  align-items: center;
  font-size: 12px;
  user-select: none;
  .cusButList {
    display: flex;
    align-items: center;
    & > span,
    & > div {
      width: 60px;
      height: 26px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
    }
    & > div {
      background: rgba(0, 0, 0, 0.05);
      &:hover {
        background: rgba(0, 0, 0, 0.05);
        background-color: #4d70fe;
        color: #fff;
      }
    }
  }
  .disabled-btn {
    pointer-events: none;
  }
  :global {
    .ant-pagination,.ant-pagination-prev,.ant-pagination-next{
      display: flex;
      align-items: center;
    }
    .ant-pagination-item {
      width: 46px;
      height: 26px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.05);
      &:hover {
        background: #3d5afe !important;
        a {
          color: #fff;
        }
      }
    }
    .ant-pagination .ant-pagination-item-active {
      background: #3d5afe;
      a {
        color: #fff;
      }
    }
  }
  .customPages {
    display: flex;
    align-items: center;
    margin:0 10px;
    input {
      width: 42px;
      height: 26px;
      border-radius: 4px;
      box-sizing: border-box;
      border: 1px solid #4d70fe;
      font-size: 12px;
      text-align: center;
      margin: 0 8px;
      outline: none;
      background: rgba(0, 0, 0, 0.05);
    }
    button {
      width: 60px;
      height: 26px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #4d70fe;
      border-radius: 4px;
      color: #fff;
      cursor: pointer;
      margin-left: 8px;
    }
  }
  .totalBox{
    span{
      color: #F40000;
      padding: 0 5px;
      font-size: 14px;
      font-weight: 500;
    }
  }
}
