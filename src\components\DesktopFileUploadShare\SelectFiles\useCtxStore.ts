import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create } from '@/store';
import { multiply } from 'lodash';

interface FormFilterData {
  realName: string;
  title: string;
}

interface ColumnsFilterData {
  fileFormatTypeList: string[];
  fileSourceList: string[];
  minFileSize: string;
  maxFileSize: string;
}

export interface State {
  selectFilesOpen: boolean;
  formFilterData: FormFilterData;
  columnsFilterData: ColumnsFilterData;
  queryData: (FormFilterData & ColumnsFilterData)[];
  queryType: { current: 'all' | 'results' };
  list: any[];
  total:number;
  selectedList: any[];
  selectedMap: Record<string, any>;
}

export interface SetState {
  setSelectFilesOpen: Setter;
  setFormFilterData: Setter;
  setColumnsFilterData: Setter;
  setQueryData: Setter;
  setQueryType: Setter;
  setList: Setter;
  setTotal:Setter;
  setSelectedList: Setter;
  setSelectedMap: Setter;
}

interface Config {
  module: string;
  type: string;
  [prop: string]: any;
}

export const formatQueryData = (queryData: any, { pageNumber, pageSize, config }: any) => {
  const result: any = {};
  const add = (key: any, value: any) => {
    result[key] = result[key] || [];
    if (value) {
      result[key].push(value);
    }
  };
  const getFileFormatType = (item: any) => {
    if (config.module === 'audioPlay') {
      return ['library_file_type_audio'];
    } else if (config.module === 'videoPlay') {
      return ['library_file_type_video'];
    } else {
      return item.fileFormatTypeList;
    }
  };
  queryData.forEach((item: any) => {
    result.pageNumber = pageNumber.current;
    result.pageSize = pageSize;
    result.type = 'desktopFile';
    add('title', item.title);
    add('content', '');
    add('startTime', '');
    add('endTime', '');
    add('minSizes', multiply(Number(item.minFileSize), 1024 * 1024));
    add('maxSizes', multiply(Number(item.maxFileSize), 1024 * 1024));
    add('fileSource', item.fileSourceList);
    add('fileFormat', getFileFormatType(item));
  });
  return result;
};

const createUseCtxStore = function (config: Config) {
  const useCtxStore = autoUseShallow<State, SetState>(
    create(
      autoCreateSetters<State>((set, get, api): any => {
        const formFilterData = {
          realName: '',
          title: '',
        };
        const columnsFilterData = {
          fileFormatTypeList: [],
          fileSourceList: [],
          minFileSize: '',
          maxFileSize: '',
        };
        return {
          selectFilesOpen: false,
          formFilterData,
          columnsFilterData,
          queryData: [
            {
              ...formFilterData,
              ...columnsFilterData,
            },
          ],
          queryType: { current: 'all' },
          list: [],
          total:0,
          selectedList: [],
          selectedMap: {},
        };
      }),
    ),
  );

  useCtxStore.subscribe((state, prevState) => {
    if (state.queryType === prevState.queryType) {
      return;
    }
    switch (state.queryType.current) {
      case 'all':
        state.setQueryData([
          {
            ...state.formFilterData,
            ...state.columnsFilterData,
          },
        ]);
        break;
      case 'results':
        state.setQueryData((value: any) => [
          ...value,
          {
            ...state.formFilterData,
            ...state.columnsFilterData,
          },
        ]);
        break;
    }
  });

  return useCtxStore;
};

export default createUseCtxStore;
