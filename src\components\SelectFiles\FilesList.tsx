import { searchConditionAll, searchConditionResult } from '@/api/library';
import { formatFileSizeFilterValue } from '@/components/Filters';
import FilePreview from '@/components/FliePreview';
import type { FilePreviewAPI } from '@/components/FliePreview';
import { Button, Space, Table } from 'antd';
import { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { getColumns } from './columns';
import Context from './Context';
import styles from './index.module.less';
import { formatQueryData } from './useCtxStore';
import { debounce } from 'lodash';

const Component = () => {
  const filePreviewRef = useRef<FilePreviewAPI>();
  const { useCtxStore, sceneType, columnFields } = useContext(Context);
  const [setColumnsFilterData, queryData, list, setList, selectedList, setSelectedList] =
    useCtxStore!((state) => [
      state.setColumnsFilterData,
      state.queryData,
      state.list,
      state.setList,
      state.selectedList,
      state.setSelectedList,
    ]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageNumber, setPageNumber] = useState({ current: 1 });
  const [pageSize, setPageSize] = useState(1000);
  const [total, setTotal] = useState(0);
  const [loadedList, setLoadedList] = useState<any[]>([]);
  const didMountRef = useRef(false);
  const columns = useMemo(() => {
    return getColumns(true, columnFields).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              <span></span>
              {!!list.length && (
                <Button
                  type="primary"
                  size="small"
                  ghost={selectedList.length !== list.length}
                  onClick={() => {
                    if (selectedList.length === list.length) {
                      setSelectedList([]);
                    } else {
                      setSelectedList([...list]);
                    }
                  }}
                >
                  全部选择
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          let element = null;
          if (selectedList.includes(row)) {
            element = (
              <Button
                type="primary"
                size="small"
                onClick={() =>
                  setSelectedList((value: any) => value.filter((item: any) => item.id !== row.id))
                }
              >
                已选
              </Button>
            );
          } else {
            element = (
              <Button
                type="primary"
                size="small"
                ghost
                onClick={() => setSelectedList((value: any) => [...value, row])}
              >
                多选
              </Button>
            );
          }
          return (
            <Space>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  preview(row);
                }}
              >
                浏览
              </Button>
              {element}
            </Space>
          );
        };
      }
      return item;
    });
  }, [list, selectedList]);
  useEffect(() => {
    if (total > 0 && list.length === total) {
      setHasMore(false);
    }
  }, [total, list]);
  const getList = () => {
    const api = queryData.length === 1 ? searchConditionAll : searchConditionResult;
    api(formatQueryData(queryData, { pageNumber, pageSize, sceneType }), { setLoading }).then(
      ({ data }: any) => {
        const { total, list } = data;
        const nextLoadedList = [...loadedList, ...list];
        setTotal(total);
        setLoadedList(nextLoadedList);
        setList([...nextLoadedList]);
      },
    );
  };
  const change = (pagination: any, filters: any, sorter: any, extra: any) => {
    const fileSize: any = filters.fileSize ? filters.fileSize[0] : {};
    switch (extra.action) {
      case 'filter':
        setColumnsFilterData({
          fileFormatTypeList: filters.fileFormatType ?? [],
          sourceList: filters.source ?? [],
          ...formatFileSizeFilterValue(fileSize),
        });
        break;
    }
  };
  const preview = (row: any) => {
    filePreviewRef.current?.open({ ...row });
  };
  const handleScroll = useCallback(
    debounce((e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, clientHeight, scrollHeight } = e.target as HTMLDivElement;
      if (scrollTop + clientHeight >= scrollHeight - 20 && hasMore && !loading) {
        setPageNumber((value) => ({ current: value.current + 1 }));
      }
    }, 500),
    [hasMore, loading],
  );
  useEffect(() => {
    if (!loadedList.length || loadedList.length < total) {
      getList();
    }
  }, [pageNumber]);
  useEffect(() => {
    if (didMountRef.current) {
      setTotal(0);
      setLoadedList([]);
      setPageNumber({ current: 1 });
    } else {
      didMountRef.current = true;
    }
  }, [queryData]);

  return (
    <div className={styles.filesList}>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={list}
        loading={loading}
        rowKey={'id'}
        scroll={{ y: 491 }}
        pagination={false}
        onChange={change}
        onScroll={handleScroll}
      />
      <FilePreview ref={filePreviewRef} />
    </div>
  );
};

export default Component;
