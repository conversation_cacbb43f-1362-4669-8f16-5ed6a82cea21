.record-content {
  .header {
    height: 72px;
    padding: 18.47px 19.7px;
    background: rgba(255, 255, 255, 0.5);
    border-width: 1px 1px 0px 1px;
    border-style: solid;
    border-color: #ffffff;

    .header-span {
      height: 35px;
      font-size: 20px;
      font-weight: 500;
      line-height: 35px;
      opacity: 1;
    }

    .header-button {
      height: 32px;
      border-radius: 4px;
      opacity: 1;
      padding: 5px 12px;
      background: #ffffff;
      border: 1px solid #3d5afe;
    }
  }
  .content {
    position: relative;
    overflow: hidden;
    // background-image: url('@/assets/images/mail/backImage.png');
    // background-size: cover; /* 让背景图片按比例缩放以覆盖整个容器 */
    // background-position: center; /* 让背景图片在容器中居中显示 */
    // background-repeat: no-repeat; /* 防止背景图片重复 */
    background-color: black;

    .duration {
      height: 36px;
      opacity: 1;
      font-size: 26px;
      font-weight: 500;
      color: #ffffff;
      position: absolute;
      top: 33px;
      z-index: 2;
      left: 50%;
      transform: translateX(-50%);
    }
    .float {
      width: 100px;
      height: 100px;
      position: absolute;
      bottom: 70px;
      z-index: 2;
      left: 50%;
      transform: translateX(-50%);
      cursor: pointer;
    }
    .autoPlay {
      height: 928px;
    }
  }

  :global {
    .ant-modal-content {
      padding: 0;
      margin: 0;
      height: 1000px;
      border-radius: 10px;
      opacity: 1;
      background: rgba(255, 255, 255, 0.85);
      border: 1px solid #ffffff;
      backdrop-filter: blur(38.18px);
      box-shadow: 0px 24.63px 49.26px 0px rgba(0, 0, 0, 0.1);
    }
  }
}
