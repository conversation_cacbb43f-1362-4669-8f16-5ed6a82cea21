import { FC, useEffect, useState } from 'react';
import ChatDetail from './chatDetail/index';
import ChatList from './chatList/index';
interface ChatPropType {
  showDetail?: boolean;
}
const Index: FC<ChatPropType> = ({ showDetail }) => {
  const goChatDetail = (item: any) => {
    console.log('进入聊天详情', item);
    setShowList(false);
  };
  const [showList, setShowList] = useState(!showDetail);
  const [open, setOpen] = useState(true);
  useEffect(() => {
    console.log('showDetail', showDetail);
    setShowList(Boolean(!showDetail));
  }, [showDetail]);
  const init = () => {
    setShowList(true);
  };
  return (
    <div id="chat-root">
      {showList ? <ChatList goChatDetail={goChatDetail} /> : <ChatDetail onClose={init} />}
      {/* <SelectEmployee
        open={open}
        mode="multiple"
        onClose={() => setOpen(false)}
        onSubmit={(list) => console.log('list', list)}
      /> */}
    </div>
  );
};
export default Index;
