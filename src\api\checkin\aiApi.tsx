const getXMLRequest = (url: string, data: any) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open('POST', `http://10.66.236.10:17500${url}`, true);
    xhr.setRequestHeader('Authorization', 'Bearer secrettoken');
    xhr.onload = () => {
      if (xhr.readyState === 4) {
        if (xhr.status === 200) {
          resolve(JSON.parse(xhr.responseText));
        } else {
          reject(xhr.statusText);
        }
      }
    };
    xhr.onerror = () => {
      reject(xhr.statusText);
    };
    xhr.send(data);
  });
};

// 登记人脸
export const registerFace = (data: any) => {
  return getXMLRequest('/v1/register_user_face', data);
};

// 人脸比对
export const checkFace = (data: any) => {
  return getXMLRequest('/v1/user_check_in', data);
};
