import api from '../index';
import { CreateQuestionModel } from './questionModel';

// 创建题目
export const createQuestion = (data: CreateQuestionModel) => {
  return api.post({
    url: '/web-api/exam/question/create',
    data,
  });
};

// 根据id获得题目
export const getQuestionById = (id: any) => {
  return api.get({
    url: `/web-api/exam/question/get?id=${id}`,
  });
};

// 更新题目
export const updateQuestion = (data: any) => {
  return api.put({
    url: '/web-api/exam/question/update',
    data,
  });
};

// 创建和更新题库说明
export const updateQuestionBankDescribe = (data: any) => {
  return api.put({
    url: '/web-api/exam/question/bank/update',
    data,
  });
};

// 获取题库说明
export const getQuestionBankDescription = (id: any) => {
  return api.get({
    url: '/web-api/exam/question/bank/get',
  });
};
