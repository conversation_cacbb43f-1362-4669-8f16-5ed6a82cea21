//发起审批数据模型空间

//图标和红点数据类型
export interface approvalType {
  msg: number;
  show: string;
  text: string;
  value: number;
}

export interface SiderProps {
  list: approvalType[];
}

export interface creatProval {
  initUserId: string;
  initUserName: string;
  initUserRealName?: string;
  configId: string;
  configCode: string;
  approvalFullName: string;
  startTime?: string;
  endTime?: string;
  approvalName: string;
  content: string;
  approvalUser: string;
  approvalUserName: string;
  approvalUserRealName?: string;
  fileReqVOList: FileReqVOList[];
  mainId: string;
  middleContent: string;
  toUserId?: string;
  toUserName?: string;
  toUserRealName?: string;
  bizId?: string;
  bizExt?: string;
  attendanceApproval?: any;
}
export interface FileReqVOList {
  visitPath?: string;
  fileName: string;
  fileUrl: string;
  fileTail: string;
  libraryId: string;
  fileFormatType: string;
  fileSize: number;
  fileType: string;
  secretKey: string;
  source: number;
  version: number;
  docId?: string;
  decodeKey: string;
}

export interface approvalRes {
  id: string;
  mainId: string;
  approvalFullName: string;
  approvalName: string;
  content: string;
  initUserId: string;
  initUserName: string;
  initUserRealName: string;
  configCode: string;
  preId: string;
  fromUserId: string;
  fromUserName: string;
  fromUserRealName: string;
  middleContent: string;
  currentStatus: number;
  createTime: string;
  approvalTime: string;
  toUserId: string;
  toUserName: string;
  toUserRealName: string;
  fromUserLevel: string;
  fileDOList: FileResVOList[];
  bizId: string;
  bizExt: string;
}

export interface FileResVOList {
  createTime: string;
  updateTime: string;
  creator: string;
  updater: string;
  deleted: number;
  tenantId: number;
  id: number;
  detailId: number;
  mainId: number;
  libraryId: number;
  fileName: string;
  fileUrl: string;
  fileTail: string;
  fileFormatType: string;
  fileSize: string;
  fileType: string;
  secretKey: string;
  source: number;
  version: number;
  decodeKey: number;
}

// 审批状态
export const ApprovalStatus = {
  1: '待审批',
  2: '已审批',
  3: '退回',
};

// 审批列表相应接口中的 detailList
export interface DetailListProps {
  id: number;
  mainId: number;
  fromUserId: string;
  toUserId: string;
  fromUserName: string;
  fromUserRealName: string;
  middleContent: string;
  status: number;
  updateTime: number;
  currentStatus: number;
}
// 审批列表响应接口
export interface ListPageProps {
  /*审批主键 */
  id: number;

  /*审批发起人id */
  initUserId: number;

  /*发起人账号 */
  initUserName: string;

  /*发起人姓名 */
  initUserRealName: string;

  /*发起时间（格式:YYYY-mm-dd HH:mm:ss） */
  createTime: '';

  /*关联配置表主键 */
  configId: number;

  /*审批类别配置编码 */
  configCode: string;

  /*审批类型全名 */
  approvalFullName: string;

  /*审批事项名称 */
  approvalName: string;

  /*审批事实及理由 */
  content: string;

  /*审批附件Id */
  fileId: string;

  /*审批附件名称 */
  fileName: string;

  /*审批附件url */
  fileUrl: string;

  /*中间审批人，审批内容，审批时间【json】 */
  list: string;

  /*中间审批人，审批内容，审批时间 */
  detailList: [];

  /*审批状态 1审批中 2审批完成 3退回 */
  status: number;
}

// export const list: approvalRes[] = [
//   {
//     id: '1',
//     mainId: '0',
//     approvalFullName: '',
//     approvalName: '',
//     content: '',
//     initUserId: '0',
//     initUserName: '2222222222',
//     initUserRealName: '黄庆州',
//     configCode: '',
//     preId: '0',
//     fromUserId: '',
//     fromUserName: '000000000',
//     fromUserRealName: '吴迪',
//     middleContent: '',
//     createTime: '',
//     approvalTime: '',
//     fileDOList: [],
//     toUserId: '',
//     toUserName: '',
//     toUserRealName: '',
//     fromUserLevel: '',
//     currentStatus: 0,
//   },
//   {
//     id: '1',
//     mainId: '0',
//     approvalFullName: '',
//     approvalName: '请假',
//     content: '',
//     initUserId: '0',
//     initUserName: '2222222222',
//     initUserRealName: '黄庆州',
//     configCode: '',
//     preId: '0',
//     fromUserId: '',
//     fromUserName: '1111111111',
//     fromUserRealName: '张朋涛',
//     middleContent:
//       '我同意,我也想去看看，我同意,我也想去看看我同意,我也想去看看我同意,我也想去看看我同意,我也想去看看我同意,我也想去看看我同意,我也想去看看我同意,我也想去看看我同意,我也想去看看我同意,我也想去看看我同意,我也想去看看我同意,我也想去看看',
//     createTime: '',
//     approvalTime: '2024-12-24 18:30:45',
//     fileDOList: [],
//     toUserId: '',
//     toUserName: '',
//     toUserRealName: '',
//     fromUserLevel: '',
//     currentStatus: 0,
//   },
//   {
//     id: '1',
//     mainId: '0',
//     approvalFullName: '请假1',
//     approvalName: '请假',
//     content:
//       '世界那么大，我想去看看,世界那么大，我想去看看世界那么大，我想去看看世界那么大，我想去看看世界那么大，我想去看看世界那么大，我想去看看世界那么大，我想去看看世界那么大，我想去看看世界那么大，我想去看看世界那么大，我想去看看世界那么大，我想去看看',
//     initUserId: '0',
//     initUserName: '2222222222',
//     initUserRealName: '黄庆州',
//     configCode: '',
//     preId: '0',
//     fromUserId: '',
//     fromUserName: '',
//     fromUserRealName: '',
//     middleContent: '',
//     createTime: '2024-12-24 15:30:45',
//     approvalTime: '',
//     fileDOList: [],
//     toUserId: '',
//     toUserName: '',
//     toUserRealName: '',
//     fromUserLevel: '',
//     currentStatus: 0,
//   },
// ];

// 审批类别配置树
export interface ConfigTreeProps {
  id: number;
  parentId: number;
  code: string;
  approvalName: string;
  leafFlag: number;
  formFlag: number;
  approvalType: number;
  isShow: number;
  parentIds: string;
  createTime: string;
  children: ConfigTreeProps[];
  value: number;
  label: string;
  isExpand: number;
}
export interface configTreeListProps {
  approvalType: 1 | 2; // 审批等级 1：公共审批，2：特殊审批
  tree: ConfigTreeProps[];
}
