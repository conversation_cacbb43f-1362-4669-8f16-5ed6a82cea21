.WenKuDaQuanContainer {
  width: 100vw;
  height: 100%;
  :global {
    .ant-table {
      width: 100%;
    }
  }
  // 菜单搜索
  .headerSearch {
    width: 100%;
    height: 66px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 14px;
    padding-left: 24px;
    padding-right: 24px;
    font-size: 14px;
    .searchLeft {
      display: flex;
      align-items: center;
      .searchInput {
        display: flex;
        align-items: center;
        margin-right: 12px;
        > span {
          padding-right: 8px;
        }
        .searchSpace {
          margin-left: 8px;
        }
        input {
          width: 120px;
          height: 30px;
          border-radius: 8px;
          background: #f3f9ff;
          box-sizing: border-box;
          border: 1px solid #4d70fe;
          font-size: 12px;
          padding-left: 8px;
          outline: none;
        }
      }
      .flexBox {
        align-items: center;
        margin-right: 12px;
        span {
          white-space: nowrap;
          margin-right: 8px;
        }
      }

      .searchButton {
        width: 88px;
        height: 30px;
        border-radius: 8px;
        background: #3d5afe;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        margin-right: 12px;
        cursor: pointer;
        &:last-of-type {
          margin-right: 0;
        }
      }
    }
    .searchRight {
      display: flex;
    }
  }
  .tableBut {
    width: 60px;
    height: 30px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #3d5afe;
    margin: 0 auto;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
  }
  :global {
    .ant-table {
      th {
        height: 36px;
        line-height: 36px;
        padding: 0 !important;
        font-weight: normal !important;
        background-color: #e3ecf5 !important;
        &::before {
          height: 0 !important;
        }
      }
      td {
        height: 60px;
        line-height: 60px;
        padding: 0 !important;
      }
    }
  }
}
:global {
  .timePopover .ant-popover-inner {
    padding: 0 !important;
  }
}
.cursor {
  cursor: pointer;
}

.close {
  border-radius: 4px;
  /* 自动布局 */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  height: 30px;
  background: #fff;
  color: #3d5afe;
  border: 1px solid #3d5afe;
  cursor: pointer;
}
:global {
  .optionsWrap {
    .ant-select-item-option-selected {
      background: #3d5afe !important;
      color: #fff !important;
      line-height: 29px !important;
    }
  }
}
