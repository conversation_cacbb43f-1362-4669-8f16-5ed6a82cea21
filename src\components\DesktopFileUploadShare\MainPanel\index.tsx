import { MinimizeButton } from '@/components/LoadingList';
import MultiModal from '@/components/MultiModal';
import { Button } from 'antd';
import { useContext, useEffect, useMemo, useRef } from 'react';
import Context from '../Context';
import styles from './index.module.less';
import Progress from './Progress';
import SelectFiles from './SelectFiles';
import ToolBar from './ToolBar';
import useDesktopFileUploadShareStore from '../useDesktopFileUploadShareStore';

interface Props {
  onMinimize?: () => void;
  onCancel?: () => void;
}

const Component = ({ onMinimize, onCancel }: Props) => {
  const progressRef = useRef<{
    upload: () => void;
    pause: () => void;
    resume: () => void;
    abort: () => void;
  }>();
  const [shareFileList, shareGroupList, shareUserList] = useDesktopFileUploadShareStore(
    (state) => [state.shareFileList, state.shareGroupList, state.shareUserList],
  );
  const { useMainPanelCtxStore, useSelectFilesCtxStore, config } = useContext(Context);
  const [mainPanelOpen, selectedFileList, loadingStatus, setSelectedFileList] =
    useMainPanelCtxStore!((state) => [
      state.mainPanelOpen,
      state.selectedFileList,
      state.loadingStatus,
      state.setSelectedFileList,
    ]);
  const [setSelectFilesOpen] = useSelectFilesCtxStore!((state) => [state.setSelectFilesOpen]);
  const okEnabled = useMemo(() => {
    return selectedFileList.length > 0 && loadingStatus === 'init';
  }, [selectedFileList, loadingStatus]);
  const pauseEnabled = useMemo(() => {
    return loadingStatus === 'loading';
  }, [loadingStatus]);
  const resumeEnabled = useMemo(() => {
    return loadingStatus === 'pause';
  }, [loadingStatus]);
  const abortEnabled = useMemo(() => {
    return loadingStatus === 'loading' || loadingStatus === 'pause';
  }, [loadingStatus]);
  const minimizeEnabled = useMemo(() => {
    return loadingStatus === 'loading' || loadingStatus === 'pause';
  }, [loadingStatus]);
  const closeEnabled = useMemo(() => {
    return loadingStatus === 'init' || loadingStatus === 'complete';
  }, [loadingStatus]);
  const toolBarClick = (type: string) => {
    if (type === 'selectFiles') {
      setSelectFilesOpen(true);
    }
  };
  const minimize = () => {
    if (onMinimize) {
      onMinimize();
    }
  };
  const cancel = () => {
    if (onCancel) {
      onCancel();
    }
  };
  const upload = () => {
    setSelectFilesOpen(false);
    progressRef.current?.upload();
  };
  const pause = () => {
    progressRef.current?.pause();
  };
  const resume = () => {
    progressRef.current?.resume();
  };
  const abort = () => {
    progressRef.current?.abort();
  };
  useEffect(() => {
    if (shareFileList.length > 0) {
      setSelectedFileList([...shareFileList])
    }
  }, [shareFileList]);
  useEffect(() => {
    return () => {
      cancel();
      abort();
    };
  }, []);
  const title = (
    <div className={styles.title}>
      <div className={styles.left}>
        <span>分享文件</span>
        <span>
          已选择 <b>{selectedFileList.length}</b> 个
        </span>
      </div>
      <div className={styles.right}>
        <MinimizeButton disabled={!minimizeEnabled} onClick={minimize} />
        <Button type="primary" ghost disabled={!closeEnabled} onClick={cancel}>
          关闭
        </Button>
      </div>
    </div>
  );

  return (
    <MultiModal
      layoutGroup={`upload_${config?.module}_${config?.type}`}
      layoutClassName="modalRight"
      destroyOnClose={true}
      closable={false}
      title={title}
      open={mainPanelOpen}
      footer={[
        <Button key="abort" type="primary" ghost danger disabled={!abortEnabled} onClick={abort}>
          取消分享
        </Button>,
        <Button
          key="upload"
          type="primary"
          loading={loadingStatus === 'waiting'}
          disabled={!okEnabled}
          onClick={upload}
        >
          确认分享
        </Button>,
      ]}
    >
      <ToolBar onClick={toolBarClick} />
      <SelectFiles />
      <Progress ref={progressRef} />
    </MultiModal>
  );
};

export default Component;
