import MultiModal from '@/components/MultiModal';
import { Button } from 'antd';
import { useEffect, useMemo, useContext } from 'react';
import Filter from './Filter';
import FilesList from './FilesList';
import styles from './index.module.less';
import Context from '../Context';

const Component = () => {
  const { useMainPanelCtxStore, useSelectFilesCtxStore, config } = useContext(Context);
  const [selectFilesOpen, setSelectFilesOpen, list, selectedList,total] = useSelectFilesCtxStore!(
    (state) => [state.selectFilesOpen, state.setSelectFilesOpen, state.list, state.selectedList,state.total],
  );
  const [setSelectedFileList, selectedFileMap, setSelectedFileMap] = useMainPanelCtxStore!(
    (state) => [state.setSelectedFileList, state.selectedFileMap, state.setSelectedFileMap],
  );
  const okEnabled = useMemo(() => {
    return selectedList.some((item: any) => !selectedFileMap[item.id]);
  }, [selectedList, selectedFileMap]);
  const cancel = () => {
    setSelectFilesOpen(false);
  };
  const ok = () => {
    const map = { ...selectedFileMap };
    selectedList.forEach((item: any) => {
      if (!map[item.id]) {
        item.status = 'init';
        map[item.id] = item;
      }
    });
    setSelectedFileMap(map);
    setSelectedFileList(Object.values(map));
  };
  useEffect(() => {
    if (selectFilesOpen === false) {
      useSelectFilesCtxStore!.reset();
    }
  }, [selectFilesOpen]);

  const title = (
    <div className={styles.title}>
      <span>选择文件</span>
      <span>
        共计 <b>{total}</b> 个，已选择 <b>{selectedList.length||selectedFileMap.szie||0}</b> 个
      </span>
    </div>
  );

  return (
    <MultiModal
      layoutGroup={`upload_${config?.module}_${config?.type}`}
      layoutClassName="modalLeft"
      destroyOnClose={true}
      title={title}
      open={selectFilesOpen}
      onCancel={cancel}
      footer={[
        <Button key="submit" type="primary" disabled={!okEnabled} onClick={ok}>
          确认选择
        </Button>,
      ]}
    >
      <Filter />
      <FilesList />
    </MultiModal>
  );
};

export default Component;
