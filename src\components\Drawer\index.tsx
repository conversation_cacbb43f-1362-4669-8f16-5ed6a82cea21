import { getDomRealWH } from '@/utils/common';
import { Drawer, DrawerProps } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.module.less';
const Component = (DrawerProps: DrawerProps) => {
  const [width, setWidth] = useState('');
  useEffect(() => {
    setWidth(getDomRealWH('#smartSecretaryRoot').width);
  }, []);
  return (
    <div className={styles.customDrawerComponent}>
      <Drawer
        {...DrawerProps}
        placement={DrawerProps.placement || 'right'}
        closable={false}
        getContainer={false}
        width={+width}
        push={{ distance: 0 }}
      >
        {DrawerProps.children}
      </Drawer>
    </div>
  );
};

export default Component;
