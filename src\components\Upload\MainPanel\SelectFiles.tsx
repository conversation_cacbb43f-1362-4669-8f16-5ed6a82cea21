import { Button, Space, Table } from 'antd';
import { useContext, useMemo } from 'react';
import Context from '../Context';
import { getColumns } from '../SelectFiles/columns';
import styles from './index.module.less';
import { cppbRequest } from '@/store/useCppBridgeStore';
import { getRandom } from '@/utils/common';
import { shareError } from '@/utils/modal';

const Component = () => {
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [selectedFileList, setSelectedFileList, selectedFileMap, setSelectedFileMap] =
    useMainPanelCtxStore!((state) => [
      state.selectedFileList,
      state.setSelectedFileList,
      state.selectedFileMap,
      state.setSelectedFileMap,
    ]);
    //浏览
  const scanBtn = async (row: any) => {
    const response = await cppbRequest({
      module: 'desktopFile',
      id: getRandom(6), // 消息id 随机数
      method: 'openfile',
      data: {
        filePath: row.filePath,
      },
    });
    console.log('cppResponse', response);
    if (response.code !== 0) {
      shareError(response.data.status, response.data.status);
    }
  };
  const columns = useMemo(() => {
    console.log(selectedFileList)

    const hasInitFiles = selectedFileList.filter((item: any) => item.status === 'init').length > 0;
    return getColumns(false, config).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              {!hasInitFiles && <span>进度</span>}
              {hasInitFiles && (
                <Button
                  type="primary"
                  size="small"
                  ghost
                  onClick={() => {
                    const map = { ...selectedFileMap };
                    selectedFileList.forEach((item: any) => {
                      if (item.status === 'init') {
                        delete map[item.id];
                      }
                    });
                    setSelectedFileMap(map);
                    setSelectedFileList(Object.values(map));
                  }}
                >
                  全部取消
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          let element;
          if (row._status === 'pause') {
            element = <span className={styles.pause}>暂停上传</span>;
          } else if (row.status === 'abort') {
            element = <span className={styles.abort}>终止上传</span>;
          } else if (row.status === 'success') {
            element = <span className={styles.success}>已完成</span>;
          } else if (row.status === 'loading') {
            element = <span className={styles.uploading}>正在上传</span>;
          } else if (row.status === 'waiting') {
            element = <span className={styles.waiting}>等待中</span>;
          } else if (row.status === 'error') {
            element = <span className={styles.error}>上传错误</span>;
          } else {
            element = (
              <Space>
                <Button
                  ghost
                  type="primary"
                  size="small"
                  onClick={() => {
                    const map = { ...selectedFileMap };
                    delete map[row.id];
                    setSelectedFileMap(map);
                    setSelectedFileList(Object.values(map));
                  }}
                >
                  取消
                </Button>
                <Button style={{ paddingLeft: 0, paddingRight: 0 }} type="link" size="small" onClick={()=>{
                  scanBtn(row)
                }}>
                  浏览
                </Button>
              </Space>
            );
          }
          return element;
        };
      }
      return item;
    });
  }, [selectedFileList, selectedFileMap]);

  return (
    <div className={styles.list}>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={selectedFileList}
        rowKey={'id'}
        scroll={{ y: 491 }}
        pagination={false}
      />
    </div>
  );
};

export default Component;
