.record-content {
  .header {
    height: 72px;
    padding: 18.47px 19.7px;
    background: rgba(255, 255, 255, 0.5);
    border-width: 1px 1px 0px 1px;
    border-style: solid;
    border-color: #ffffff;

    .header-span {
      // width: 120px;
      height: 35px;
      font-size: 20px;
      font-weight: 500;
      line-height: 35px;
      opacity: 1;
    }

    .header-button {
      // width: 52px;
      height: 32px;
      border-radius: 4px;
      opacity: 1;
      padding: 5px 12px;
      background: #ffffff;
      border: 1px solid #3d5afe;
    }
  }
  .content {
    height: 928px;
    .animate {
      width: 100%;
      height: 255px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      .bar {
        background-color: #d8d8d8;
        width: 10px;
        border-radius: 5px;
      }
    }
    .duration {
      height: 36px;
      opacity: 1;
      font-size: 26px;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0em;
      color: rgba(0, 0, 0, 0.85);
    }

    .images {
      width: 80px;
      height: 80px;
      cursor: pointer;
    }

    .con-button {
      height: 28px;
      font-size: 20px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.9);
    }
  }
  :global {
    .ant-modal-content {
      padding: 0;
      margin: 0;
      height: 1000px;
      border-radius: 10px;
      opacity: 1;
      background: rgba(255, 255, 255, 0.85);
      border: 1px solid #ffffff;
      backdrop-filter: blur(38.18px);
      box-shadow: 0px 24.63px 49.26px 0px rgba(0, 0, 0, 0.1);
    }
  }
}
