import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create, createJSONStorage, devtools } from '@/store';
// 来自于数据银行、回收站、共享天地、编辑器
type FormModule = 'dataBank' | 'recycle' | 'share' | 'editor' | 'workReview' | 'im' | '';

// 传递的额外参数 如共享天地的groupId
interface FromModuleQuery {
  groupId?: string;
  groupNameWithCount?: string;
  backPath?: string;
  articleTitle?: string;
  currentModuleFlag?: string; // 区分当前在那个模块；是在共享天地获音频播放器
  isSourceFromNetWorld?: boolean; // 区分是否在网络世界
}
interface WorkReviewQuery {
  realName?: string;
  username?: string;
  userId?: string;
  backPath?: string;
  email?: string;
}
interface State {
  shareUserList: Record<string, any>[];
  shareGroupList: Record<string, any>[];
  shareFileList: Record<string, any>[];
}

interface SetState {
  setShareUserList: Setter;
  setShareGroupList: Setter;
  setShareFileList: Setter;
}

const useDesktopFileUploadShareStore = autoUseShallow<State, SetState>(
  create(
    devtools(
      autoCreateSetters<State>({
        shareUserList: [],
        shareGroupList: [],
        shareFileList: [],
      }),
      { name: 'from-module', storage: createJSONStorage(() => sessionStorage) },
    ),
  ),
);
export default useDesktopFileUploadShareStore;
