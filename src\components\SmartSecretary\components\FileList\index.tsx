/**
 * 一键截图，图片收藏，网页编辑
 */
import { searchConditionAll } from '@/api/library';
import useJavaWebSocketStore from '@/store/useJavaWebSocketStore.ts';
import usesmartSecretaryActiveTab from '@/store/usesmartSecretaryActiveTab';
import { FC, useEffect, useRef, useState } from 'react';
import styles from '../Library/index.module.less';
import SecretaryLibraryList from '../Library/list';

interface FileListType {
  fileFormatType?: string;
  source?: string;
}
const FileList: FC<FileListType> = ({ fileFormatType, source }) => {
  const [libraryData, setLibraryData] = useState<any>([]);
  const [hasMore, setHasMore] = useState(true);
  const [pageNo, setPageNo] = useState({ current: 1 });
  const [response] = useJavaWebSocketStore((state) => [state.response]);
  const pageSize = 30;
  const fetchList = () => {
    /**
     * source
     * 一键截图 - 20
     * 图片收藏 - 22
     * 网页编辑 - 8
     */
    const params: any = { pageNo: pageNo.current, pageSize, source: [], fileFormatType: [] }; // source=截图
    source && (params.source = [source]);
    fileFormatType && (params.fileFormatType = [fileFormatType]);
    console.log('请求列表', params);
    searchConditionAll(params).then(({ data }: any) => {
      pageNo.current === 1
        ? setLibraryData(data.list)
        : setLibraryData([...libraryData, ...data.list]);
      console.log('还有更多', libraryData.length + data.list.length >= data.total);
      if (libraryData.length + data.list.length >= data.total) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
    });
  };
  const fetchListNext = () => {
    console.log('触发页码+1');
    setPageNo((pageNo) => ({ current: ++pageNo.current }));
  };

  useEffect(() => {
    fetchList();
  }, [fileFormatType, source, pageNo]);

  useEffect(() => {
    console.log(response.noticeType);
    // 收到推送
    if (response.noticeType === 'LIBRARY') {
      const containerDiv = document.querySelector('.secretaryLibraryWrap');
        if(containerDiv){
          containerDiv.children[0].scrollTop = 0;
        }
      
      setHasMore(true);
      setLibraryData([]);
      setPageNo({ current: 1 });
    }
  }, [response]);

  return (
    <div className={styles.secretaryLibraryWrap}>
      <SecretaryLibraryList
        list={libraryData}
        hasMore={hasMore}
        fetchListNext={fetchListNext}
        minusHeigh={120}
      />
    </div>
  );
};

export default FileList;
