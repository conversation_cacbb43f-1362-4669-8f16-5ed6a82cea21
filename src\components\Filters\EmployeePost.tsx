import MenuFilter from './MenuFilter';
import { getPostPage } from '@/api/account/privilege';
import { useEffect, useState } from 'react';

const Component = ({ setSelectedKeys, selectedKeys, confirm, close, clearFilters }: any) => {
  const [items, setItems] = useState([]);
  const toggleItem = (key: string) => {
    if (selectedKeys.includes(key)) {
      setSelectedKeys(selectedKeys.filter((k: string) => k !== key));
    } else {
      setSelectedKeys([...selectedKeys, key]);
    }
  };
  const submit = (type: string) => {
    if (type === 'reset') {
      clearFilters();
    } else if (type === 'ok') {
      confirm();
      close();
    }
  };
  useEffect(() => {
    getPostPage({ pageNo: 1, pageSize: 1000 }).then(({ data }: any) => {
      if (data.list) {
        setItems(
          data.list.map((item: any) => {
            return {
              key: item.id,
              label: item.name,
            };
          }),
        );
      }
    });
  }, []);

  return (
    <MenuFilter items={items} selectedKeys={selectedKeys} onSelect={toggleItem} onSubmit={submit} />
  );
};

export default Component;
