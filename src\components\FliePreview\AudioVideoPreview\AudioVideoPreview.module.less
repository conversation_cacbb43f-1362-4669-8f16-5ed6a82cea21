
.AudioVideoPreviewContent {
  width: 100%;
  height: 738px;
  :global {
    .xgplayer-controls,
    .xgplayer-error {
      display: none !important;
    }
    .xgplayer.xgplayer-inactive {
      cursor: pointer;
    }
    .xgplayer{
      background-color: transparent;
    }
  }
  .AudioVideoBox {
    width: 100%;
    height:
    calc(100% - 90px);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    .videoBoxRander,.SonicDiagramBox{
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.85);
    }
  }
  .playerBox {
    width: 100%;
    height: 90px;
  }
}
.AudioVideoPreviewContentFull{
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000000;
  background-color: #333;
}
.AudioVideoPreviewTitle {
  width: 100%;
  height: 62px;
  display: flex;
  align-items: center;
  padding-left: 18px;
  font-size: 14px;
  color: #333;
 pointer-events: none;
  .title{
    font-size: 20px;
  }
  .cancleInfo {
    display: flex;
    align-items: center;
    & > div {
      height: 23px;
      border-radius: 0px 4px 0px 4px;
      color: #FB8C00;
      /* 自动布局 */
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 1.5px 4px;
      background: rgba(255, 183, 77, 0.1);
      box-sizing: border-box;
      /* 橙色-Orange/Orange-600 */
      border: 1px solid #fb8c00;
      margin: 0 10px;
      span{
        color: #F4511E;
      }
    }
  }
}
