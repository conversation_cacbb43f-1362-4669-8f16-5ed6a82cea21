import {
  AuthCommmonReq,
  JobcontentReq,
  PrivilegeDetailReq,
  PrivilegeReq,
  UsernameReq,
} from '@/pages/Auth/typing';
import request from '../index';

/**
 * 获取权限列表
 * @param data
 * @returns
 */
export const getPrivilegeList = (data: PrivilegeReq) => {
  return request.post({
    url: '/web-api/ee/account/privilege/config/page',
    data,
  });
};

/**
 * 保存权限
 * @param data
 * @returns
 */
export const savePrivilege = (data: any) => {
  return request.post({
    url: '/web-api/ee/account/privilege/config/save',
    data,
  });
};
/**
 * 获取权限详情
 * @param data
 * @returns
 */
export const getPrivilegeDetail = (data: PrivilegeDetailReq) => {
  return request.post({
    url: '/web-api/ee/account/privilege/config/get',
    data,
  });
};

/**
 * 获取办公模式
 * @param data
 * @returns
 */
export const getModeList = () => {
  return request.get({
    url: '/web-api/ee/account/privilege/workMode/getModeList',
  });
};

/**
 * 获得部门分页
 * @param data
 * @returns
 */
export const getDeptPage = (data: AuthCommmonReq) => {
  return request.post({
    url: '/web-api/ee/account/manage/dept/page',
    data,
  });
};

/**
 * 获取岗位分页
 * @param data
 * @returns
 */
export const getPostPage = (data: AuthCommmonReq) => {
  return request.post({
    url: '/web-api/ee/account/manage/post/page',
    data,
  });
};

/**
 * 获得工作内容分页
 * @param data
 * @returns
 */
export const getJobcontentPage = (data: JobcontentReq) => {
  return request.post({
    url: '/web-api/ee/account/manage/jobContent/page',
    data,
  });
};

/**
 * 获取直接上级
 * @param data
 * @returns
 */
export const getHrPage = (data: JobcontentReq) => {
  return request.post({
    url: '/web-api/hr/hr/pageBind',
    data,
  });
};

/**
 * 获取账号
 * @param data
 * @returns
 */
export const getEeUserPage = (data: UsernameReq) => {
  return request.post({
    url: '/web-api/ee/account/user/manage/getEeUserPage',
    data,
  });
};
