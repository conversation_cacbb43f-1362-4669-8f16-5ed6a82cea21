import { TenantType } from '@/const';
import Permission from '@/const/permission';
import useUserStore from '@/store/useUserStore';
import request from '../index';
//查询第三方应用安装软件查询接口分页展示
export const selectInstallPage = (data: any) => {
  return request.post({
    url: '/web-api/desktop/softwareInstall/installedPage',
    data,
  });
};
// 获得通讯录联系人群分页
export const queryGroupByPage = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/group/getGroupPageByType',
    data,
  });
};
//选择性删除群
export const deleteGroupByIds = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/deleteOrCancelByIds',
    data,
  });
};
//删除所有群
export const deleteGroupAll = (data: any) => {
  return request.post({
    url: '/web-api/contacts/contact/deleteOrCancelAll',
    data,
  });
};

export interface MenuRes {
  userId: string;
  permission: keyof Permission;
  clickCount: number;
  sort: number;
}
/**
 * 点击菜单
 * @param data
 * @returns
 */
export const operateMenu = (permission: Permission) => {
  return request.get({
    url: '/web-api/account/manage/userMenuOperate/save',
    params: {
      permission: permission,
    },
  });
};

/**
 * 获取菜单排序
 * @param data
 * @returns
 */
export const getUserMenuOperate = () => {
  return request.get<Response<MenuRes>>({
    url: '/web-api/account/manage/getUserMenuOperate',
  });
};
//获取常用网站（兼容企业版、综合版）
export const selectWebSites = (data: any) => {
  const queryUrl =
    useUserStore.getState().tenantType === TenantType.ENTERPRISE
      ? '/web-api/networld/white-website/commonWebsites'
      : '/web-api/networld/website/getPage';
  return request.post({
    url: queryUrl,
    data,
  });
};
