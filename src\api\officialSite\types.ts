// 类型编码(228-员工手册 229-企业简介 230-今日新闻外 231-行业动态 232-经典视频 233-大事记 " +
// "234-企业荣誉 235-员工风采 236-公益事业 237-参观交流 238-领导关怀 239-通知文件 240-智慧考试 " +
// "241-招贤纳士 242-投诉建议 243-今日新闻内 244-党建引领)")

export enum ArticleType {
  '员工手册' = '228',
  '企业简介' = '229',
  '今日新闻外' = '230',
  '行业动态' = '231',
  '经典视频' = '232',
  '大事记' = '233',
  '企业荣誉' = '234',
  '员工风采' = '235',
  '公益事业' = '236',
  '参观交流' = '237',
  '领导关怀' = '238',
  '通知文件' = '239',
  '智慧考试' = '240',
  '招贤纳士' = '241',
  '投诉建议' = '242',
  '今日新闻内' = '243',
  '党建引领' = '244',
}

/** 文章列表 */
export interface GetArticleListParam {
  typeCode: ArticleType;
  approvalStatus?: string;
  pageNo: number | string;
  pageSize: number | string;
  keyWords?: Array<string>;
}
export interface GetArticleListRes {
  list: {
    /*id */
    id: string;

    title: string;

    /*类型编码 */
    typeCode: string;

    /*可替换主图地址 */
    visitableMainImage: string;

    /*文章缩略内容 */
    shortContent: string;

    /*可访问内容 */
    visitableContent: string;

    /*作者 */
    author: string;

    /*审批状态 1待审批 2已审批 3退回 4发起人 */
    // approvalStatus: number;

    /*审批时间 */
    approvalTime: string;

    /*租户id */
    // tenantId: number;
  }[];

  /*总量 */
  total: number;
}

/** 文章详情 */
export interface DetailByIdRes {
  /*id */
  id: string;

  /*类型编码 */
  typeCode: string;

  /*标题 */
  title: string;

  /*可替换主图地址 */
  visitableMainImage: string;

  /*文章缩略内容 */
  shortContent: string;

  /*可访问内容 */
  visitableContent: string;

  /*附加字段 */
  ext: string;

  /*作者 */
  author: string;

  /*审批状态 1待审批 2已审批 3退回 4发起人 */
  approvalStatus: number;

  /*审批时间 */
  approvalTime: string;

  /*租户id */
  tenantId: number;

  createTime: string;
}

/**保存文章 */
export interface SaveParams {
  /*类型编码 */
  typeCode: string;

  title: string;

  /*文章主图地址 */
  mainImage?: string;

  /*文章缩略内容 */
  shortContent?: string;

  /*内容 */
  content: string;

  /*附加字段 */
  ext?: string;

  /*作者 */
  author?: string;

  /*租户id */
  tenantId?: number;
}
