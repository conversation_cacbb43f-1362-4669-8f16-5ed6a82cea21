import { userCol } from '@/components/MailBook/columns';
import { getWidth } from '@/utils/common';
import { Button, Table } from 'antd';
import { FC, useContext } from 'react';
import styles from '../index.module.less';
import ShareContext from '../ShareContext';

interface UserProps {
  dataSource: any[] | undefined;
}

const Component: FC<UserProps> = ({ dataSource }) => {
  const { setUser } = useContext(ShareContext);

  const removeUser = (record: any) => {
    (setUser as any)(dataSource?.filter((item) => item.id !== record.id));
  };

  const columns = [
    ...userCol,
    {
      title: '操作',
      key: 'action',
      width: getWidth(80),
      render: (_: any, record: any) => (
        <Button
          type="primary"
          size={'small'}
          ghost
          onClick={() => {
            removeUser(record);
          }}
        >
          取消
        </Button>
      ),
    },
  ];

  return (
    <div className={styles.list}>
      <div className={styles.title}>已选分享用户({(dataSource || []).length})</div>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        scroll={{ x: 700, y: 163 }}
        pagination={false}
      />
    </div>
  );
};

export default Component;
