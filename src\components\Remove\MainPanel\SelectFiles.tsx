import { getColumns } from '../SelectFiles/columns';
import { Button, Space, Table } from 'antd';
import { useMemo, useContext, useRef } from 'react';
import styles from './index.module.less';
import Context from '../Context';
import FilePreview from '@/components/FliePreview';
import type { FilePreviewAPI } from '@/components/FliePreview';

const Component = () => {
  const filePreviewRef = useRef<FilePreviewAPI>();
  const { useMainPanelCtxStore, config } = useContext(Context);
  const [selectedFileList, setSelectedFileList, selectedFileMap, setSelectedFileMap] =
    useMainPanelCtxStore!((state) => [
      state.selectedFileList,
      state.setSelectedFileList,
      state.selectedFileMap,
      state.setSelectedFileMap,
    ]);
  const columns = useMemo(() => {
    return getColumns(false, config).map((item: any) => {
      if (item.dataIndex === 'actions') {
        item.title = () => {
          return (
            <Space>
              {selectedFileList.length === 0 && <span>操作</span>}
              {selectedFileList.length > 0 && (
                <Button
                  type="primary"
                  size="small"
                  ghost
                  onClick={() => {
                    setSelectedFileMap({});
                    setSelectedFileList([]);
                  }}
                >
                  全部取消
                </Button>
              )}
            </Space>
          );
        };
        item.render = (value: any, row: any) => {
          return (
            <Space>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  preview(row);
                }}
              >
                浏览
              </Button>
              <Button
                ghost
                type="primary"
                size="small"
                onClick={() => {
                  const map = { ...selectedFileMap };
                  delete map[row.id];
                  setSelectedFileMap(map);
                  setSelectedFileList(Object.values(map));
                }}
              >
                取消
              </Button>
            </Space>
          );
        };
      }
      return item;
    });
  }, [selectedFileList, selectedFileMap]);
  const preview = (row: any) => {
    filePreviewRef.current?.open({ ...row });
  };

  return (
    <div className={styles.list}>
      <Table
        virtual
        size="small"
        className={styles.table}
        columns={columns}
        dataSource={selectedFileList}
        rowKey={'id'}
        scroll={{ y: 491 }}
        pagination={false}
      />
      <FilePreview ref={filePreviewRef} />
    </div>
  );
};

export default Component;
