.scrollableDiv {
  height: calc(100% - 114px);
  overflow: auto;
  // padding: 0 16px;
}
.todoListContainer {
  width: 100%;
}
.todoList {
  background: #fff;
  height: calc(100%-88px);
  :global {
    .ant-list-item-meta-title {
      margin-bottom: 0;
    }
  }
  .listContentItem {
    padding: 16px;
  }
  .leftContent {
    .webIcon {
      width: 50px;
      height: 50px;
    }
  }
  .titleLine {
    display: flex;
    align-items: baseline;
    .title {
      font-size: 17px;
      font-weight: 500;
      color: #292929;
      overflow: hidden; /* 超出部分隐藏 */
      display: -webkit-box; /* 使用 WebKit 的弹性盒模型 */
      -webkit-line-clamp: 1; /* 限制在一个块元素显示的文本的行数 */
      -webkit-box-orient: vertical;
      flex: 1;
    }
  }
  .contentLine {
    display: flex;
    align-items: end;
  }

  .content {
    flex: 1;
    .contentText {
      width: 210px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: keep-all;
    }
  }
  .unread {
    color: #fff;
    background: #e53935;
    border-radius: 50%;
    font-size: 13px;
    padding: 0 2px;
  }
  .cirleUnread {
    color: #fff;
    background: #e53935;
    border-radius: 50%;
    font-size: 13px;
    width: 20px;
    line-height: 20px;
    text-align: center;
  }
  .time {
    margin: -1px 0 0 10px;
    color: rgba(0, 0, 0, 0.25);
  }
  .typeTitle {
    padding: 0px 4px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 12px;
  }
}
