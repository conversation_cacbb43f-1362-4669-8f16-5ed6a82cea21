import request from '../index';

// 获得用户登录记录分页
export const loginLog = (data: any) => {
  return request.post({
    url: '/web-api/account/loginReview/loginLog/page',
    data,
  });
};

// 创建系统用户浏览记录
export const createBrowseLog = (data: any) => {
  return request.post({
    url: '/web-api/account/loginReview/browseLog/create',
    data,
  });
};

// 获得浏览记录分页
export const getBrowseLog = (data: any) => {
  return request.post({
    url: '/web-api/account/loginReview/browseLog/page',
    data,
  });
};

// 创建系统用户软件打开记录
export const createAppLoginLog = (data: any) => {
  return request.post({
    url: '/web-api/account/loginReview/appLoginLog/create',
    data,
  });
};

// 获得软件打开记录分页
export const getAppLoginLog = (data: any) => {
  return request.post({
    url: '/web-api/account/loginReview/appLoginLog/page',
    data,
  });
};

// 创建系统用户打印记录
export const createPrintLog = (data: any) => {
  return request.post({
    url: '/web-api/account/loginReview/printLog/create',
    data,
  });
};
// 获得打印记录分页
export const getPrintLog = (data: any) => {
  return request.post({
    url: '/web-api/account/loginReview/printLog/page',
    data,
  });
};

// 创建系统用户下载记录
export const createDownloadLog = (data: any) => {
  return request.post({
    url: '/web-api/account/loginReview/downloadLog/create',
    data,
  });
};
// 获得下载记录分页
export const getDownloadLog = (data: any) => {
  return request.post({
    url: '/web-api/account/loginReview/downloadLog/page',
    data,
  });
};

// 删除登录记录
export const deleteLoginLog = (data: any) => {
  return request.put({
    url: '/web-api/account/loginReview/loginLog/delete',
    data,
  });
};
// 删除浏览记录
export const deleteBrowseLog = (data: any) => {
  return request.put({
    url: '/web-api/account/loginReview/browseLog/delete',
    data,
  });
};
// 删除下载记录
export const deleteDownloadLog = (data: any) => {
  return request.put({
    url: '/web-api/account/loginReview/downloadLog/delete',
    data,
  });
};
// 删除打印记录
export const deletePrintLog = (data: any) => {
  return request.put({
    url: '/web-api/account/loginReview/printLog/delete',
    data,
  });
};
// 删除软件打开记录
export const deleteAppLoginLog = (data: any) => {
  return request.put({
    url: '/web-api/account/loginReview/appLoginLog/delete',
    data,
  });
};
