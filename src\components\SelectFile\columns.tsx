import { getFileFormatTypeFilterProps, getSourceFilterProps } from '@/components/Filters';
import useFromModuleStore from '@/store/useFromModuleStore';
import { getWidth } from '@/utils/common';

export interface FileRecord {
  id: string;
  shareRealName: string;
  title: string;
  source: string;
  sourceName: string;
  fileSize: number;
  fileFormatType: string;
  fileFormatTypeName: string;
  visitPath?: string;
  [prop: string]: any;
}

export type ColumnField =
  | 'index'
  | 'shareRealName'
  | 'title'
  | 'source'
  | 'fileSize'
  | 'fileFormatType'
  | 'actions';

export const getColumns = (
  hasFilters: boolean,
  columnFields: ColumnField[] = [
    'index',
    'shareRealName',
    'title',
    'fileFormatType',
    'source',
    'actions',
  ],
) => {
  const { fromModule, fromModuleQuery } = useFromModuleStore.getState();
  const columns: any[] = [];
  columnFields.forEach((field) => {
    switch (field) {
      case 'index':
        columns.push({
          title: '序号',
          dataIndex: 'index',
          key: 'index',
          width: getWidth(40),
          render: (value: any, row: any, index: number) => index + 1,
        });
        break;
      case 'shareRealName':
        columns.push({
          title: '姓名',
          dataIndex: 'shareRealName',
          key: 'shareRealName',
          width: getWidth(60),
          ellipsis: true,
        });
        break;
      case 'title':
        columns.push({
          title: '标题',
          dataIndex: 'title',
          key: 'title',
          width: getWidth(100),
          ellipsis: true,
        });
        break;
      case 'fileFormatType':
        columns.push({
          title: '文件格式',
          dataIndex: 'fileFormatType',
          key: 'fileFormatType',
          width: getWidth(90),
          ellipsis: true,
          ...(hasFilters && !(fromModule && fromModuleQuery.currentModuleFlag)
            ? getFileFormatTypeFilterProps()
            : {}),
          render: (value: any, row: any) => {
            return row.fileFormatTypeName;
          },
        });
        break;
      case 'source':
        columns.push({
          title: '文件来源',
          dataIndex: 'source',
          key: 'source',
          width: getWidth(90),
          ...(hasFilters ? getSourceFilterProps() : {}),
          render: (value: any, row: any) => {
            return row.sourceName;
          },
        });
        break;

      case 'actions':
        columns.push({
          title: '操作',
          dataIndex: 'actions',
          key: 'actions',
          width: getWidth(110),
        });
        break;
    }
  });

  return columns;
};
