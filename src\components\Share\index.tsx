import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import type { Config } from './Context';
import Context, {
  createUseMainPanelCtxStore,
  createUseSelectFilesCtxStore,
  createUseShareFoldersCtxStore,
  createUseUsersCtxStore,
} from './Context';
import MainPanel from './MainPanel';
import SelectFiles from './SelectFiles';
import ShareFolders from './ShareFolders';
import Users from './Users';
export interface Props {
  config: Config;
}

const Component = forwardRef(({ config = { module: 'library' } }: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      setMainPanelOpen(true);
    },
  }));
  const [useMainPanelCtxStore] = useState(() => createUseMainPanelCtxStore({ ...config }));
  const [useUsersCtxStore] = useState(() => createUseUsersCtxStore({ ...config }));
  const [useShareFoldersCtxStore] = useState(() => createUseShareFoldersCtxStore({ ...config }));
  const [useSelectFilesCtxStore] = useState(() => createUseSelectFilesCtxStore({ ...config }));
  const [setMainPanelOpen] = useMainPanelCtxStore((state) => [state.setMainPanelOpen]);
  const [setUsersOpen] = useUsersCtxStore((state) => [state.setUsersOpen]);
  const [setShareFoldersOpen] = useShareFoldersCtxStore((state) => [state.setShareFoldersOpen]);
  const [setSelectFilesOpen] = useSelectFilesCtxStore((state) => [state.setSelectFilesOpen]);
  const [modalType, setModalType] = useState({ current: '' });
  const toolBarClick = (type: string) => {
    setModalType({ current: type });
    switch (type) {
      case 'users':
        setShareFoldersOpen(false);
        setSelectFilesOpen(false);
        break;
      case 'shareFolders':
        setUsersOpen(false);
        setSelectFilesOpen(false);
        break;
      case 'selectFiles':
        setUsersOpen(false);
        setShareFoldersOpen(false);
        break;
    }
  };
  useEffect(() => {
    switch (modalType.current) {
      case 'users':
        setUsersOpen(true);
        break;
      case 'shareFolders':
        setShareFoldersOpen(true);
        break;
      case 'selectFiles':
        setSelectFilesOpen(true);
        break;
    }
  }, [modalType]);

  return (
    <Context.Provider
      value={{
        useMainPanelCtxStore,
        useUsersCtxStore,
        useShareFoldersCtxStore,
        useSelectFilesCtxStore,
        config,
      }}
    >
      <MainPanel
        onCancel={() => {
          setUsersOpen(false);
          setShareFoldersOpen(false);
          setSelectFilesOpen(false);
          setMainPanelOpen(false);
        }}
        onToolBarClick={toolBarClick}
      />
      <Users />
      <ShareFolders />
      <SelectFiles />
    </Context.Provider>
  );
});

export default Component;
