import type { Setter } from '@/store';
import { autoCreateSetters, autoUseShallow, create } from '@/store';
import type { Config } from '../Context';

export interface State {
  mainPanelOpen: boolean;
  selectedFileList: any[];
  selectedFileMap: Record<string, any>;
}
export interface SetState {
  setMainPanelOpen: Setter;
  setSelectedFileList: Setter;
  setSelectedFileMap: Setter;
}

const createUseCtxStore = (config: Config) => {
  const useCtxStore = autoUseShallow<State, SetState>(
    create(
      autoCreateSetters<State>({
        mainPanelOpen: false,
        selectedFileList: [],
        selectedFileMap: {},
      }),
    ),
  );

  return useCtxStore;
};

export default createUseCtxStore;
