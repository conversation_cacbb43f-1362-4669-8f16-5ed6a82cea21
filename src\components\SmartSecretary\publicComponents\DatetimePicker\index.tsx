/**
 * 封装时间控件，以适配小秘书
 */

import { Popup } from '@/components/SmartSecretary/publicComponents/vantComponents';
import { FC, useState } from 'react';
import { DatetimePicker } from 'react-vant';

interface Props {
  title?: string;
  value: string;
  onConfirm: (val: any) => void;
  onClose: () => void;
  visible: boolean;
}

const YbDatetimePicker: FC<Props> = ({ title, value, onConfirm, visible, onClose }) => {
  const [dateVal, setDateVal] = useState(new Date(value));

  return (
    <Popup visible={visible} position="bottom" onClose={onClose}>
      <DatetimePicker
        title={title || '选择年月日'}
        type="date"
        minDate={new Date(2024, 12, 1)}
        maxDate={new Date(2025, 10, 1)}
        value={dateVal}
        onConfirm={onConfirm}
      />
    </Popup>
  );
};

export default YbDatetimePicker;
